import liveApi from 'api/live'
import webrtcMgr from 'common/js/webrtc-manager'

class ChannelVideo {
  constructor (instanceId) {
    var pc = new RTCPeerConnection({})
    pc.onconnectionstatechange = event => {
      if (pc.connectionState === 'disconnected') {
        console.log('disconnected')
        setTimeout(() => {
          this.reconnect(instanceId)
        }, 10000)
      }
    }
    this.sdpFailCount = 0 // 获取sdp失败次数
    this.connect(pc, instanceId)
    webrtcMgr.register(pc)
  }

  reconnect (instanceId) {
    webrtcMgr.destroyAll()
    var pc = new RTCPeerConnection({})
    pc.onconnectionstatechange = event => {
      if (pc.connectionState === 'disconnected') {
        setTimeout(() => { // 后台重启等待一会再重连
          this.reconnect(instanceId)
        }, 10000)
      }
    }
    this.sdpFailCount = 0 // 获取sdp失败次数
    this.connect(pc, instanceId)
    webrtcMgr.register(pc)
  }

  connect (pc, instanceId) {
    this.rtcChannel = pc.createDataChannel('sendChannel')
    this.rtcChannel.onmessage = event => {
      if (this.listener) {
        this.listener(event.data)
      }
    }
    pc.createOffer()
      .then(d => {
        pc.setLocalDescription(d, () => {
          this.getSdp(pc, instanceId)
        })
      })
  }

  send (message) {
    if (this.rtcChannel) {
      try {
        this.rtcChannel.send(JSON.stringify(message))
      } catch (error) {
        console.error('dataChannel send fail, msg:', message)
        console.error(error)
      }
    } else {
      console.error('dataChannel not exist')
    }
  }

  getSdp (pc, instanceId) {
    liveApi.getPollData('data', pc.localDescription, (message) => {
      if (message === '' && this.sdpFailCount < 5) {
        this.sdpFailCount++
        setTimeout(() => {
          this.getSdp(pc, instanceId)
        }, 500)
        return
      }
      try {
        pc.setRemoteDescription(new RTCSessionDescription(JSON.parse(atob(message.sdp))))
      } catch (e) {
        console.log('setRemoteDescription error:' + e)
      }
    }, () => {}, instanceId)
  }

  bind (fn) {
    this.listener = fn
  }
}

export default ChannelVideo
