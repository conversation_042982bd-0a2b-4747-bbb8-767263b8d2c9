<template>
  <div class="header-area">
    <div class="left-option flex-c-c">
      <h1 class="title">{{title}}</h1>
      <i class="el-icon-edit" @click="openTitleEditDialog()" v-show="roleType === 1"></i>
    </div>
    <div class="right-option">
      <span class="option-item" @click="openMainSetting" v-show="roleType === 1">
        <i class="icon icon-fount-setting" title="设置"></i>
        <span class="icon-text">设置</span>
      </span>
      <span class="icon-text" style="cursor: pointer" v-show="roleType === 2" @click="loginOut()">退出</span>
    </div>
    <el-dialog :title="'名称修改'" :visible.sync="showTitleEditDialog" width="574px" :close-on-click-modal="false" @close="showTitleEditDialog = false" append-to-body>
      <el-form :model="instanceForm" label-width="92px">
        <el-form-item label="名称：" prop="name">
          <el-input v-model.trim="instanceForm.name" :placeholder="'请输入名称'" size="small"></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <span class="g-option-btn-1" style="width: 84px" @click="confirmEdit()">确定</span>
        <span class="g-option-btn-2" style="width: 84px" @click="showTitleEditDialog = false">取消</span>
      </span>
    </el-dialog>
    <main-setting v-if="showMainSetting" @close="showMainSetting = false"></main-setting>
  </div>
</template>

<script>
import mainSetting from '@/components/instanceList/main-setting'
import {mapGetters} from 'vuex'
export default {
  data () {
    return {
      showTitleEditDialog: false,
      instanceForm: {
        name: ''
      },
      title: '',
      showMainSetting: false
    }
  },
  computed: {
  ...mapGetters([
    'roleType'
  ])
  },
  components: {mainSetting},

  methods: {
    confirmEdit () {
      if (!this.instanceForm.name) {
        return this.$message.warning('请输入名称')
      }
      const limitLength = 16
      if (this.instanceForm.name.length > limitLength) {
        return this.$message.warning('名称不能大于' + limitLength + '个')
      }
      this.$message.success('编辑成功')
      this.title = this.instanceForm.name
      this.showTitleEditDialog = false
      this.$_setLocalStorage('instance_list_page_title', this.title)
    },
    openTitleEditDialog () {
      this.instanceForm.name = this.title
      this.showTitleEditDialog = true
    },
    loginOut () {
      this.$confirm('确定退出吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        localStorage.removeItem('live_pwd')
        location.reload()
      })
    },
    getTitle () {
      let newTitle = this.$_getLocalStorage('instance_list_page_title')
      if (newTitle) {
        this.title = newTitle
      } else {
        this.title = '全时播控巡检'
      }
    },
    openMainSetting () {
      this.showMainSetting = true
    }
  },
  created () {
    this.getTitle()
  }
}
</script>

<style scoped lang="scss">
  .header-area {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 30px;
    box-sizing: border-box;
    .title {
      font-size: 26px;
    }
    .line {
      margin: 0 12px;
      display: inline-block;
      width: 1px;
      height: 26px;
      background: #515A6E;
    }
    .right-option {
      display: flex;
      align-items: center;
      .option-item {
        display: flex;
        align-items: center;
        cursor: pointer;
        &:hover {
          color: #FC4F08;
        }
      }
    }
    .text {
      font-size: 26px;
      font-family: Microsoft YaHei;
      font-weight: 400;
      color: #B2BFDE;
    }
    .icon-fount-setting {
      font-size: 23px;
    }
    .icon-text {
      margin-left: 6px;
      font-size: 18px;
      color: #B2BFDE;
      user-select: none;
    }
  }
  .left-option {
    cursor: pointer;
    &:hover {
      .el-icon-edit {
        display: inline-block;
      }
    }
    .el-icon-edit {
      display: none;
      margin-left: 10px;
      font-size: 18px;
      &:hover {
        color: #FC4F08;
      }
    }
  }
</style>
