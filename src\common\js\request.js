import axios from 'axios'
import qs from 'qs'

const ERR_OK = 0
let axiosInstance = axios.create({
  headers: {
    'Content-Type': 'application/json;charset=UTF-8'
  }
})
function getRequestMsg (code, msg) {
  if (code === -1) {
    return '请求失败'
  }
  if (code === -60006) {
    return '与现有节目冲突，请修改后添加'
  }
  if (code === -60008) {
    return '与现有节目冲突，请修改后添加'
  }
  if (code === -60011) {
    return '节目正在播放中，无法删除'
  }
  if (msg === '预览资源不足') {
    return '预览操作过于频繁，请稍后重试'
  }
  return msg
}
let instance = null
let handleMsg = function (msg, successCall, failCall, noFailTips) {
  let value = msg.value || msg.data || msg.Data
  let resultError = getRequestMsg(msg.code, msg.msg)
  if (msg.code === ERR_OK || msg.Code === ERR_OK) {
    if (successCall) return successCall(value)
    return instance.$message.success('操作成功')
  } else {
    if (noFailTips) return
    if (failCall !== undefined) return failCall(msg.code, resultError, value)
    if (instance) {
      if (msg.msg === '实例升级中') {
        instance.$message.warning(resultError)
      } else {
        instance.$message.error(resultError)
      }
      return
    }
    return alert(value)
  }
}
export default {
  bind: function (vue) {
    instance = vue
  },
  getText: function (url, successCall, failCall) {
    axios.get(url,
      { transformResponse: res => {
          successCall(res)
        }
      })
  },
  requestGet: function (url, params, successCall, failCall, noFailTips) {
    axios.get(url, {params}).then((res) => {
      handleMsg(res.data, successCall, failCall, noFailTips)
    }).catch(() => {
      if (noFailTips) return
      instance ? instance.$message.error('服务器忙，请稍后再试') : window.alert('服务器忙，请稍后再试')
    })
  },
  request: function (url, params, successCall, failCall, noFailTips) {
    axios.post(url, qs.stringify(params)).then((res) => {
      handleMsg(res.data, successCall, failCall, noFailTips)
    }).catch(() => {
      if (noFailTips) return
      instance ? instance.$message.error('服务器忙，请稍后再试') : window.alert('服务器忙，请稍后再试')
    })
  },
  requestJson: function (url, params, successCall, failCall, noFailTips) {
    axiosInstance.post(url, JSON.stringify(params)).then((res) => {
      handleMsg(res.data, successCall, failCall, noFailTips)
    }).catch(() => {
      if (noFailTips) return
      instance ? instance.$message.error('服务器忙，请稍后再试') : window.alert('服务器忙，请稍后再试')
    })
  }
}
