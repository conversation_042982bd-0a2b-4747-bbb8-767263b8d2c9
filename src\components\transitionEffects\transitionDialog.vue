<template>
  <el-dialog :title="title" :visible.sync="visible" width="500px" top="15vh"
    append-to-body :close-on-click-modal="false" @close="close()">
    <Content />
    <span slot="footer" class="dialog-footer">
      <span class="g-option-btn-1" style="width: 84px" @click="confirm">确定</span>
      <span class="g-option-btn-2" style="width: 84px" @click="close">取消</span>
    </span>
  </el-dialog>
</template>

<script>
import Content from './index.vue'

export default {
  components: { Content },
  props: {
    title: {
      type: String,
      default: '转场特效'
    }
  },
  computed: {},
  data() {
    return {
      visible: true
    }
  },
  methods: {
    close() {
      this.$emit('close')
    },
    confirm() {
      this.$emit('confirm')
    }
  },
  created() {},
  mounted() {}
}
</script>

<style scoped lang="scss">

</style>
