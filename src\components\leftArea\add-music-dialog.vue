<template>
  <el-dialog title="添加音频流" width="535px" top="200px" :visible.sync="showDialog" :close-on-click-modal="false"
             @close="$emit('close')">
    <el-form label-position="left" label-width="110px">
      <el-form-item label="音频名称：" required>
        <el-input style="width: 380px;" size="small" :maxlength="255" v-model.trim="audioInfo.name" placeholder="请输入音频名称"></el-input>
      </el-form-item>
      <el-form-item label="音频流地址：" required>
        <el-input style="width: 380px;" size="small" v-model.trim="audioInfo.url" placeholder="请输入流音频流地址"></el-input>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button type="primary" @click="confirm" size="small">确 定</el-button>
      <el-button type="info" @click="$emit('close')" size="small">取 消</el-button>
    </span>
  </el-dialog>
</template>

<script>
  import cloudApi from 'api/cloud'

  export default {
    name: 'add-music-dialog',
    data () {
      return {
        showDialog: true,
        audioInfo: {
          name: '',
          url: ''
        }
      }
    },
    methods: {
      confirm () {
        if (!this.audioInfo.name) {
          return this.$message.warning('请填写音频名称')
        }
        if (!this.audioInfo.url) {
          return this.$message.warning('请填写音频流地址')
        }
        let param = {
          type: 1,
          fileName: this.audioInfo.name,
          visitUrl: this.audioInfo.url,
          duration: 0,
          format: '',
          fileSize: 0
        }
        cloudApi.addAudio(param, () => {
          this.$message.success('添加成功')
          this.$emit('uploadCallback')
          this.$emit('close')
        })
      }
    }
  }
</script>

<style scoped lang="scss">
 ::v-deep {
   .el-dialog__body {
     padding-bottom: 5px;
   }
 }
</style>
