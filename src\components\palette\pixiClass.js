import * as PIXI from 'pixi.js'
import getRotateFilter from './filter/rotateFilter'
import {MyColorMatrixFilter} from './filter/myColorMatrixFilter/MyColorMatrixFilter'
// import jsUtil from '@/common/js/jsUtil'

class PixiClass {
  constructor ({
                 display,
                 mosaic,
                 source,
                 pixiApp,
                 spriteContainer,
                 trackItemId,
                 tone
               }) {
    const pixiSource = new PIXI.BaseTexture(source, {resourceOptions: {autoPlay: false}})
    const texture = new PIXI.Texture(pixiSource)
    texture.frame = new PIXI.Rectangle(display.crop.x * pixiSource.width, display.crop.y * pixiSource.height, display.crop.w * pixiSource.width, display.crop.h * pixiSource.height)
    this.trackItemId = trackItemId
    this._pixiApp = pixiApp
    this._spriteContainer = spriteContainer
    this._sprite = new PIXI.Sprite(texture)
    // this._sprite.anchor.set(0.5)
    this._sprite.width = this.screenWidth
    this._sprite.height = this.screenHeight
    this._sprite.x = 0
    this._sprite.y = 0
    this.width = display.w
    this.height = display.h
    this.x = display.x
    this.y = display.y
    this.xFlip = display.xFlip ? display.xFlip : 0
    this.yFlip = display.yFlip ? display.yFlip : 0

    this._sprite.filterArea = pixiApp.screen
    this._sprite.filters = []
    this._sprite.alpha = 1
    this._angle = display.rotate || 0
    this.visible = true
    // 初始化滤镜
    this._rotateFilter = getRotateFilter() // 旋转滤镜
    this._colorMatrixFilter = new MyColorMatrixFilter() // 调色滤镜
    this._contrastFilter = new MyColorMatrixFilter() // 对比度滤镜
    this.addFilter(this._colorMatrixFilter)
    this.addFilter(this._contrastFilter)
    this.addFilter(this._rotateFilter, false, this._sprite.filters.length) // 旋转滤镜加在最后
    this._spriteContainer.addChild(this._sprite)

    tone && this.updateColor(tone)
  }

  get sprite () {
    return this._sprite
  }

  get screenWidth () {
    return this._pixiApp.screen.width
  }

  get screenHeight () {
    return this._pixiApp.screen.height
  }

  get width () {
    return this._width
  }

  set width (value) {
    this._width = value
  }

  get xFlip () {
    return this._xFlip
  }

  set xFlip (value) {
    this._xFlip = value
  }

  get yFlip () {
    return this._yFlip
  }

  set yFlip (value) {
    this._yFlip = value
  }

  get height () {
    return this._height
  }

  set height (value) {
    this._height = value
  }

  get x () {
    return this._x
  }

  set x (value) {
    this._x = value
  }

  get y () {
    return this._y
  }

  set y (value) {
    this._y = value
  }

  get zIndex () {
    return this._spriteContainer.zIndex
  }

  get angle () {
    return this._angle
  }

  set angle (value) {
    this._angle = value
  }

  get scale () {
    return this._sprite.scale
  }

  set scale (value) {
    this._sprite.scale.set(value.x, value.y)
  }

  get visible () {
    return this._sprite.visible
  }

  set visible (value) {
    this._sprite.visible = value
  }

  get alpha () {
    return this._sprite.alpha
  }

  set alpha (value) {
    this._sprite.alpha = value
  }

  get originDisplay () {
    return calculateContainParam(this._sprite.texture.width, this._sprite.texture.height, this.screenWidth, this.screenHeight)
  }

  remove () {
    this.stopRender()
    this._spriteContainer.removeChild(this._sprite)
    this.removeAllFilter()
    this._sprite.destroy()
  }

  resize () {
    // this._pixiApp
    this._sprite.width = this.screenWidth
    this._sprite.height = this.screenHeight
    this.updateFilterBasis()
  }

  startRender () {
    if (this.visible) {
      return
    }
    this.visible = true
    this.updateFrames()

    // this.changeAllFilterState(true)
  }

  stopRender () {
    if (!this.visible) {
      return
    }
    this.visible = false

    // this.changeAllFilterState(false)
  }

  updateFrames () {
    this._sprite.texture.update()
    // this._bg && this._bg.texture && this._bg.texture.update()
  }

  getAllFilter () {
    return {
      colorMatrixFilter: this._colorMatrixFilter,
      contrastFilter: this._contrastFilter,
      rotateFilter: this._rotateFilter
    }
  }

  updateView (option) {
    if (option) {
      this.width = option.width
      this.height = option.height
      this.x = option.x
      this.y = option.y
      this.angle = option.angle || 0
      this.alpha = option.alpha
      this.xFlip = option.xFlip ? 1 : 0
      this.yFlip = option.yFlip ? 1 : 0
    }
    this.updateFilterBasis()
  }
  updateColor (colorMatrix) {
    this._colorMatrixFilter.brightness(colorMatrix.brightness, false) // 曝光, 第2参数表示效果叠加
    this._colorMatrixFilter.saturate(colorMatrix.saturate, true) // 饱合度
    this._colorMatrixFilter.hue(colorMatrix.hue, true) // 色调,色相
    this._contrastFilter.contrast(colorMatrix.contrast, false) // 对比度
  }

  updateFilterBasis () {
    let allFilter = this.getAllFilter()
    for (let k in allFilter) {
      if (allFilter[k] && k === 'mosaic') {
        for (let mosaicId in allFilter['mosaic']) {
          let filter = allFilter['mosaic'][mosaicId]
          if (filter.myType === 1) {
            filter.screenArea = [this.screenWidth, this.screenHeight, 0, 0] // 整个画布大小和偏移量
            filter.area = [this.width, this.height, this.x, this.y] // 视频纹理区域，[宽，高，x轴偏移，y轴偏移]
          } else {
            filter.uniforms.screenArea = [this.screenWidth, this.screenHeight, 0, 0] // 整个画布大小和偏移量
            filter.uniforms.area = [this.width, this.height, this.x, this.y] // 视频纹理区域，[宽，高，x轴偏移，y轴偏移]
            filter.uniforms.pixel_size = [1.0 / this.screenWidth, 1.0 / this.screenHeight]
          }
        }
        continue
      }
      let isChangeOrigin = false
      this.setFilterbyBasics(allFilter[k], isChangeOrigin)
    }
  }

  addFilter (filter, isChangeOrigin, fromLastIndex) { // isChangeOrigin:原点是否为左下角 // fromLastIndex:从最后位置算插入位置,0为第一个
    this.setFilterbyBasics(filter, isChangeOrigin)
    if (!this._sprite.filters.find((item) => {
      return item === filter
    })) {
      // if (!this.visible) {
      //   filter.enabled = false
      // }
      if (fromLastIndex >= 0) {
        this._sprite.filters.splice(fromLastIndex, 0, filter)
      } else {
        this._sprite.filters.unshift(filter)
      }
    }
  }

  removeAllFilter () {
    let allFilter = this.getAllFilter()
    for (let k in allFilter) {
      if (allFilter[k] && k === 'mosaic') {
        for (let mosicId in allFilter['mosaic']) {
          this.removeMosaicFilter(mosicId)
        }
        continue
      }
      this.removeFilter(allFilter[k])
    }
  }

  removeFilter (filter) {
    if (this._sprite) {
      let index = this._sprite.filters.findIndex((item) => item === filter)
      index >= 0 && this._sprite.filters.splice(index, 1)
    }
  }

  setFilterbyBasics (filter, isChangeOrigin) {
    if (!filter) {
      return
    }
    try {
      filter.uniforms.screenArea = [this.screenWidth, this.screenHeight, 0, 0]
      if (isChangeOrigin) {
        filter.uniforms.area = [this.width, this.height, this.x, this.screenHeight - this.y - this.height]
      } else {
        filter.uniforms.area = [this.width, this.height, this.x, this.y]
      }
      filter.uniforms.flip = [this.xFlip, this.yFlip] // 翻转属性，水平翻转与垂直翻转
      filter.uniforms.angle = this.angle
      // 以下用于天气特效
      filter.uniforms.iResolution = [1.0, 1.0]
      filter.uniforms.iTime = 0
    } catch (e) {
      // console.error(e)
    }
  }
}

function calculateCoverParam (spriteWidth, spriteHeight, containerWidth, containerHeight) {
  if (spriteWidth === 0 || spriteHeight === 0) {
    return {
      x: 0,
      y: 0,
      width: containerWidth,
      height: containerHeight
    }
  }
  if (spriteHeight / spriteWidth > (containerHeight / containerWidth)) {
    var realH = spriteHeight * (containerWidth / spriteWidth)
    return {
      x: 0,
      y: (containerHeight - realH) / 2,
      width: containerWidth,
      height: realH
    }
  } else {
    var realW = spriteWidth * (containerHeight / spriteHeight)
    return {
      x: (containerWidth - realW) / 2,
      y: 0,
      width: realW,
      height: containerHeight
    }
  }
}

function calculateContainParam (spriteWidth, spriteHeight, containerWidth, containerHeight) {
  if (spriteWidth === 0 || spriteHeight === 0) {
    return {
      x: 0,
      y: 0,
      width: containerWidth,
      height: containerHeight
    }
  }
  if (spriteHeight / spriteWidth > (containerHeight / containerWidth)) {
    var realW = containerHeight * spriteWidth / spriteHeight
    return {
      x: (containerWidth - realW) / 2,
      y: 0,
      width: realW,
      height: containerHeight
    }
  } else {
    var realH = containerWidth * spriteHeight / spriteWidth
    return {
      x: 0,
      y: (containerHeight - realH) / 2,
      width: containerWidth,
      height: realH
    }
  }
}

export default PixiClass
