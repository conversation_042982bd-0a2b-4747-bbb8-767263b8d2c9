<template>
  <div class="video-renderer">
    <div v-if="component.previewImg" class="video-preview" :style="videoBackgroundStyle">
    </div>
    <div v-else class="preview-placeholder">
      <i class="el-icon-video-play"></i>
      <span>点播源</span>
    </div>
  </div>
</template>

<script>
export default {
  name: 'VideoRenderer',
  props: {
    component: {
      type: Object,
      required: true
    },
    isSelected: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    isVideoUrl() {
      if (!this.component.url) return false
      const videoExtensions = ['.mp4', '.mov', '.webm', '.avi', '.mkv']
      return videoExtensions.some(ext => this.component.url.toLowerCase().includes(ext))
    },
    videoBackgroundStyle() {
      // 将 objectFit 值转换为对应的 backgroundSize 值
      const getBackgroundSize = (objectFit) => {
        switch (objectFit) {
          case 'contain':
            return 'contain'
          case 'cover':
            return 'cover'
          case 'fill':
            return '100% 100%'
          default:
            return 'contain'
        }
      }

      const style = {
        width: '100%',
        height: '100%',
        backgroundImage: `url(${this.component.previewImg})`,
        backgroundSize: getBackgroundSize(this.component.objectFit),
        backgroundPosition: 'center',
        backgroundRepeat: 'no-repeat'
      }

      if (this.component.volume !== undefined) {
        // 视频音量控制可以在这里添加其他样式
      }

      return style
    }
  },
  methods: {
    handleVideoLoaded(event) {
      // 视频加载完成后的处理
      const video = event.target
      if (this.component.playType === 'loop') {
        video.loop = true
      }
      // 静音预览
      video.muted = true
    }
  }
}
</script>

<style lang="scss" scoped>
.video-renderer {
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.preview-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: rgba(103, 194, 58, 0.1);
  border: 1px dashed rgba(103, 194, 58, 0.3);
  color: #67c23a;
  font-size: 12px;

  i {
    font-size: 24px;
    margin-bottom: 4px;
  }
}

.video-preview {
  width: 100%;
  height: 100%;
  background: #000000;
  display: flex;
  align-items: center;
  justify-content: center;

  img {
    width: 100%;
    height: 100%;
  }
  video {
    max-width: 100%;
    max-height: 100%;
  }

  .video-info {
    text-align: center;
    color: #67c23a;
    i {
      font-size: 32px;
      margin-bottom: 8px;
      display: block;
    }

    span {
      display: block;
      font-size: 14px;
      margin-bottom: 4px;
    }

    .video-url {
      font-size: 10px;
      opacity: 0.8;
      max-width: 200px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      margin-bottom: 4px;
    }

    .play-mode {
      font-size: 10px;
      opacity: 0.6;
    }
  }
}
</style>
