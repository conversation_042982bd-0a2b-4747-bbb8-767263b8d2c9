<template>
  <div class="header-area">
    <div class="left-option flex-c-c">
      <img src="./logo.png" alt="">
      <h1 class="title" style="margin-left: 25px">{{title}}</h1>
      <i class="el-icon-edit" @click="openTitleEditDialog()" v-show="roleType !== 2"></i>
    </div>
    <div class="right-option">
      <template v-if="info">
        <div class="listen-item-date" v-if="info.isLiving">
          <span class="live" style="margin-right: 20px">LIVE</span>
          <div style="font-size: 18px" v-if="liveTime > 0">
            <span class="num">{{Math.floor(liveTime / 86400)}}</span>天
            <span class="num">{{Math.floor((liveTime % 86400) / 3600)}}</span>时
            <span class="num">{{Math.floor((liveTime % 86400) % 3600 / 60)}}</span>分
            <span class="num">{{Math.floor(((liveTime % 86400) % 3600) % 60)}}</span>秒
          </div>
          <div style="font-size: 20px" v-else>
            <span class="num">0</span>天
            <span class="num">0</span>时
            <span class="num">0</span>分
            <span class="num">0</span>秒
          </div>
        </div>
        <div class="listen-item-date" v-else>
          <span class="live" style="margin-right: 20px">LIVE</span>
          <div style="font-size: 18px">
            <span class="num">{{Math.floor(info.duration / 86400) || 0}}</span>天
            <span class="num">{{Math.floor((info.duration % 86400) / 3600) || 0}}</span>时
            <span class="num">{{Math.floor((info.duration % 86400) % 3600 / 60) || 0}}</span>分
            <span class="num">{{Math.floor(((info.duration % 86400) % 3600) % 60) || 0}}</span>秒
          </div>
        </div>
        <div style="display: flex;justify-content: flex-start;align-items: center;width: 300px;">
          <div class="listen-item" style="margin-top: 4px">
            <span>CPU</span>
            <strong style="color:#FF2222;font-size: 16px;padding-top: 2px">{{parseInt(Number(info.cpu)) || 0}}%</strong>
          </div>
          <div class="listen-item">
            <span>内存</span>
            <strong style="color:#F6AC0D;font-size: 16px;padding-top: 2px">{{parseInt(Number(info.memory)) || 0}}%</strong>
          </div>
          <div class="listen-item">
            <span>磁盘(剩余)</span>
            <strong style="color:#66A7FF;font-size: 16px;padding-top: 2px">{{(Number(info.disk) / (1024 * 1024 * 1024)).toFixed(2) || 0}}G</strong>
          </div>
          <div class="listen-item">
            <span>实时码率</span>
            <strong style="color:#32D711;font-size: 16px;padding-top: 2px">{{parseInt(Number(info.downstream) / 1024) || 0}}kb/s</strong>
          </div>
        </div>
      </template>
      <el-popover placement="bottom" trigger="hover" v-if="!$_isLocal">
        <qr-code :value="href" :options="{ size: 150 }"></qr-code>
        <span class="g-option-btn" slot="reference">手机监播</span>
      </el-popover>
      <span class="live-url-btn g-option-btn" :data-clipboard-text="copyPwd()" v-show="roleType !== 2">复制口令</span>
      <span class="cut-line"></span>
      <i class="icon icon-fount-setting" title="设置" @click="globalDialog" v-show="roleType !== 2"></i>
      <i class="icon icon-fount-full-screen" title="全屏" @click.stop="toggleFullScreen" v-if="!fullscreenFlag"></i>
      <i @click.stop="toggleFullScreen" class="icon" v-else>
        <svg-icon class="svg-icon1" icon-class="exit-fullscreen" style="font-size: 22px;margin-bottom: 1px;"></svg-icon>
      </i>
    </div>
    <live-setting v-if="liveSettingDialog" @close="liveSettingDialog=false" :typeId="typeId"></live-setting>
    <el-dialog :title="'实例名称修改'" :visible.sync="showTitleEditDialog" width="574px" :close-on-click-modal="false" @close="showTitleEditDialog = false" append-to-body>
      <el-form :model="instanceForm" label-width="92px">
        <el-form-item label="实例名称：" prop="name">
          <el-input v-model.trim="instanceForm.name" :placeholder="'请输入实例名称'" size="small"></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <span class="g-option-btn-1" style="width: 84px" @click="confirmEdit()">确定</span>
        <span class="g-option-btn-2" style="width: 84px" @click="showTitleEditDialog = false">取消</span>
      </span>
    </el-dialog>
  </div>
</template>

<script>
  import {mapGetters, mapMutations} from 'vuex'
  import Clipboard from 'clipboard'
  import qrCode from '@xkeshi/vue-qrcode'
  import liveSetting from '@/components/headerArea/liveSetting'
  import cloudApi from 'api/cloud'
  export default {
    name: 'header-area',
    components: {qrCode, liveSetting},
    props: {
      info: {
        type: Object,
        default: () => {}
      }
    },
    computed: {
      liveTime () {
        return (new Date().getTime() + this.localTimeDifference) / 1000 - this.info.durationBase
      },
      ...mapGetters([
        'roleType',
        'instanceId',
        'pwd',
        'output',
        'Emergency',
        'localTimeDifference'
      ])
    },
    data () {
      return {
        instanceForm: {
          name: ''
        },
        title: '全时播控',
        liveSettingDialog: false,
        capture: '',
        href: window.location.href,
        showWarningDialog: false,
        showGlobalDialog: false,
        globalForm: {
          delay: '',
          safeVideoUrl: '' // 紧急切换地址
        },
        mailList: [{email: ''}],
        mobileList: [{mobile: ''}],
        validatorMobile: true,
        validatorEmail: true,
        fullscreenFlag: false,
        timer: null,
        liveDuration: 0,
        typeId: 1,
        showTitleEditDialog: false,
        password: ''
      }
    },
    methods: {
      getPwd () {
        cloudApi.getInstancePass({}, res => {
          this.password = res.pwd
        })
      },
      backListPage () {
        this.$router.replace('/')
      },
      confirmEdit () {
        if (!this.instanceForm.name) {
          return this.$message.warning('请输入实例名称')
        }
        const limitLength = 16
        if (this.instanceForm.name.length > limitLength) {
          return this.$message.warning('实例名称不能大于' + limitLength + '个')
        }
        cloudApi.setInstanceInfo({name: this.instanceForm.name}, () => {
          this.$message.success('编辑成功')
          this.title = this.instanceForm.name
          this.showTitleEditDialog = false
        })
      },
      getInstanceInfo () {
        cloudApi.getInstanceInfo({}, res => {
          this.title = res.name
          this.setLocalStorageInfo({currentVolume: res.currentVolume, maxVolume: res.maxVolume})
        })
      },
      openTitleEditDialog () {
        this.instanceForm.name = this.title
        this.showTitleEditDialog = true
      },
      copyPwd () {
        let a =
          '全时播控链接：' + this.href +
          '\r'
        a += '口令：' + this.password + '\r'
        a += '温馨提示：请妥善保管口令及链接'
        return a
      },
      globalDialog () {
        this.typeId = 1
        this.liveSettingDialog = true
      },
      clipboard () {
        let clipboard = new Clipboard('.live-url-btn')
        clipboard.on('success', (e) => {
          this.$message.success('已复制')
        })
      },
      toggleFullScreen () {
        this.fullscreenFlag = !this.fullscreenFlag
        this.$fullscreen.toggle(this.$refs.studioInstance, {wrap: false})
      },
      ...mapMutations({
        setLocalStorageInfo: 'SET_LOCAL_STORAGE_INFO',
        setEmergency: 'SET_EMERGENCY',
        setOutput: 'SET_OUTPUT'
      })
    },
    mounted () {
      this.$bus.$on('openLiveSetting', typeId => {
        this.typeId = typeId
        this.liveSettingDialog = true
      })
    },
    created () {
      this.getPwd()
      this.getInstanceInfo()
      this.clipboard()
    }
  }
</script>

<style scoped lang="scss">
  .header-area {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-right: 30px;
    padding-left: 20px;
    box-sizing: border-box;
    .title {
      font-size: 26px;
    }
    .right-option {
      display: flex;
      align-items: center;
      .listen-item-date {
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-wrap: nowrap;
        margin-right: 200px;
        .live {
          font-size: 16px;
          color: #FF1818;
          border: #ff1818 solid 1px;
          text-align: center;
          box-sizing: border-box;
          height: 26px;
          line-height: 26px;
          padding: 0 10px;
        }
        .num {
          padding: 0 2px;
          font-size: 32px;
          font-weight: bold;
        }
      }
      .listen-item {
        display: flex;
        flex-direction: column;
        margin-right: 20px;
        font-size: 12px;
      }
      .cut-line {
        @include cut-line();
        color: #000;
      }
      .icon {
        padding: 4px 0;
        margin-left: 14px;
        font-size: 22px;
        cursor: pointer;
        &:hover {
          color: $color-theme;
        }
      }
    }
    .email-li {
      margin-bottom: 10px;
    }
    .capture-img {
      /deep/ .upload-img {
        flex: 0 0 160px;
        width: 160px;
        height: 90px;
      }
    }
    .svg-icon1 {
      fill: #B5BBCA;
      &:hover {
        fill: $color-theme;
      }
    }
  }
  .left-option {
    cursor: pointer;
    &:hover {
      .el-icon-edit {
        display: inline-block;
      }
    }
    .el-icon-edit {
      display: none;
      margin-left: 10px;
      font-size: 18px;
      &:hover {
        color: #FC4F08;
      }
    }
  }
</style>
