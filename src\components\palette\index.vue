<template>
  <el-dialog class="palette-dialog" title="调色" :visible.sync="showPalette" width="1340px" top="60px" :close-on-click-modal="false"
             :close-on-press-escape="false" @close="$emit('close')">
    <div class="preview-box" ref="previewBox" v-loading="videoLoading" element-loading-background="rgba(0, 0, 0, 0)" element-loading-text="加载中...." element-loading-spinner="loading-bg">
      <!-- <video id="preview" autoplay="true" muted="true" class="videoBg" ref="previewVideo" v-show="navTabs === 'lut'"></video> -->
      <div id="canvasBox" v-show="navTabs === 'toning'"></div>
      <canvas id="lutCanvas" ref="lutCanvas" v-show="navTabs === 'lut'" width="960" height="540"></canvas>
    </div>
    <div class="right-option">
      <el-tabs v-model="navTabs" @tab-click="onNavTabsClick" class="default-tab">
        <el-tab-pane label="调色" name="toning" class="editCgStyleTab">
          <toning :toneData="toneData" @change="onChangeToneData" @set="onSetPalette" @close="$emit('close')" v-if="navTabs==='toning'"></toning>
        </el-tab-pane>
        <el-tab-pane label="LUT" name="lut" class="editCgStyleTab">
          <lut :lutData="lutData" :videoDom="videoDom" :canvasDom="$refs.lutCanvas" @change="onChangeLutData" @set="onSetPalette" @close="$emit('close')" v-if="navTabs==='lut'"></lut>
        </el-tab-pane>
      </el-tabs>
    </div>
  </el-dialog>
</template>

<script>
  import {mapActions, mapGetters} from 'vuex'
  import 'cropperjs/src/css/cropper.scss'
  import ChannelVideo from 'common/js/channel-preview'
  import webrtcMgr from 'common/js/webrtc-manager'
  import toning from './toning'
  import lut from './lut'
  import pixiOperate from './pixiOperate'
  import cloudApi from 'api/cloud'

  const CROPBOX_WIDTH = 960
  const CROPBOX_HEIGHT = 540
  export default {
    name: 'palette',
    props: {
      programType: {
        type: String,
        default: ''
      },
      scheduleId: {
        type: Number,
        default: 0
      },
      manageId: {
        type: Number,
        default: 0
      },
      currentProgramItem: {
        type: Object,
        default: () => {}
      }
    },
    components: {toning, lut},
    computed: {
      ...mapGetters([
        'liveRatio',
        'liveWidth',
        'liveHeight',
        'audio',
        'defaultPic',
        'cgs'
      ])
    },
    data () {
      return {
        showPalette: true,
        navTabs: 'toning',
        video: null,
        videoLoading: false,
        contentWidth: 0,
        imgWidth: 0,
        imgHeight: 0,
        videoDom: null,
        toneData: {
          brightness: 1,
          saturate: 0,
          contrast: 0,
          hue: 0
        },
        lutData: {
          path: '',
          factor: 1
        },
        display: {
          h: 0,
          x: 0,
          y: 0,
          w: 0,
          scale: 1,
          crop: {
            x: 0, // 0%
            y: 0, // 0%
            w: 1, // 100%
            h: 1, // 100%
            ratio: null
          }
        },
        pixiItem: null
      }
    },
    methods: {
      onSetPalette ({type}) {
        let editProgram = this.currentProgramItem
        editProgram.video.channels.forEach(item => {
          if (item.idx === 0) {
            // lut和调色互斥
            item.lut = type === 'lut' ? this.getLut() : {path: '', factor: 1}
            item.colorTone = type === 'toning' ? this.getToneInfo(true) : {contrast: 0, saturation: 0, hue: 0, exposure: 0}
          }
        })
        if (this.manageId > 0) {
          let param = {
            id: this.manageId,
            program: JSON.stringify(editProgram)
          }
          cloudApi.setProgram(param, () => {
            this.$bus.$emit('refreshProgramManage')
            this.$emit('close')
          })
        } else if (this.scheduleId > 0) {
          let param = {
            id: this.scheduleId,
            program: JSON.stringify(editProgram)
          }
          cloudApi.uploadProgramArrangementById(param, () => {
            this.$bus.$emit('refreshProgramManage')
            this.$emit('close')
          })
        } else {
          let param = {
            programType: this.programType,
            program: editProgram
          }
          this.modProgram(param).then(() => {
            this.$emit('close')
          })
        }
      },
      onChangeLutData (lut) {
        this.lutData = lut
      },
      onChangeToneData (tone) {
        this.toneData = tone
        this.updateToneVideo()
      },
      updateToneVideo () {
        if (this.pixiItem) {
          console.log(this.toneData)
          pixiOperate.updateColor({pixiItem: this.pixiItem, tone: this.toneData})
        }
      },
      onNavTabsClick () {
        if (this.navTabs === 'toning') {
          if (this.pixiItem) {
            this.pixiItem.visible = true
            this.pixiItem.startRender()
          }
        }
      },
      initTone (toneData) {
        if (toneData) {
          return {
            brightness: toneData.exposure + 1,
            saturate: toneData.saturation,
            contrast: toneData.contrast,
            hue: toneData.hue
          }
        } else {
          return {
            brightness: 1,
            saturate: 0,
            contrast: 0,
            hue: 0
          }
        }
      },
      getToneInfo (type) {
        if (this.toneData && type) {
          return {
            contrast: this.toneData.contrast,
            saturation: this.toneData.saturate,
            hue: this.toneData.hue,
            exposure: this.toneData.brightness - 1
          }
        } else {
          return {
            contrast: 0,
            saturation: 0,
            hue: 0,
            exposure: 0
          }
        }
      },
      getLut () {
        if (this.lutData) {
          return this.lutData
        } else {
          return {
            path: '',
            factor: 1
          }
        }
      },
      createVideo () {
        this.videoDom = document.createElement('video')
        this.videoDom.setAttribute('id', 'preview')
        this.videoDom.setAttribute('autoplay', true)
        this.videoDom.setAttribute('muted', true)
        this.videoDom.className = 'videoBg'

        // this.videoDom = this.$refs.previewVideo
      },
      getCurrentBg () {
        let canvasBox = document.getElementById('canvasBox')
        let scale = Math.min(CROPBOX_WIDTH / this.imgWidth, CROPBOX_HEIGHT / this.imgHeight)
        canvasBox.style.width = CROPBOX_WIDTH + 'px'
        canvasBox.style.height = CROPBOX_HEIGHT + 'px'
        this.display.w = CROPBOX_WIDTH
        this.display.h = CROPBOX_HEIGHT
        pixiOperate.init({canvasBox})
        canvasBox.innerHTML = ''
        canvasBox.appendChild(pixiOperate.pixiApp.view)
        this.videoDom.onloadedmetadata = () => {
          console.log(this.toneData)
          this.pixiItem = pixiOperate.createPixiItem({display: this.display, media: this.videoDom, tone: this.toneData})
        }
      },
      programReset (program) {
        let programCopy = program
        programCopy.startTime = null
        let master = programCopy.video.channels.find(v => v.idx === 0)
        master.display = {dx: 0, dy: 0, width: 0, height: 0}
        master.crop = {dx: 0, dy: 0, width: 1, height: 1}
        master.colorTone = {
            contrast: 0,
            saturation: 0,
            hue: 0,
            exposure: 0
          }
        master.lut = {
            path: '',
            factor: 1
          }
        programCopy.video.channels = [master]
        programCopy.audios = []
        programCopy.video.useBackground = false
        programCopy.video.usePip = false
        return programCopy
      },
      initPreview () {
        this.videoLoading = true
        let program = this.programReset(this.$_deepCopy(this.currentProgramItem))
        let audio = this.$_deepCopy(this.audio)
        audio.audios = []
        setTimeout(() => {
          this.createVideo()
          this.video = new ChannelVideo(this.videoDom, program, audio, this.defaultPic, null, res => {
            this.imgWidth = res.width
            this.imgHeight = res.height
            this.getCurrentBg()
            this.videoLoading = false
          }, (msg) => {
            this.imgWidth = this.liveWidth
            this.imgHeight = this.liveHeight
            this.getCurrentBg()
            this.videoLoading = false
            this.$message.warning(msg)
          })
        }, 100)
        this.$nextTick(() => {
          this.contentWidth = CROPBOX_WIDTH
        })
      },
      ...mapActions(['modProgram'])
    },
    mounted () {
      this.initPreview()
    },
    beforeDestroy () {
      webrtcMgr.destroyAllPreview()
    },
    created () {
      this.toneData = this.initTone(this.currentProgramItem.video.channels.find(item => item.idx === 0).colorTone)
      this.lutData = this.currentProgramItem.video.channels.find(item => item.idx === 0).lut || {path: '', factor: 1}
    }
  }
</script>

<style scoped lang="scss">
  .palette-dialog {
    /deep/ {
      .el-dialog__body {
        display: flex;
        justify-content: space-between;
      }
      .el-tabs {
        width: 100%;
      }
      .el-tabs__item {
        background-color: transparent !important;
      }
      .el-tabs__item.is-active {
        background-color: transparent !important;
        border-top: 1px solid transparent !important;
      }
      .el-tabs__nav-wrap::after {
        background-color: #4C566A;
      }
    }
    .preview-box {
      width: 960px;
      height: 540px;
      background-color: #000;
      flex-grow: 0;
      flex-shrink: 0;
      display: flex;
      align-items: center;
      justify-content: center;
      position: relative;
      #imgBox {
        width: 100%;
        height: 100%;
        img {
          width: 100%;
          height: 100%;
        }
      }
    }
    .right-option {
      display: flex;
      justify-content: space-between;
      flex-grow: 0;
      flex-shrink: 0;
      width: 325px;
      .label {
        color: $color-text;
      }
    }
  }
  /deep/ .el-loading-mask {
    display: flex;
    justify-content: center;
  }
  /deep/ .el-loading-spinner {
    padding: 10px 0;
    width: 80px;
    background: rgba(0, 0, 0, 0.8);
    border-radius: 6px;
  }
  /deep/ .loading-bg {
    display: inline-block;
    width: 20px;
    height: 20px;
    background: url("../../common/img/loading-icon.gif") no-repeat center;
    background-size: 20px 20px;
  }
  /deep/ .el-loading-spinner .el-loading-text {
    color: white;
  }
  #canvasBox {
    position: relative;
    z-index: 1;
  }
  .videoBg {
    width: 100%;
    height: 100%;
  }
</style>
