<template>
  <div class="live-video">
    <div class="video-area-box">
      <div class="video-area" ref="videoArea" :style="{width: videoWidth + 'px'}"
           v-loading="videoLoading" element-loading-background="rgba(0, 0, 0, 0)" element-loading-text="加载中...." element-loading-spinner="loading-bg">
        <video id="pgm" class="video wh100" autoplay :poster="getPoster" muted></video>
        <template v-if="info">
          <div class="volume-groove left">
            <div class="volume-bar pgm left" :style="{height: info.programVolume.left + '%', transition: 'height 1.2s ease-in'}"></div>
          </div>
          <div class="volume-groove right">
            <div class="volume-bar pgm right" :style="{height: info.programVolume.right + '%', transition: 'height 1.2s ease-in'}"></div>
          </div>
        </template>
      </div>
    </div>
    <div class="bottom-button" :style="{transform: 'scale(' + scale +')'}" ref="bottomArea">
      <el-button class="option-btn" size="large" type="primary" @click.stop="stopLive" v-if="isLiving" :disabled="roleType === 2">
        <i class="icon el-icon-video-camera-solid"></i>
        <span>结束直播</span>
      </el-button>
      <el-button class="option-btn" size="large" type="primary" @click.stop="startLive" :disabled="roleType === 2" v-else>
        <i class="icon el-icon-video-camera-solid"></i>
        <span>开始直播</span>
      </el-button>
      <el-button class="option-btn" type="info" size="large" @click="resume" v-if="Emergency.autoFlag ? autoUrgency : inUrgency" :disabled="roleType === 2">
        <i class="icon el-icon-refresh-left"></i>
        <span>恢复</span>
      </el-button>
      <el-button class="option-btn" size="large" type="info" @click.stop="urgency" :disabled="roleType === 2" v-else>
        <i style="margin-right: 8px;color:#ff0000;" class="icon icon-fount-urgent-cut"></i>
        <span>紧急切换</span>
      </el-button>
      <el-button class="option-btn" size="large" type="info" @click.stop="nextProgram" :disabled="roleType === 2">
        <i class="icon el-icon-caret-right"></i>
        <span>下一个节目</span>
      </el-button>
      <el-button class="option-btn clear-btn" :disabled="!hasCgs() || roleType === 2" :class="{dinginess: !hasCgs()}" size="large" type="info" @click.stop="clearCg">
        <i style="margin-right: 8px;" class="icon icon-fount-cg-clear"></i>
        <span>清空全部CG字幕</span>
      </el-button>
    </div>
    <div class="open-volumes-panel" v-if="showVolumesTipsPanel">
      <div class="volumes-tips">
        <i class="icon el-icon-warning"></i>
        <span>检测到浏览器的声音暂未开启</span>
      </div>
      <el-button type="primary" size="medium" @click="openPvwVolumes">点击开启</el-button>
    </div>
  </div>
</template>

<script>
  import {mapGetters, mapMutations, mapActions} from 'vuex'
  import ChannelVideo from 'common/js/channel-video'
  import liveApi from 'api/live'

  export default {
    name: 'live-video',
    data () {
      return {
        showVolumesTipsPanel: true,
        video: null,
        videoWidth: 0,
        scale: 1
      }
    },
    props: {
      info: {
        type: Object,
        default: () => {}
      },
      currentProgramId: {
        type: String
      },
      nextProgramId: {
        type: String
      }
    },
    computed: {
      btnScale () {
        if (this.$refs.bottomArea) {
          return this.$refs.bottomArea.offsetWidth / 670
        }
        return 1
      },
      videoLoading () {
        if (this.info.pullStatus && this.info.pullStatus[0] === 'normal') {
          return false
        } else if (!this.info.pullStatus) {
          return false
        }
        return true
      },
      autoUrgency () {
        return this.info && this.info.inUrgency
      },
      inUrgency () {
        return this.Emergency.inUrgency
      },
      isLiving () {
        return this.output.isLiving
      },
      ...mapGetters([
        'roleType',
        'instanceId',
        'Emergency',
        'output',
        'programs',
        'liveRatio',
        'defaultPic',
        'audio',
        'cgs',
        'programsSn',
        'loopPrograms',
        'todayPrograms'
      ])
    },
    watch: {
      autoUrgency (value) {
        this.setEmergency({inUrgency: value})
      },
      defaultPic (value) {
        this.getPoster(value)
      }
    },
    methods: {
      hasCgs () {
        let programInfo = this.programs.find(item => item.id === this.currentProgramId)
        let hasCg = this.cgs.find(item => item.selected)
        let cgIds = programInfo && programInfo.cgIds
        return hasCg || (cgIds && cgIds.length)
      },
      clearCg (type) {
        let programInfo = this.programs.find(item => item.id === 1)
        let hasRelate = (programInfo && programInfo.cgIds && programInfo.cgIds.length) || 0
        if (type === 'noTips') {
          return liveApi.clearCgsReq({}, () => {
            let newCgs = this.$_deepCopy(this.cgs)
            newCgs.forEach(item => {
              item.selected = false
            })
            this.setIntCgs(newCgs)
            this.getPrograms()
            this.$bus.$emit('initLogoList')
            this.hasCgs()
          })
        }
        this.$confirm(hasRelate > 0 ? '该CG字幕已被关联到节目单，是否清空?' : '确定清空吗', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          liveApi.clearCgsReq({}, () => {
            let newCgs = this.$_deepCopy(this.cgs)
            newCgs.forEach(item => {
              item.selected = false
            })
            this.setIntCgs(newCgs)
            this.getPrograms()
            this.$bus.$emit('initLogoList')
            this.hasCgs()
            this.$message.success('清空成功')
          })
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消'
          })
        })
      },
      getPoster (defaultPic) {
        if (defaultPic) return defaultPic
        return '@/common/img/video-placeholder.png'
      },
      openPvwVolumes () {
        let pvwEle = document.getElementById('pgm')
        pvwEle.muted = false
        this.showVolumesTipsPanel = false
      },
      startLive () {
        if (this.output.outputUrls && this.output.outputUrls.length && this.output.outputUrls[0]) {
          liveApi.startLive({}, () => {
            this.setOutput({isLiving: true})
          })
        } else {
          return this.$message.warning('请先设置直播分发地址')
        }
      },
      stopLive () {
        liveApi.stopLive({}, () => {
          this.setOutput({isLiving: false})
        })
      },
      urgency () {
        liveApi.urgency({}, () => {
          this.setEmergency({inUrgency: true})
        })
      },
      resume () {
        liveApi.resume({}, () => {
          this.setEmergency({inUrgency: false})
        })
      },
      getVideoWidth () {
        this.videoWidth = parseInt(this.$refs.videoArea.offsetHeight * this.liveRatio)
      },
      getNextProgramTip () {
        let todayPrograms = this.todayPrograms.filter(v => !v.isCompleted)
        if (!this.loopPrograms.length) {
          return '暂无下个节目'
        }
        let findLoopProgram = this.loopPrograms.find(v => v.id === this.currentProgramId)
        let findTodayProgram = todayPrograms.find(v => v.id === this.currentProgramId)
        let findNextTodyProgram = todayPrograms.find(v => v.id === this.nextProgramId)
        if (findTodayProgram) {
          return ''
        }
        if (!todayPrograms.length) {
          return ''
        }
        if (findLoopProgram && this.loopPrograms.length === 1) {
          return '未到今日节目播出时间，自动切换到下一个循环节目'
        }
        if (findLoopProgram && findNextTodyProgram) {
          return '未到今日节目播出时间，自动切换到下一个循环节目'
        }
        if (!findLoopProgram) {
          return '未到今日节目播出时间，自动切换到下一个循环节目'
        }
      },
      nextProgram () {
        let tips = this.getNextProgramTip()
        if (tips) {
          this.$message.warning(tips)
        }
        liveApi.nextProgram({programsSn: this.programsSn}, res => {
          this.setProgramsSn(res.programsSn)
          this.getPrograms()
        }, (code, msg, value) => {
          // this.$message.error(msg)
          if (code === -60004) {
            this.setProgramsSn(value.programsSn)
            this.nextProgram()
          }
        })
      },
      resizeChange () {
        if (this.$refs.bottomArea) {
          this.scale = this.$refs.bottomArea.offsetWidth / 670
        }
      },
      ...mapMutations({
        setEmergency: 'SET_EMERGENCY',
        setOutput: 'SET_OUTPUT',
        setProgramsData: 'SET_PROGRAMS',
        setIntCgs: 'INT_CGS',
        setProgramsSn: 'SET_PROGRAMS_SN'
      }),
      ...mapActions(['getPrograms'])
    },
    mounted () {
      this.$bus.$on('clearCgs', () => {
        this.clearCg('noTips')
      })
      this.$bus.$on('changeVolume', value => {
        let videoElement = document.getElementById('pgm')
        videoElement.volume = value
      })
      this.$bus.$on('windowResize', this.resizeChange)
      this.$nextTick(() => {
        this.getVideoWidth()
        this.scale = this.$refs.bottomArea.offsetWidth / 670
      })
      this.video = new ChannelVideo('pgm', 'pgm', this.instanceId)
      let videoElement = document.getElementById('pgm')
      videoElement.volume = 1 / 3
    }
  }
</script>

<style scoped lang="scss">
  .live-video {
    padding: 10px 10px 0 10px;
    box-sizing: border-box;
    height: 100%;
    /deep/ .tab-title {
      font-size: 22px;
      margin: 0 0 10px -12px;
    }
    .video-area-box {
      display: flex;
      justify-content: center;
      height: calc(100% - 80px);
      .video-area {
        position: relative;
        height: 100%;
        video {
          width:100%;
          height:100%;
          object-fit:fill;
        }
        .volume-groove,
        .volume-groove1 {
          position: absolute;
          bottom: 0;
          top: 0;
          width: 6px;
          display: flex;
          justify-content: center;
          align-items: flex-end;
          background-color: #242731;
          opacity: .8;
          overflow: hidden;
          &.left {
            left: 0;
          }
          &.right {
            right: 0;
          }
          .volume-bar {
            width: 6px;
            height: 50%;
            border-radius: 1px;
            background: linear-gradient(0deg, #17A928, #53EA52, #FFC821, #FF722C);
          }
        }
        .volume-groove1 {
          &.left {
            left: 6px;
          }
          &.right {
            right: 6px;
          }
        }
      }

    }
    .bottom-button {
      height: 63px;
      display: flex;
      align-items: center;
      justify-content: center;
      .option-btn {
        margin: 0 10px;
        .icon {
          transform: scale(1.2);
        }
      }
      .clear-btn {
        background-color: $color-content-bg;
        border-color: #535d72;
        color: #ffffff;
        &.dinginess {
          border-color: #404655;
          color: #404655;
        }
      }
    }
    .open-volumes-panel {
      position: fixed;
      width: 350px;
      z-index: 5000;
      right: 0;
      top: 0;
      text-align: center;
      padding: 30px 0;
      background-color: #444B5A;
      box-shadow: 0 4px 10px rgba(0, 0, 0, .2);
      .volumes-tips {
        margin: 10px auto;
        color: #E7EEFF;
        .icon {
          color: #FF9A16;
          margin-right: 8px;
        }
      }
      .el-icon-close {
        position: absolute;
        right: 0;
        top: 0;
        padding: 10px;
        cursor: pointer;
        font-size: 24px;
        &:hover {
          color: $color-theme;
        }
      }
      /deep/ .el-button--primary {
        background-color: #4D77D0;
        border-color: #4D77D0;
      }
    }
  }
  /deep/ .el-loading-mask {
    display: flex;
    justify-content: center;
  }
  /deep/ .el-loading-spinner {
    padding: 10px 0;
    width: 80px;
    background: rgba(0, 0, 0, 0.8);
    border-radius: 6px;
  }
  /deep/ .loading-bg {
    display: inline-block;
    width: 20px;
    height: 20px;
    background: url("../../common/img/loading-icon.gif") no-repeat center;
    background-size: 20px 20px;
  }
  /deep/ .el-loading-spinner .el-loading-text {
    color: white;
  }
</style>
