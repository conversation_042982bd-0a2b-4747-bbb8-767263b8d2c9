<template>
  <div class="sound-mixer">
    <ul class="mixer-ul" :style="{transform: 'scaleY(' + scale +')'}" ref="mixer">
      <li class="mixer-li">
        <div class="mixer-box">
          <div class="top-button">
            <i class="icon" :class="{active: audioForm.programMix}" @click="setProgramMix" title="混音">M</i>
            <i class="icon line" :class="{active: audioForm.programSingle}" @click="setProgramSingle" title="独占">S</i>
          </div>
          <div class="sound-bar">
            <el-slider class="slider" @change="setProgramVolume" :max="300" v-model="audioForm.programVolume" :format-tooltip="showTooltip" vertical
                       height="140px" :disabled="roleType === 2"></el-slider>
          </div>
          <el-select class="channel-type" @change="setProgramType" v-model="audioForm.programType" placeholder="请选择" size="mini" :disabled="roleType === 2">
            <el-option v-for="v in options" :key="v.value" :label="v.label" :value="v.value"></el-option>
          </el-select>
          <div class="mute-option">
            <el-button style="width: 80px;" type="info" size="mini" @click="setProgramMute" :disabled="roleType === 2">
              <span :class="{mute: audioForm.programVolume === 0}">静音</span>
            </el-button>
          </div>
        </div>
        <p class="mixer-name">源流</p>
      </li>
      <li class="mixer-li">
        <div class="mixer-box">
          <div class="top-button">
            <i class="icon" :class="{active: audioForm.bgmMix}" @click="setBgmMix" title="混音">M</i>
            <i class="icon line" :class="{active: audioForm.bgmSingle}" @click="setBgmSingle" title="独占">S</i>
            <i class="icon line" title="音频设置" @click="showBgmUrlSetDialog">
              <i class="icon-fount-setting"></i>
            </i>
          </div>
          <div class="sound-bar">
            <el-slider class="slider" @change="setBgmVolume" :max="300" v-model="audioForm.bgmVolume" :format-tooltip="showTooltip" vertical
                       height="140px" :disabled="roleType === 2"></el-slider>
          </div>
          <div class="mute-option bgm">
            <el-button style="width: 80px;" type="info" size="mini" @click="setBgmMute" :disabled="roleType === 2">
              <span :class="{mute: audioForm.bgmVolume === 0}">静音</span>
            </el-button>
          </div>
        </div>
        <p class="mixer-name">BGM</p>
      </li>
      <li class="mixer-li" v-show="showInputBox">
        <div class="mixer-box">
          <div class="top-button">
            <i class="icon" :class="{active: audioForm.micMix}" @click="setMicMix" title="混音">M</i>
            <i class="icon line" :class="{active: audioForm.micSingle}" @click="setMicSingle" title="独占">S</i>
            <!-- <i class="icon line" :class="{active: audioForm.micMonitor}" title="监听" @click="setMicMonitor">
              <svg-icon icon-class="earphone" class="earphone"></svg-icon>
            </i> -->
            <i class="icon line" title="音频设置" @click="showMicPicker">
              <i class="icon-fount-setting"></i>
            </i>
          </div>
          <div class="sound-bar">
            <el-slider class="slider" @change="setMicVolume" :max="300" v-model="audioForm.micVolume" :format-tooltip="showTooltip" vertical
                       height="140px" :disabled="roleType === 2"></el-slider>
          </div>
          <div class="mute-option bgm">
            <el-button style="width: 80px;" type="info" size="mini" @click="setMicMute" :disabled="roleType === 2">
              <span :class="{mute: audioForm.micVolume === 0}">静音</span>
            </el-button>
          </div>
        </div>
        <p class="mixer-name">外设</p>
      </li>
      <li class="mixer-li">
        <div class="mixer-box">
          <div class="top-button">
            <i class="empty-icon"></i>
            <i class="empty-icon"></i>
          </div>
          <div class="sound-bar">
            <el-slider class="slider" @change="setEquipmentVolume" :max="300" v-model="equipmentVolume" :format-tooltip="showTooltip" vertical
                       height="140px" :disabled="roleType === 2"></el-slider>
          </div>
          <div class="mute-option listen">
            <el-button style="width: 80px;" type="info" size="mini" @click="setEquipmentMute" :disabled="roleType === 2">
              <span :class="{mute: equipmentVolume === 0}">静音</span>
            </el-button>
          </div>
        </div>
        <p class="mixer-name">监听</p>
      </li>
    </ul>
    <el-dialog class="relate-audio-first" title="关联背景音乐" width="700px" top="70px"
      :visible.sync="bgmUrlSetDialog" :close-on-click-modal="false" v-if="bgmUrlSetDialog">
      <div>
        当前播单
        <el-select v-model="selectedPlayId" placeholder="请选择播单" @change="getAudioList">
          <el-option v-for="item in playList" :label="item.name" :value="item.id" :key="item.id"></el-option>
        </el-select>
        <el-button @click="doApplyPgmMenu">应用</el-button>
      </div>
      <el-table :data="playInfo.data" style="width: 100%;margin-top: 15px" stripe height="500"
        element-loading-spinner="el-icon-loading"
        element-loading-background="rgba(0, 0, 0, 0.8)" border row-key="id" size="small">
        <el-table-column label="音频名" prop="fileName"></el-table-column>
        <el-table-column label="类型">
          <template slot-scope="scope">
            <span>{{scope.row.audioType ? '音频流' : '点播'}}</span>
          </template>
        </el-table-column>
        <el-table-column label="音频时长">
          <template slot-scope="scope">
            <span>{{scope.row.duration | formatDurationMs}}</span>
          </template>
        </el-table-column>
        <!-- <el-table-column label="操作">
          <template slot-scope="scope">
            <el-button type="info" @click="cancelRelate(scope.$index)" size="mini" v-if="roleType !== 2">取消关联</el-button>
          </template>
        </el-table-column> -->
        <div class="empty-box wh100" v-if="playInfo.data.length === 0" slot="empty">
          <no-data tips="暂无关联背景音频"></no-data>
        </div>
      </el-table>
      <!-- <div slot="footer" class="dialog-footer">
          <el-button size="small" type="primary" @click="relateAudioDialog=true" v-if="roleType !== 2">关联新文件</el-button>
          <div style="height: 32px" v-else></div>
      </div> -->
      <div class="tips">
        <p>注：</p>
        <p>
          1、若需要添加或移除音频，可前往 音频管理>自建播单 中操作，
          <span style="color: #687EBD;cursor: pointer;"
            @click="goAudioManage">立即前往</span>
        </p>
        <p>2、播单中有音频流时，会一直播放该音频流；若要播发其他音频文件，需要先手动移除该音频流</p>
      </div>
    </el-dialog>
    <relate-audio @close="relateAudioDialog=false" v-if="relateAudioDialog && $_isLocal" :selects="audio.audios || []"></relate-audio>
    <MicPicker @close="micPickerShow=false" v-if="micPickerShow" />
  </div>
</template>

<script>
  import {mapGetters, mapMutations} from 'vuex'
  import liveApi from 'api/live'
  import cloudApi from 'api/cloud'
  import relateAudio from '@/components/base/relateAudio/relate-audio'
  import noData from 'components/base/no-data'
  import MicPicker from './mic-picker.vue'

  export default {
    name: 'sound-mixer',
    components: {relateAudio, noData, MicPicker},
    props: {
      info: {
        type: Object,
        default: () => {}
      }
    },
    computed: {
      needInitMic () { // 需要重新设置硬件音频设置
        return this.audio.micMonitor || this.audio.micMix || this.audio.micSingle
      },
      showInputBox () {
        if (!this.info.micDev && this.needInitMic) {
          console.log(this.audio)
          this.audioForm.micVolume = 100
          this.audioForm.micSingle = false
          this.audioForm.micMix = false
          this.audioForm.micMonitor = false
          this.setSoundMixer()
        }
        return this.info.micDev || false
      },
      ...mapGetters([
        'roleType',
        'audio',
        'instanceId'
      ])
    },
    watch: {
      showInputBox (val) { // 插拔输入时，重置为默认
        console.log('micDev:', val)
        if (val) {
          this.audioForm.micVolume = 100
          this.audioForm.micSingle = false
          this.audioForm.micMix = false
          this.audioForm.micMonitor = true
        }
      }
    },
    data () {
      return {
        micPickerShow: false,
        playList: [],
        selectedPlayId: '',
        playInfo: {
          data: []
        },
        relateAudioDialog: false,
        scale: 1,
        bgmUrlSetDialog: false,
        options: [
          {label: '立体声', value: 0},
          {label: '左声道', value: 1},
          {label: '右声道', value: 2}
        ],
        audioRules: {
          bgmUrl: [
            {required: true, message: '请输入音频地址', trigger: 'blur'}
          ]
        },
        audioForm: {
          programVolume: 100,
          programType: 0,
          programSingle: false,
          programMix: false,
          bgmUrl: '',
          bgmVolume: 0,
          micVolume: 100,
          bgmSingle: false,
          micSingle: false,
          bgmMix: false,
          micMix: false,
          micMonitor: true
        },
        equipmentVolume: 100
      }
    },
    methods: {
      showMicPicker() {
        this.micPickerShow = true
      },
      getPgmList() {
        this.playList = []
        this.selectedPlayId = ''
        liveApi.getAudioPlayList({instanceId: this.instanceId}, (res) => {
          this.playList = Array.isArray(res) ? res : []
          if (this.playList.length > 0) {
            this.selectedPlayId = this.playList[0].id
            this.getAudioList()
          }
        })
      },
      getAudioList() {
        this.playInfo.data = []
        cloudApi.getAudioListNoPage({
          playlistId: this.selectedPlayId
        }, (res) => {
          if (res) {
            this.playInfo.data = Array.isArray(res) ? res : []
          }
        }, (code, msg) => {
          this.$message.error(msg)
        })
      },
      doApplyPgmMenu() {
        const param = {
          audioPlayListId: this.selectedPlayId,
          audios: this.playInfo.data.map(item => ({
              audioId: item.id,
              audioName: item.fileName,
              audioUrl: item.path,
              duration: item.duration,
              audioType: item.type
            }))
        }
        liveApi.setBgm(param, () => {
          this.$message.success('设置成功')
          this.bgmUrlSetDialog = false
        })
      },
      goAudioManage() {
        this.bgmUrlSetDialog = false
        this.$bus.$emit('locateMenu', this.selectedPlayId)
      },
      setMicMonitor () {
        if (this.roleType === 2) {
           return
        }
        this.audioForm.micMonitor = !this.audioForm.micMonitor
        this.setSoundMixer()
      },
      cancelRelate (index) {
        let newAudios = this.$_deepCopy(this.audio.audios)
        newAudios.splice(index, 1)
        liveApi.setBgm({audios: newAudios}, () => {
          this.$message.success('取消关联成功')
          this.setSoundMixerData({audios: newAudios})
        })
      },
      setEquipmentMute () {
        this.equipmentVolume = 0
        this.$bus.$emit('changeVolume', 0)
      },
      setProgramMute () {
        this.audioForm.programVolume = 0
        this.setSoundMixer()
      },
      setBgmMute () {
        this.audioForm.bgmVolume = 0
        this.setSoundMixer()
      },
      setMicMute () {
        this.audioForm.micVolume = 0
        this.setSoundMixer()
      },
      setMicVolume (value) {
        this.audioForm.micVolume = value
        this.setSoundMixer()
      },
      setProgramType (value) {
        this.audioForm.programType = value
        this.setSoundMixer()
      },
      setProgramVolume (value) {
        this.audioForm.programVolume = value
        this.setSoundMixer()
      },
      setEquipmentVolume (value) {
        this.equipmentVolume = value
        let vol = value / 300
        this.$bus.$emit('changeVolume', vol)
      },
      setBgmVolume (value) {
        this.audioForm.bgmVolume = value
        this.setSoundMixer()
      },
      setProgramSingle () {
        if (this.roleType === 2) {
           return
        }
        if (!this.audioForm.programSingle) {
          this.audioForm.programMix = false
          this.audioForm.bgmMix = false
          this.audioForm.bgmSingle = false
          this.audioForm.micMix = false
          this.audioForm.micSingle = false
        }
        this.audioForm.programSingle = !this.audioForm.programSingle
        this.setSoundMixer()
      },
      setBgmSingle () {
        if (this.roleType === 2) {
           return
        }
        if (!this.audioForm.bgmSingle) {
          this.audioForm.programMix = false
          this.audioForm.bgmMix = false
          this.audioForm.programSingle = false
          this.audioForm.micMix = false
          this.audioForm.micSingle = false
        }
        this.audioForm.bgmSingle = !this.audioForm.bgmSingle
        this.setSoundMixer()
      },
      setMicSingle () {
        if (this.roleType === 2) {
           return
        }
        if (!this.audioForm.micSingle) {
          this.audioForm.programMix = false
          this.audioForm.programSingle = false
          this.audioForm.bgmMix = false
          this.audioForm.bgmSingle = false
          this.audioForm.micMix = false
        }
        this.audioForm.micSingle = !this.audioForm.micSingle
        this.setSoundMixer()
      },
      setProgramMix () {
        if (this.roleType === 2) {
           return
        }
        if (!this.audioForm.programMix) {
          this.audioForm.programSingle = false
          this.audioForm.bgmSingle = false
          this.audioForm.micSingle = false
        }
        this.audioForm.programMix = !this.audioForm.programMix
        this.setSoundMixer()
      },
      setBgmMix () {
        if (this.roleType === 2) {
           return
        }
        if (!this.audioForm.bgmMix) {
          this.audioForm.programSingle = false
          this.audioForm.bgmSingle = false
          this.audioForm.micSingle = false
        }
        this.audioForm.bgmMix = !this.audioForm.bgmMix
        this.setSoundMixer()
      },
      setMicMix () {
        if (this.roleType === 2) {
           return
        }
        if (!this.audioForm.micMix) {
          this.audioForm.programSingle = false
          this.audioForm.bgmSingle = false
          this.audioForm.micSingle = false
        }
        this.audioForm.micMix = !this.audioForm.micMix
        this.setSoundMixer()
      },
      setSoundMixer () {
        let param = this.audioForm
        param.audios = this.audio.audios
        liveApi.setSoundMixer(param, () => {
          this.setSoundMixerData(this.$_deepCopy(this.audioForm))
        }, (code, msg) => {
          this.$message.error(msg)
        })
      },
      showBgmUrlSetDialog () {
        this.getPgmList()
        this.bgmUrlSetDialog = true
      },
      showTooltip (value) {
        return value / 100
      },
      resizeChange () {
        this.$nextTick(() => {
          if (this.$refs.mixer) {
            this.scale = this.$refs.mixer.offsetWidth / 690
          }
        })
      },
      ...mapMutations({
        setSoundMixerData: 'SET_SOUND_MIXER'
      })
    },
    mounted () {
      this.$bus.$on('windowResize', this.resizeChange)
      this.$nextTick(() => {
        if (this.$refs.mixer) {
          this.scale = this.$refs.mixer.offsetWidth / 690
        }
      })
    },
    created () {
      this.audioForm = this.$_deepCopy(this.audio)
      let vol = this.equipmentVolume / 300
      this.$nextTick(() => {
        this.$bus.$emit('changeVolume', vol)
      })
    }
  }
</script>

<style scoped lang="scss">
  .sound-mixer {
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    .active {
      color: $color-theme;
      .earphone {
        color: $color-theme;
      }
    }
    .mixer-ul {
      display: flex;
      justify-content: space-around;
      overflow-y: auto;
      padding: 0 14px 0 30px;
      .mixer-li {
        flex: 0 0 100px;
        width: 100px;
        .mixer-name {
          margin-top: 16px;
          text-align: center;
          font-size: $font-size;
        }
      }
    }
    .mixer-box {
      border: 1px solid #191D25;
      background-color: #343B48;
      text-align: center;
      .top-button {
        display: flex;
        border-bottom: 1px solid #191D25;
        .empty-icon {
          flex: 1;
          height: 30px;
          line-height: 30px;
          text-align: center;
          font-size: 16px;
        }
        .icon {
          flex: 1;
          height: 30px;
          line-height: 30px;
          text-align: center;
          font-size: 16px;
          cursor: pointer;
          &:hover {
            background-color: #5e6678;
          }
          &:active {
            background-color: #2B3748;
          }
          &.line {
            border-left: 1px solid #191D25;
          }
        }
      }
      .sound-bar {
        display: flex;
        width: 70px;
        height: 176px;
        margin: 10px auto;
        background: url("./sound-bar.png") no-repeat center/cover;
        .slider {
          margin: auto 4px;
          padding-top: 3px;
        }
        /deep/ .el-slider__runway {
          background-color: transparent;
        }
        /deep/ .el-slider__button {
          width: 30px;
          height: 30px;
          border: none;
          background: url("./sound-pointer.png") no-repeat center/cover;
        }
      }
      .channel-type {
        width: 80px;
        /deep/ .el-input__inner {
          background-color: #485163;
        }
      }
      .mute-option {
        margin: 14px 0 20px;
        &.bgm, &.listen {
          margin-top: 52px;
        }
        .mute {
          color: $color-theme;
        }
      }
    }
    /deep/ .el-input--suffix {
      .el-input__inner {
        padding-right: 20px;
      }
    }
    .box-content {
      max-height: 600px;
      overflow-y: auto;

      .bgm-info-box {
        display: flex;
        justify-content: flex-start;
        align-items: center;
        color: #B5BBCA;
        margin-bottom: 10px;
      }
      .bgm-info-item {
        font-size: 22px;
        display: inline-block;
        margin-right: 50px;
        width: 300px;
        @include elliptical();
      }
    }
  }
  ::v-deep .el-table td.el-table__cell {
    border-bottom: 1px solid #434A58;
  }
  .tips {
    position: absolute;
    bottom: 25px;
    color: #8892A7;
  }
</style>
