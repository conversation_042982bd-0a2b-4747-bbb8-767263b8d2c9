// 颜色定义规范
$color-theme: #FF5C14;               // 平台品牌色，仅限小面积使用，用于需要特别强调突出的文字，按钮和选中状态的ICON
$color-theme-hover: #d65b53;         // 平台品牌色，仅限小面积使用，用于需要特别强调突出的文字，按钮和选中状态的ICON
$color-theme-active: #d62a23;        // 平台品牌色，仅限小面积使用，用于需要特别强调突出的文字，按钮和选中状态的ICON
$color-assist: #4386eb;              // 作为橘黄色的辅助色，用于二级重点突出的按钮或者文字颜色，当文字带有链接，并且可点击时可用该颜色
$color-header: #313642;              // 指挥调度主页界面皮肤和顶部导航栏主色调，仅限用于界面皮肤用色
$color-tips:  #7a859c;               // 注释说明类

$color-border: #434A58;              // 用于所有边框的描边颜色和分割线颜色
$color-border2: #000;                // 用于所有边框的描边颜色和分割线颜色
$color-border-btn: #22262E;          // 用于按钮边框
$color-bg-btn: #535D72;              // 用于按钮边框
$color-bg-header: #434A5A;              // 用于按钮边框
$color-divider: #404756;             // 分割线
$color-content-bg: #373D4A;          // 用于所有页面内容区域的背景底色
$color-mask: rgba(0, 0, 0, .5);       // 用于遮罩颜色

$color-text: #B5BBCA;                // 主标题和常规内容文字的主要用色
$color-sub-text: #979797;            // 作为副标题和次要内容文字的颜色，比如查看更多，是否关闭导航栏等，一般和#333333成深浅对比关系时出现
$color-input-tips: #d3d3d3;          // 用于输入框内提示性文字的字体颜色
$color-tips: #677288;               // 用于提示性文字的字体颜色

// 字体定义规范
$font-size-small: 12px;              // 该字号为最小字号，仅用于注释类文案
$font-size: 14px;                    // 用于内页大部分常规内容文字
$font-size-medium: 16px;             // 用于左侧菜单名称和内页二级导航菜单名称
$font-size-large: 18px;             // 需要重点突出的小标题
$font-size-large-x: 20px;            // 仅用于需要重点突出的大标题

// 圆角
$border-radius: 6px;

// 阴影
$box-shadow: 0 1px 2px rgba(0, 0, 0, .2);
