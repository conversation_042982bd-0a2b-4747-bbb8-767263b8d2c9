const regexLineTitle = /^TITLE "(.*)"$/m
const regexLineSize = /^LUT_3D_SIZE (\d+)$/m
// eslint-disable-next-line no-useless-escape
const regexLineValue = /^([\d\.Ee-]+) ([\d\.Ee-]+) ([\d\.Ee-]+)$/

function lerp(a, b, t) {
  return b * t + a * (1.0 - t)
}

class LUTCube {
  /**
   * @param {number} size
   * @param {Float32Array} array
   */
  constructor(size, array) {
    this.size = size
    this.array = array
  }

  __lookup(ir, ig, ib) {
    const index = 3 * (ir + this.size * (ig + this.size * ib))
    return this.array.subarray(index, index + 3)
  }

  /**
   * @param {number} r
   * @param {number} g
   * @param {number} b
   */
  lookupNearest(r, g, b) {
    const ir = Math.round(r * (this.size - 1))
    const ig = Math.round(g * (this.size - 1))
    const ib = Math.round(b * (this.size - 1))
    return this.__lookup(ir, ig, ib)
  }

  /**
   * @param {number} r
   * @param {number} g
   * @param {number} b
   */
  lookupLinear(r, g, b) {
    const ir = Math.floor(r * (this.size - 2))
    const ig = Math.floor(g * (this.size - 2))
    const ib = Math.floor(b * (this.size - 2))
    const fr = (r * (this.size - 2)) % 1.0
    const fg = (g * (this.size - 2)) % 1.0
    const fb = (b * (this.size - 2)) % 1.0
    const v000 = this.__lookup(ir, ig, ib)
    const v001 = this.__lookup(ir, ig, ib + 1)
    const v010 = this.__lookup(ir, ig + 1, ib)
    const v011 = this.__lookup(ir, ig + 1, ib + 1)
    const v100 = this.__lookup(ir + 1, ig, ib)
    const v101 = this.__lookup(ir + 1, ig, ib + 1)
    const v110 = this.__lookup(ir + 1, ig + 1, ib)
    const v111 = this.__lookup(ir + 1, ig + 1, ib + 1)
    return v000.map((v, i) => (
        lerp(
            lerp(
                lerp(v000[ i ], v001[ i ], fb),
                lerp(v010[ i ], v011[ i ], fb),
                fg
            ),
            lerp(
                lerp(v100[ i ], v101[ i ], fb),
                lerp(v110[ i ], v111[ i ], fb),
                fg
            ),
            fr
        )
    ))
  }
}

function parseLUTCube(str) {
  regexLineTitle.exec(str)

  const resultSize = regexLineSize.exec(str)
  const size = resultSize ? parseInt(resultSize[ 1 ]) : 32

  let lines = str.replace(/\r/g, '').split('\n')
  let index = 0
  lines && (lines = lines.filter(item => item))
  while (!regexLineValue.test(lines[ index ])) { index++ }
  lines.splice(0, index)
  lines.splice(size * size * size)

  const array = new Float32Array(3 * size * size * size)
  lines.forEach((line, i) => {
    const result = regexLineValue.exec(line)
    array[ i * 3 + 0 ] = parseFloat(result[ 1 ])
    array[ i * 3 + 1 ] = parseFloat(result[ 2 ])
    array[ i * 3 + 2 ] = parseFloat(result[ 3 ])
  })
  return new LUTCube(size, array)
}

function fillLUTOnCanvas(lut, ctx) {
  const data = ctx.getImageData(0, 0, 512, 512)

  for (var ir = 0; ir < 64; ir++) {
    for (var ig = 0; ig < 64; ig++) {
      for (var ib = 0; ib < 64; ib++) {
        const x = 511 - ((63 - ir) + (7 - ib % 8) * 64)
        const y = ig + Math.floor(ib / 8) * 64
        const value = lut.lookupLinear(ir / 63.0, ig / 63.0, ib / 63.0)
        data.data[ 4 * (x + 512 * y) + 0 ] = Math.round(255.0 * value[ 0 ])
        data.data[ 4 * (x + 512 * y) + 1 ] = Math.round(255.0 * value[ 1 ])
        data.data[ 4 * (x + 512 * y) + 2 ] = Math.round(255.0 * value[ 2 ])
        data.data[ 4 * (x + 512 * y) + 3 ] = 255
      }
    }
  }

  ctx.putImageData(data, 0, 0)
}

function convert(lut, callback) {
  if (typeof lut === 'string') {
    // 如果lut为下载路径
    var req = new XMLHttpRequest()
    req.onload = function() {
      getCanvas(req.responseText)
    }
    req.onerror = function () {
      getCanvas('')
    }
    req.open('GET', lut, true)
    req.send()
  } else if (typeof lut === 'object') {
    // 如果lut为文件
    const reader = new FileReader()
    reader.readAsText(lut)
    reader.onload = function () {
      getCanvas(this.result)
    }
    reader.onerror = function () {
      getCanvas('')
    }
  }
  function getCanvas(lutStr) {
    try {
      const lutCube = parseLUTCube(lutStr)
      var canvas = document.createElement('canvas')
      canvas.width = 512
      canvas.height = 512
      const ctx = canvas.getContext('2d')
      fillLUTOnCanvas(lutCube, ctx)
      callback(canvas)
    } catch (e) {
      callback(false)
      throw (e)
    }
  }
}

export default convert
