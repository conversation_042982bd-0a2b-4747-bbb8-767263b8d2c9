<template>
  <div class="content-box ">
    <template v-if="currentCgInfo.obj.dataType === 'realTime'">
      <template v-if="currentCgInfo.obj.styleType === 1">
        <div class="realTime" :class="[`style-${currentCgInfo.obj.styleType}`]" :style="{background: `linear-gradient(-90deg, rgba(105,124,183, ${currentCgInfo.obj.transparence}), rgba(134,153,209, ${currentCgInfo.obj.transparence}))`}">
          <div class="left">
            <i class="region-icon" v-html="svgIcon"></i>
            <span style="font-size: 20px">杭州市</span>
          </div>
          <div class="separated"></div>
          <div class="center">
            <div class="temperature"><span style="font-size: 34px; font-weight: bold">24</span><span style="font-size: 24px">℃</span></div>
            <div class="wind" style="font-size: 14px">东南风4级</div>
          </div>
          <div class="separated"></div>
          <div class="right">
            <i class="qi-100-fill" style="font-size: 46px"></i>
            <span style="font-size: 14px;">晴</span>
          </div>
        </div>
      </template>
      <template v-if="currentCgInfo.obj.styleType === 2">
        <div class="realTime" :class="[`style-${currentCgInfo.obj.styleType}`]" :style="{background: `linear-gradient(-90deg, rgba(255,86,19, ${currentCgInfo.obj.transparence}), rgba(255,165,48, ${currentCgInfo.obj.transparence}))`}">
          <div class="center">
            <div class="temperature"><span style="font-size: 34px; font-weight: bold">24</span><span style="font-size: 34px">℃</span></div>
            <div class="wind" style="font-size: 14px">东南风4级</div>
          </div>
          <div class="separated"></div>
          <div class="right">
            <i class="qi-100-fill" style="font-size: 46px"></i>
            <span style="font-size: 14px;">晴</span>
          </div>
        </div>
      </template>
      <template v-if="currentCgInfo.obj.styleType === 3">
        <div class="realTime" :class="[`style-${currentCgInfo.obj.styleType}`]">
          <div class="center" :style="{background: `rgba(255, 255, 255, ${currentCgInfo.obj.transparence})`}">
            <div class="temperature" style="color: #FF5C10"><span style="font-size: 34px; font-weight: bold">24</span><span style="font-size: 34px">℃</span></div>
            <div class="wind" style="color: #222222;font-size: 14px">东南风4级</div>
          </div>
          <div class="right" :style="{background: `linear-gradient(-90deg, rgba(255,86,19, ${currentCgInfo.obj.transparence}), rgba(255,165,48, ${currentCgInfo.obj.transparence}))`}">
            <i class="qi-318-fill" style="font-size: 40px"></i>
            <span style="font-size: 14px;">晴</span>
          </div>
        </div>
      </template>
      <template v-if="currentCgInfo.obj.styleType === 4">
        <div class="realTime" :class="[`style-${currentCgInfo.obj.styleType}`]"
             :style="{background: `linear-gradient(-90deg, rgba(57,116,237, ${currentCgInfo.obj.transparence}), rgba(23,187,255, ${currentCgInfo.obj.transparence}))`}">
          <div class="left">
            <i class="region-icon" v-html="svgIcon"></i>
            <span style="font-size: 20px">杭州市</span>
          </div>
          <div class="separated"></div>
          <div class="center">
            <i class="qi-100-fill" style="font-size: 46px"></i>
          </div>
          <div class="right">
            <div class="temperature"><span style="font-size: 34px; font-weight: bold">24</span><span style="font-size: 24px">℃</span></div>
            <div>
              <span style="font-size: 12px;margin-right: 5px">晴</span>
              <span class="wind" style="font-size: 12px">东南风4级</span>
            </div>
          </div>
        </div>
      </template>
    </template>
    <template v-if="currentCgInfo.obj.dataType === 'lastThreeDays'">
      <template v-if="currentCgInfo.obj.styleType === 1">
        <div class="lastThreeDays" :class="[`style-${currentCgInfo.obj.styleType}`]" :style="{background: `linear-gradient(-90deg, rgba(111,141,238, ${currentCgInfo.obj.transparence}), rgba(164,110,210, ${currentCgInfo.obj.transparence}))`}">
          <div class="left">
            <div class="date" style="font-size: 20px;margin-bottom: 3px">2022-10-24</div>
            <div class="flex-v-c">
              <i class="region-icon" style="font-size: 18px" v-html="svgIcon"></i>
              <span style="font-size: 18px">杭州市</span>
            </div>
          </div>
          <div class="separated"></div>
          <div class="center">
            <div class="temperature"><span style="font-size: 26px; font-weight: bold">18-24</span><span style="font-size: 20px">℃</span></div>
            <div class="wind" style="font-size: 14px">东南风4级</div>
          </div>
          <div class="separated"></div>
          <div class="right">
            <i class="qi-100-fill" style="font-size: 46px"></i>
            <span style="font-size: 14px;">晴</span>
          </div>
        </div>
      </template>
      <template v-if="currentCgInfo.obj.styleType === 2">
        <div class="lastThreeDays" :class="[`style-${currentCgInfo.obj.styleType}`]"
             style="padding: 0"
             :style="{color: '#fff',
             border: '1px solid rgba(255,255,255,0.44)',
             'border-radius':'6px',
             background: `linear-gradient(-90deg, rgba(93,102,147, ${currentCgInfo.obj.transparence}), rgba(103,131,169, ${currentCgInfo.obj.transparence}))`}">
          <div class="top" style="background: rgba(227, 231, 238, 0.16);width: 100%;padding: 5px 12px;box-sizing: border-box">
            <div class="date" style="font-size: 14px;">周四  2022-10-24</div>
          </div>
          <div class="bottom" style="display: flex;padding: 14px 22px">
            <div style="margin-right: 12px">
              <i class="qi-101-fill" style="font-size: 52px"></i>
            </div>
            <div style="display: flex; flex-direction: column">
              <div class="temperature" style="display: table-cell;vertical-align: top">
                <span style="font-size: 20px; font-weight: bold">18-24</span><span style="font-size: 12px;vertical-align: top;">℃</span>
              </div>
              <div style="font-size: 12px;">多云转晴</div>
              <div class="wind" style="font-size: 12px;">东南风4级</div>
            </div>
          </div>
        </div>
      </template>
      <template v-if="currentCgInfo.obj.styleType === 3">
        <div class="lastThreeDays" :class="[`style-${currentCgInfo.obj.styleType}`]"
             style="padding: 0"
             :style="{color: '#fff',
             border: '1px solid rgba(255,255,255,0.44)',
             'border-radius':'6px 0 6px 0',
             background: `linear-gradient(141deg, rgba(255,96,18, ${currentCgInfo.obj.transparence}), rgba(255,138,0, ${currentCgInfo.obj.transparence}))`}">
          <div class="top" style="width: 100%;padding: 10px 12px 5px 12px;box-sizing: border-box;border-bottom: 1px solid rgba(255, 255, 255, .2)">
            <div class="date" style="font-size: 14px;">周三  2022-10-24</div>
          </div>
          <div class="bottom" style="display: flex;padding: 12px 8px">
            <div style="display: flex; align-items: center;padding: 10px 10px; background: rgba(0, 0, 0, .18)">
              <div style="display: flex; flex-direction: column; margin-right: 18px;">
                <div class="temperature" style="display: table-cell;margin-bottom: 10px">
                  <span style="font-size: 26px; font-weight: bold">18-24</span><span style="font-size: 12px;vertical-align: top;">℃</span>
                </div>
                <div class="wind" style="font-size: 12px;">东南风4级</div>
              </div>
              <div style="display: flex; flex-direction: column">
                <i class="qi-101-fill" style="font-size: 40px"></i>
                <div style="font-size: 12px;">多云转晴</div>
              </div>
            </div>
          </div>
        </div>
      </template>

    </template>
  </div>
</template>

<script>
  export default {
    name: '',
    components: {
    },
    props: {
      currentCgInfo: {
        type: Object,
        default: () => {}
      }
    },
    data() {
      return {
        svgIcon: `<svg style="enable-background:new 0 0 20 20;width: 1em;height: 1em;vertical-align: -0.15em;fill: currentColor;overflow: hidden;" version="1.1" id="图层_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"
             viewBox="0 0 20 20" xml:space="preserve">
            <g>
              <g>
                <path d="M10,0.68c-3.74,0-6.76,2.99-6.76,6.68c0,3.69,6.76,11.97,6.76,11.97s6.76-8.28,6.76-11.97C16.76,3.67,13.73,0.68,10,0.68
                  L10,0.68z M10,10.68c-1.79,0-3.23-1.45-3.23-3.23S8.21,4.21,10,4.21s3.23,1.45,3.23,3.23S11.79,10.68,10,10.68L10,10.68z
                   M10,10.68"/>
              </g>
            </g>
        </svg>`
      }
    }
  }
</script>

<style scoped lang="scss">
.content-box, .realTime, .lastThreeDays, .left, .right, .center {
  display: flex;
  align-items: center;
  justify-content: center;
}
.separated {
  width: 1px;
  height: 45px;
  background: #FFFFFF;
  opacity: 0.35;
  margin: 0 15px;
}
.region-icon {
  font-size: 22px;
  margin-right: 3px;
  color: #FFFFFF;
}
.realTime {
  .center {
    flex-direction: column;
    justify-content: center;
    align-items: center;
  }
  .temperature {
    display: table-cell;
    vertical-align: bottom;
  }
  .right {
    flex-direction: column;
    justify-content: center;
    align-items: center;
  }
  &.style-1 {
    color: #fff;
    border: 1px solid rgba(255,255,255,0.3);
    border-radius: 6px 0px 6px 0px;
    padding: 6px 15px;
  }
  &.style-2 {
    color: #fff;
    border-radius: 6px 0px 6px 0px;
    padding: 6px 15px;
  }
  &.style-3 {
    color: #fff;
    border-radius: 6px;
    overflow-x: hidden;
    .center, .right {
      padding: 6px 15px;
    }
    .center {
      background-color: #FFFFFF;
    }
  }
  &.style-4 {
    color: #fff;
    border-radius: 6px 0px 6px 0px;
    padding: 6px 15px;
    .center {
      margin-right: 15px;
    }
    .right {
      align-items: start;
    }
  }
}
.lastThreeDays {
  .center {
    flex-direction: column;
    justify-content: center;
    align-items: center;
  }
  .temperature {
    display: table-cell;
    vertical-align: bottom;
  }
  .right {
    flex-direction: column;
    justify-content: center;
    align-items: center;
  }
  &.style-1 {
    color: #fff;
    border: 1px solid rgba(255,255,255,0.3);
    border-radius: 6px 0px 6px 0px;
    padding: 6px 15px;
    .left {
      flex-direction: column;
      align-items: start;
    }
  }
  &.style-2 {
    color: #fff;
    border-radius: 6px 0px 6px 0px;
    padding: 6px 15px;
    flex-direction: column;
  }
  &.style-3 {
    color: #fff;
    border-radius: 6px;
    overflow-x: hidden;
    flex-direction: column;
    .center, .right {
      padding: 6px 15px;
    }
    .center {
      background-color: #FFFFFF;
    }
  }
  &.style-4 {
    color: #fff;
    border-radius: 6px 0px 6px 0px;
    padding: 6px 15px;
    .center {
      margin-right: 15px;
    }
    .right {
      align-items: start;
    }
  }
}
</style>
