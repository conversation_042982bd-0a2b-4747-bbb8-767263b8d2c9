<template>
  <div class="webpage-properties">
    <!-- 网页地址 -->
    <div class="property-section">
      <div class="section-title">网页设置</div>
      <div class="property-group">
        <div class="url-input-section">
          <el-input
            v-model="localUrl"
            placeholder="请输入网页地址"
            @input="handleUrlChange"
          />
          <div class="url-actions">
            <el-button size="small" type="info" @click="handleCropPage" :disabled="!localUrl">
              裁剪画面
            </el-button>
          </div>
        </div>

        <!-- 裁剪信息显示 -->
        <div v-if="hasCropData" class="crop-info">
          <div class="crop-info-title">裁剪区域</div>
          <div class="crop-details">
            <span>位置: ({{ Math.round(localCrop.dx * 100) }}%, {{ Math.round(localCrop.dy * 100) }}%)</span>
            <span>大小: {{ Math.round(localCrop.width * 100) }}% × {{ Math.round(localCrop.height * 100) }}%</span>
          </div>
          <el-button size="mini" type="text" @click="clearCrop">清除裁剪</el-button>
        </div>
      </div>
    </div>

    <!-- 位置大小 -->
    <PositionSizeProperties
      :component="component"
      :canvas-size="canvasSize"
      @update="handleUpdate"
    />

    <!-- 裁剪弹窗 -->
    <WebpageCropDialog
      :visible.sync="showCropDialog"
      :webpage-url="localUrl"
      :component="component"
      @crop-applied="handleCropApplied"
    />
  </div>
</template>

<script>
import PositionSizeProperties from './common/PositionSizeProperties.vue'
import WebpageCropDialog from '../WebpageCropDialog.vue'

export default {
  name: 'WebpageProperties',
  components: {
    PositionSizeProperties,
    WebpageCropDialog
  },
  props: {
    component: {
      type: Object,
      required: true
    },
    canvasSize: {
      type: Object,
      default: () => ({ width: 1920, height: 1080 })
    }
  },
  data() {
    return {
      localUrl: '',
      localCrop: {
        dx: 0,
        dy: 0,
        width: 1,
        height: 1
      },
      showCropDialog: false
    }
  },
  watch: {
    component: {
      handler() {
        this.syncLocalValues()
      },
      immediate: true
    }
  },
  computed: {
    hasCropData() {
      return this.localCrop.width < 1 || this.localCrop.height < 1 || this.localCrop.dx > 0 || this.localCrop.dy > 0
    }
  },
  methods: {
    syncLocalValues() {
      this.localUrl = this.component.url || ''
      this.localCrop = this.component.crop || {
        dx: 0,
        dy: 0,
        width: 1,
        height: 1
      }
    },
    handleUpdate(data) {
      this.$emit('update', data)
    },
    handleUrlChange() {
      this.$emit('update', { url: this.localUrl })
    },
    handleCropPage() {
      if (!this.localUrl) {
        this.$message.warning('请先输入网页地址')
        return
      }
      this.showCropDialog = true
    },
    handleCropApplied(cropData) {
      // 更新组件的裁剪数据
      this.localCrop = cropData.crop
      this.$emit('update', {
        crop: cropData.crop
      })
      this.$message.success('网页裁剪设置已应用')
    },
    clearCrop() {
      this.localCrop = {
        dx: 0,
        dy: 0,
        width: 1,
        height: 1
      }
      this.$emit('update', {
        crop: this.localCrop
      })
      this.$message.success('已清除裁剪设置')
    }
  }
}
</script>

<style lang="scss" scoped>
.webpage-properties {
  .property-section {
    margin-bottom: 20px;

    .section-title {
      font-size: 14px;
      font-weight: 500;
      margin-bottom: 12px;
      padding: 0 16px;
    }
  }

  .property-group {
    margin-bottom: 16px;
    padding: 0 16px;
  }

  .url-input-section {
    .url-actions {
      margin-top: 8px;
      text-align: right;
    }

    :deep(.el-input) {
      .el-input__inner {
        background: #4a4a4a;
        border-color: #606060;
        color: #ffffff;

        &::placeholder {
          color: #999999;
        }
      }
    }

    .crop-info {
      margin-top: 12px;
      padding: 12px;
      background: #3a3a3a;
      border-radius: 4px;
      border: 1px solid #606060;

      .crop-info-title {
        font-size: 12px;
        font-weight: 500;
        color: #ffffff;
        margin-bottom: 8px;
      }

      .crop-details {
        font-size: 11px;
        color: #cccccc;
        margin-bottom: 8px;

        span {
          display: block;
          margin-bottom: 2px;

          &:last-child {
            margin-bottom: 0;
          }
        }
      }

      :deep(.el-button--text) {
        color: #ff6b35;
        font-size: 11px;
        padding: 0;

        &:hover {
          color: #ff7a47;
        }
      }
    }
  }
}
</style>
