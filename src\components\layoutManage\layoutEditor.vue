<template>
  <el-dialog title="布局编辑" :visible.sync="dialogVisible" :width="dialogWidth" :close-on-click-modal="false"
    :close-on-press-escape="false" top="2vh" custom-class="layout-editor-dialog"
    @close="handleClose">
    <div class="dialog-header">
      <div class="layout-name-section">
        <span>布局名称：</span>
        <el-input size="small" v-model="layoutName" style="width: 352px;" placeholder="请输入布局名称" maxlength="10"
          show-word-limit class="layout-name-input" />
      </div>
      <div class="header-actions">
        <el-button type="primary" size="small" @click="handleSaveLayout" :loading="isSaving"
          :disabled="isSaving">
          {{ isSaving ? '保存中...' : '保存布局' }}
        </el-button>
      </div>
    </div>
    <!-- 主要内容区域 -->
    <div class="editor-content">
      <!-- 左侧组件选择区 -->
      <div class="component-panel">
        <ComponentPanel @add-component="handleAddComponent" :has-background="hasBackground" />
      </div>

      <!-- 中间画布操作区 -->
      <div class="canvas-container">
        <CanvasArea ref="canvasArea" :ratio="canvasRatio" :components="components"
          :selected-component="selectedComponent" @select-component="handleSelectComponent"
          @deactiva-component="handleDeactivateComponent"
          @update-component="handleUpdateComponent" />
      </div>

      <!-- 右侧属性设置区 -->
      <div class="properties-panel" @click.stop @mousedown.stop>
        <PropertiesPanel :component="selectedComponent" :canvas-size="canvasSize"
          :layout-name="layoutName" @delete-component="handleDeleteComponent"
          @update-component="handleUpdateComponent" />
      </div>
    </div>
  </el-dialog>
</template>

<script>
import ComponentPanel from './components/ComponentPanel.vue'
import CanvasArea from './components/CanvasArea.vue'
import PropertiesPanel from './components/PropertiesPanel.vue'
import {
  DefaultLayout,
  DefaultStream,
  DefaultVideo,
  DefaultBg,
  DefaultImage,
  DefaultSubtitle,
  DefaultWeb,
  DefaultProgram
} from './layout-default-data.js'
import html2canvas from 'html2canvas'
import { mapGetters } from 'vuex'
import Live from '@/api/live'
import { convertToBackendLayout } from './layoutDataConverter'

export default {
  name: 'LayoutEditor',
  components: {
    ComponentPanel,
    CanvasArea,
    PropertiesPanel
  },
  computed: {
    ...mapGetters(['instanceId'])
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    ratio: {
      type: String,
      default: '16:9'
    },
    layoutData: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      layoutName: '新建布局',
      components: [],
      selectedComponent: null,
      canvasRatio: '16:9',
      isSaving: false // 保存状态标识
    }
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible
      },
      set(val) {
        this.$emit('update:visible', val)
      }
    },
    dialogWidth() {
      return '82%'
    },
    dialogHeight() {
      return '81.5vh'
    },
    canvasSize() {
      // 根据比例计算画布尺寸，长边固定为1920
      const longSide = 1920
      let width, height

      switch (this.canvasRatio) {
        case '16:9':
          width = longSide
          height = Math.floor((longSide * 9) / 16)
          break
        case '9:16':
          width = Math.floor((longSide * 9) / 16)
          height = longSide
          break
        case '4:3':
          width = longSide
          height = Math.floor((longSide * 3) / 4)
          break
        case '4:9':
          width = Math.floor((longSide * 4) / 9)
          height = longSide
          break
        default:
          width = longSide
          height = Math.floor((longSide * 9) / 16)
      }

      return { width, height }
    },
    hasBackground() {
      return this.components.some((comp) => comp.type === 'bg')
    }
  },
  watch: {
    visible(val) {
      if (val) {
        this.initializeEditor()
      }
    }
  },
  methods: {
    handleDeleteComponent(component) {
      this.components = this.components.filter((comp) => comp.id !== component.id)
      if (this.selectedComponent && this.selectedComponent.id === component.id) {
        this.selectedComponent = null
      }
    },
    initializeEditor() {
      this.canvasRatio = this.ratio

      if (this.layoutData) {
        // 编辑模式：加载现有布局数据
        this.layoutName = this.layoutData.name || '布局名称'
        this.components = [...this.layoutData.components] || []
        this.selectedComponent =
          this.components.find((comp) => comp.type === 'main') || this.components[0]
      } else {
        // 新建模式：创建默认主画面组件
        const mainComponent = this.createMainComponent()
        this.components = [mainComponent]
        this.selectedComponent = mainComponent
      }
    },
    createMainComponent() {
      const mainComponent = DefaultLayout()
      mainComponent.width = this.canvasSize.width
      mainComponent.height = this.canvasSize.height
      mainComponent.dx = 0
      mainComponent.dy = 0
      mainComponent.ratio = this.canvasRatio
      return mainComponent
    },
    handleAddComponent(componentType) {
      console.log('添加组件:', componentType)

      // 组件类型与默认函数的映射关系
      const componentFactoryMap = {
        stream: () => ({
          ...DefaultStream(),
          name: '直播源',
          width: Math.floor(this.canvasSize.width * 0.3),
          height: Math.floor(this.canvasSize.height * 0.3),
          dx: Math.floor(this.canvasSize.width * 0.1),
          dy: Math.floor(this.canvasSize.height * 0.1),
          zIndex: this.getNextZIndex()
        }),
        video: () => ({
          ...DefaultVideo(),
          name: '点播源',
          width: Math.floor(this.canvasSize.width * 0.3),
          height: Math.floor(this.canvasSize.height * 0.3),
          dx: Math.floor(this.canvasSize.width * 0.1),
          dy: Math.floor(this.canvasSize.height * 0.1),
          zIndex: this.getNextZIndex()
        }),
        bg: () => ({
          ...DefaultBg(),
          name: '背景',
          width: this.canvasSize.width,
          height: this.canvasSize.height,
          dx: 0,
          dy: 0,
          opacity: 85,
          zIndex: 0
        }),
        logo: () => ({
          ...DefaultImage(),
          name: '图片',
          width: Math.floor(this.canvasSize.width * 0.2),
          height: Math.floor(this.canvasSize.height * 0.2),
          dx: Math.floor(this.canvasSize.width * 0.1),
          dy: Math.floor(this.canvasSize.height * 0.1),
          ratio: '1:1',
          zIndex: this.getNextZIndex()
        }),
        subtitle: () => ({
          ...DefaultSubtitle(),
          name: '文字',
          width: Math.floor(this.canvasSize.width * 0.2),
          height: Math.floor(this.canvasSize.height * 0.1),
          dx: Math.floor(this.canvasSize.width * 0.1),
          dy: Math.floor(this.canvasSize.height * 0.1),
          ratio: 'custom',
          text: '输入文字内容',
          size: 26,
          color: '#FF8300',
          textShadow: {
            color: '#FFFFFF',
            opacity: 100
          },
          zIndex: this.getNextZIndex()
        }),
        web: () => ({
          ...DefaultWeb(),
          name: '网页',
          width: Math.floor(this.canvasSize.width * 0.4),
          height: Math.floor(this.canvasSize.height * 0.4),
          dx: Math.floor(this.canvasSize.width * 0.1),
          dy: Math.floor(this.canvasSize.height * 0.1),
          ratio: '16:9',
          zIndex: this.getNextZIndex()
        }),
        program: () => ({
          ...DefaultProgram(),
          name: '节目单',
          width: Math.floor(this.canvasSize.width * 0.3),
          height: Math.floor(this.canvasSize.height * 0.2),
          dx: Math.floor(this.canvasSize.width * 0.1),
          dy: Math.floor(this.canvasSize.height * 0.1),
          ratio: 'custom',
          zIndex: this.getNextZIndex()
        })
      }

      // 创建组件
      const componentFactory = componentFactoryMap[componentType]
      if (componentFactory) {
        const newComponent = {
          ...componentFactory()
        }

        this.components.push(newComponent)
        this.selectedComponent = newComponent
        this.$message.success(`已添加${newComponent.name}组件`)
      } else {
        console.warn('未知的组件类型:', componentType)
      }
    },
    getNextZIndex() {
      const maxZ = Math.max(...this.components.map((comp) => comp.zIndex || 0))
      return maxZ + 1
    },
    handleSelectComponent(component) {
      this.selectedComponent = component
    },
    handleDeactivateComponent(component) {
      if (this.selectedComponent && this.selectedComponent.id === component.id) {
        this.selectedComponent = null
      }
    },
    handleUpdateComponent(updatedComponent) {
      const index = this.components.findIndex((comp) => comp.id === updatedComponent.id)
      if (index !== -1) {
        this.$set(this.components, index, { ...updatedComponent })
        if (this.selectedComponent && this.selectedComponent.id === updatedComponent.id) {
          this.selectedComponent = { ...updatedComponent }
        }
      }
    },
    async handleSaveLayout() {
      if (this.isSaving) return

      this.isSaving = true

      try {
        // 1. 取消当前组件选中状态
        this.selectedComponent = null

        // 2. 等待DOM更新
        await this.$nextTick()

        // 3. 截图画布区域
        console.log('开始截图画布...')
        const previewImage = await this.captureCanvas()
        console.log('画布截图完成')

        // 4. 上传截图
        console.log('开始上传截图...')
        const imageUrl = await this.uploadCanvasImage(previewImage)
        console.log('截图上传完成:', imageUrl)

        // 5. 构建布局数据
        const layoutData = {
          ...this.layoutData,
          id: this.layoutData && this.layoutData.id || (parseInt(Math.random() * 10000000000) + ''),
          name: this.layoutName,
          ratio: this.canvasRatio,
          components: this.components,
          canvasSize: this.canvasSize,
          previewImage: imageUrl // 添加预览图片URL
        }

        // 6. 调用后端API保存
        const savedLayoutData = await this.saveLayoutToBackend(layoutData)

        this.isSaving = false
        // 7. 发送保存成功事件，返回最新的布局数据
        this.$emit('save-layout', savedLayoutData)
        this.$message.success('布局保存成功')
      } catch (error) {
        console.error('保存布局失败:', error)

        this.isSaving = false
        this.$message.error('保存布局失败: ' + (error.message || '未知错误'))
      } finally {
        this.isSaving = false
      }
    },

    async captureCanvas() {
      // 获取画布DOM元素
      const canvasElement = this.$el.querySelector('.canvas')
      if (!canvasElement) {
        throw new Error('找不到画布元素')
      }

      // 获取CanvasArea组件实例来获取scale值
      const canvasAreaComponent = this.$refs.canvasArea
      const canvasScale = canvasAreaComponent ? canvasAreaComponent.scale : 1

      try {
        // 临时隐藏所有选中状态和控制元素
        const hideElements = canvasElement.querySelectorAll('.vdr.active, .handle, .selected')
        hideElements.forEach((el) => {
          el.style.visibility = 'hidden'
        })

        // 计算实际的画布尺寸（考虑缩放）
        const actualWidth = Math.floor(this.canvasSize.width * canvasScale)
        const actualHeight = Math.floor(this.canvasSize.height * canvasScale)

        console.log('截图参数:', {
          originalSize: this.canvasSize,
          canvasScale,
          actualSize: { width: actualWidth, height: actualHeight }
        })

        // 使用html2canvas截图
        const canvas = await html2canvas(canvasElement, {
          backgroundColor: '#1a1a1a', // 设置背景色
          scale: 0.5, // 缩放比例，减小文件大小
          useCORS: true, // 允许跨域
          allowTaint: false,
          width: actualWidth,
          height: actualHeight,
          scrollX: 0,
          scrollY: 0,
          logging: false, // 关闭日志
          removeContainer: true,
          ignoreElements: (element) => {
            // 忽略控制元素
            return (
              element.classList.contains('handle') ||
              element.classList.contains('vdr-handle') ||
              (element.classList.contains('vdr') && element.classList.contains('active'))
            )
          }
        })

        // 恢复隐藏的元素
        hideElements.forEach((el) => {
          el.style.visibility = ''
        })

        return canvas
      } catch (error) {
        console.error('画布截图失败:', error)
        // 确保恢复隐藏的元素
        const hideElements = canvasElement.querySelectorAll('.vdr.active, .handle, .selected')
        hideElements.forEach((el) => {
          el.style.visibility = ''
        })
        throw new Error('画布截图失败')
      }
    },

    async uploadCanvasImage(canvas) {
      return new Promise((resolve, reject) => {
        // 将canvas转换为blob
        canvas.toBlob(
          async (blob) => {
            if (!blob) {
              reject(new Error('转换图片失败'))
              return
            }

            try {
              // 创建FormData
              const formData = new FormData()
              const fileName = `layout_${Date.now()}.png`
              formData.append('file', blob, fileName)

              // 上传到指定接口
              const uploadUrl = `/cloud/fullTimeLive/pic/upload?instanceId=${this.instanceId || 1}`

              const xhr = new XMLHttpRequest()

              xhr.onload = () => {
                if (xhr.status === 200) {
                  try {
                    const response = JSON.parse(xhr.responseText)
                    console.log('上传响应:', response)

                    if (response && response.success !== false) {
                      // 尝试多种可能的URL字段
                      const relativeUrl = response.value.visitUrl

                      if (relativeUrl) {
                        resolve(relativeUrl)
                      } else {
                        console.warn('响应中未找到图片URL:', response)
                        reject(new Error('响应中未找到图片URL'))
                      }
                    } else {
                      reject(new Error(response.message || response.error || '上传失败'))
                    }
                  } catch (parseError) {
                    console.error('解析响应失败:', parseError, xhr.responseText)
                    reject(new Error('解析上传响应失败'))
                  }
                } else {
                  console.error('上传请求失败:', xhr.status, xhr.statusText, xhr.responseText)
                  reject(new Error(`上传失败: HTTP ${xhr.status} ${xhr.statusText}`))
                }
              }

              xhr.onerror = () => {
                reject(new Error('网络请求失败'))
              }

              xhr.ontimeout = () => {
                reject(new Error('上传超时'))
              }

              xhr.timeout = 30000 // 30秒超时
              xhr.open('POST', uploadUrl, true)
              xhr.send(formData)
            } catch (error) {
              reject(new Error('准备上传失败: ' + error.message))
            }
          },
          'image/png',
          0.9
        ) // PNG格式，90%质量
      })
    },

    async saveLayoutToBackend(layoutData) {
      try {
        // 将前端布局数据转换为后端格式
        const backendLayout = convertToBackendLayout(layoutData)

        if (this.layoutData) {
          // 编辑模式：更新现有布局
          backendLayout.id = this.layoutData.id

          await new Promise((resolve, reject) => {
            Live.updateLayout({layout: backendLayout}, (response) => {
              console.log('更新布局成功:', response)
              resolve(response)
            }, (error) => {
              console.error('更新布局失败:', error)
              reject(error)
            })
          })

          // 返回更新后的布局数据
          return {
            ...layoutData,
            id: this.layoutData.id
          }
        } else {
          // 新建模式：添加新布局
          const response = await new Promise((resolve, reject) => {
            Live.addLayout({layout: backendLayout}, (response) => {
              console.log('新增布局成功:', response)
              resolve(response)
            }, (error) => {
              console.error('新增布局失败:', error)
              reject(error)
            })
          })

          // 返回新建的布局数据（使用后端返回的ID）
          return {
            ...layoutData,
            id: response.id || layoutData.id
          }
        }
      } catch (error) {
        console.error('保存布局到后端失败:', error)
        throw new Error('保存布局失败: ' + (error.message || '未知错误'))
      }
    },

    handleClose() {
      this.$emit('update:visible', false)
      this.$emit('close')

      // 重置数据
      this.layoutName = '新建布局'
      this.components = []
      this.selectedComponent = null
    }
  }
}
</script>

<style lang="scss" scoped>
:deep(.layout-editor-dialog) {
  > .el-dialog {
    height: 81.5vh;
    display: flex;
    flex-direction: column;
    .el-dialog__body {
      flex: 1;
      padding: 22px 27px;
      overflow: hidden;
      height: 78vh;
    }
  }
  .el-input__count-inner {
    background: transparent;
  }
}

.dialog-header {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  box-sizing: border-box;
  .layout-name-section {
    display: flex;
    margin-bottom: 10px;
    align-items: center;
    gap: 10px;
    color: #BEC9E5;
    span {
      font-size: 14px;
    }
    .layout-name-input {
      width: 200px;

      :deep(.el-input__inner) {
        background: #4a4a4a;
        border-color: #606060;
        color: #ffffff;
      }
    }
  }
}

.editor-content {
  display: flex;
  height: 78vh;
  border-radius: 10px;
  overflow: hidden;
}

.component-panel {
  width: 80px;
  display: flex;
  flex-direction: column;
  align-items: center;
  background: #323743;
  flex-shrink: 0;
}

.canvas-container {
  flex: 1;
  background: #242831;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.properties-panel {
  width: 280px;
  background: #323743;
  flex-shrink: 0;
  overflow-y: auto;
  overflow-x: hidden;
}
</style>
