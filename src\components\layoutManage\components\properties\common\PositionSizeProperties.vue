<template>
  <div class="position-size-properties">
    <div class="property-section">
      <div class="section-title">位置大小</div>

      <!-- 比例选择 -->
      <div class="property-group">
        <div class="ratio-buttons">
          <button
            v-for="ratio in ratioOptions"
            :key="ratio.value"
            class="ratio-btn"
            :class="{ active: component.ratio === ratio.value }"
            @click="handleRatioChange(ratio.value)"
          >
            {{ ratio.label }}
          </button>
        </div>
      </div>

      <!-- 宽度高度 -->
      <div class="property-group">
        <div class="size-inputs">
          <div class="input-group">
            <label>宽度</label>
            <el-input-number
              v-model="localWidth"
              :min="1"
              :max="canvasSize.width"
              size="small"
              controls-position="right"
              @change="handleSizeChange"
            />
          </div>
          <div class="lock-icon" @click="toggleLock">
            <i :class="isLocked ? 'el-icon-lock' : 'el-icon-unlock'"></i>
          </div>
          <div class="input-group">
            <label>高度</label>
            <el-input-number
              v-model="localHeight"
              :min="1"
              :max="canvasSize.height"
              size="small"
              controls-position="right"
              @change="handleSizeChange"
            />
          </div>
        </div>
      </div>

      <!-- X轴Y轴 -->
      <div class="property-group">
        <div class="position-inputs" style="gap: 24px;">
          <div class="input-group">
            <label>X轴</label>
            <el-input-number
              v-model="localDx"
              :min="0"
              :max="canvasSize.width - localWidth"
              size="small"
              controls-position="right"
              @change="handlePositionChange"
            />
          </div>
          <div class="input-group">
            <label>Y轴</label>
            <el-input-number
              v-model="localDy"
              :min="0"
              :max="canvasSize.height - localHeight"
              size="small"
              controls-position="right"
              @change="handlePositionChange"
            />
          </div>
        </div>
      </div>

      <!-- 快速定位 -->
      <div class="property-group">
        <div class="quick-position">
          <button class="position-btn" @click="quickPosition('left')" title="靠左">
            <svg-icon icon-class="p-t-l" />
          </button>
          <button class="position-btn" @click="quickPosition('center-h')" title="左右居中">
            <svg-icon icon-class="p-c-c" />
          </button>
          <button class="position-btn" @click="quickPosition('right')" title="靠右">
            <svg-icon icon-class="p-t-r" />
          </button>
          <button class="position-btn" @click="quickPosition('top')" title="靠上">
            <svg-icon icon-class="p-t-t" />
          </button>
          <button class="position-btn" @click="quickPosition('center-v')" title="上下居中">
            <svg-icon icon-class="p-v-c" />
          </button>
          <button class="position-btn" @click="quickPosition('bottom')" title="靠下">
            <svg-icon icon-class="p-t-b" />
          </button>
        </div>
      </div>
    </div>

    <!-- 调整层级 -->
    <div class="property-section">
      <div class="section-title">调整层级</div>
      <div class="property-group">
        <div class="layer-controls">
          <button class="layer-btn" @click="adjustLayer('up')" title="向上一层">
            <svg-icon icon-class="layout-zt" />
          </button>
          <button class="layer-btn" @click="adjustLayer('down')" title="向下一层">
            <svg-icon icon-class="layout-zb" />
          </button>
          <button class="layer-btn" @click="adjustLayer('top')" title="最顶层">
            <svg-icon icon-class="layout-first" />
          </button>
          <button class="layer-btn" @click="adjustLayer('bottom')" title="最底层">
            <svg-icon icon-class="layout-last" />
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import {mapMutations} from 'vuex'
import TransitionEffects from '@/components/transitionEffects';
export default {
  name: 'PositionSizeProperties',
  props: {
    component: {
      type: Object,
      required: true
    },
    canvasSize: {
      type: Object,
      default: () => ({ width: 1920, height: 1080 })
    }
  },
  data() {
    return {
      localWidth: 0,
      localHeight: 0,
      localDx: 0,
      localDy: 0,
      ratioOptions: [
        { label: '16:9', value: '16:9', ratio: 16 / 9 },
        { label: '9:16', value: '9:16', ratio: 9 / 16 },
        { label: '4:3', value: '4:3', ratio: 4 / 3 },
        { label: '1:1', value: '1:1', ratio: 1 },
        { label: '自定义', value: 'custom', ratio: null }
      ]
    }
  },
  computed: {
    isLocked() {
      return this.component.whLocked
    }
  },
  watch: {
    component: {
      handler() {
        this.syncLocalValues()
      },
      immediate: true
    }
  },
  methods: {
    syncLocalValues() {
      this.localWidth = this.component.width || 100
      this.localHeight = this.component.height || 100
      this.localDx = this.component.dx || 0
      this.localDy = this.component.dy || 0
    },
    handleRatioChange(ratio) {
      const updates = { ratio }
      // 先解开锁定，防止后续更改宽高重复触发
      this.$emit('update', {whLocked: false})
      // 先解开父组件的限制，防止无法同时修改宽高和xy
      this.setHasParentLimit(false)
      if (ratio !== 'custom') {
        const ratioOption = this.ratioOptions.find(r => r.value === ratio)
        if (ratioOption && ratioOption.ratio) {
          // 根据选择的比例计算长边包含撑满画布的尺寸
          const targetRatio = ratioOption.ratio // 宽/高
          const canvasRatio = this.canvasSize.width / this.canvasSize.height

          let width, height

          // 长边撑满画布逻辑
          if (targetRatio >= 1) {
            // 横向比例（宽>=高）
            if (canvasRatio >= targetRatio) {
              // 画布比组件更宽，高度撑满
              height = this.canvasSize.height
              width = Math.floor(height * targetRatio)
            } else {
              // 画布比组件更窄，宽度撑满
              width = this.canvasSize.width
              height = Math.floor(width / targetRatio)
            }
          } else {
            // 纵向比例（宽<高）
            if (canvasRatio <= targetRatio) {
              // 画布比组件更高，宽度撑满
              width = this.canvasSize.width
              height = Math.floor(width / targetRatio)
            } else {
              // 画布比组件更宽，高度撑满
              height = this.canvasSize.height
              width = Math.floor(height * targetRatio)
            }
          }

          // 短边居中
          const dx = Math.floor((this.canvasSize.width - width) / 2)
          const dy = Math.floor((this.canvasSize.height - height) / 2)

          updates.width = width
          updates.height = height
          updates.dx = dx
          updates.dy = dy
        }
      } else {
        updates.whLocked = false
      }
      this.$nextTick(() => {
        this.$emit('update', updates)
        // 锁定宽高
        this.$nextTick(() => {
          // 调整完之后再限制父组件
          this.setHasParentLimit(true)
          if (ratio !== 'custom') {
            this.$emit('update', {whLocked: true})
          }
        })
      })
      // this.$emit('update', updates)
    },
    handleSizeChange() {
      const updates = {
        width: this.localWidth,
        height: this.localHeight
      }

      this.$emit('update', updates)
    },
    handlePositionChange() {
      this.$emit('update', {
        dx: this.localDx,
        dy: this.localDy
      })
    },
    toggleLock() {
      const updates = {
        whLocked: !this.component.whLocked
      }

      // 如果在锁定状态点击解锁，则比例选择自动跳到自定义
      if (this.component.whLocked) {
        updates.ratio = 'custom'
      }

      this.$emit('update', updates)
    },
    quickPosition(type) {
      let dx = this.localDx
      let dy = this.localDy

      switch (type) {
        case 'left':
          dx = 0
          break
        case 'center-h':
          dx = Math.floor((this.canvasSize.width - this.localWidth) / 2)
          break
        case 'right':
          dx = this.canvasSize.width - this.localWidth
          break
        case 'top':
          dy = 0
          break
        case 'center-v':
          dy = Math.floor((this.canvasSize.height - this.localHeight) / 2)
          break
        case 'bottom':
          dy = this.canvasSize.height - this.localHeight
          break
      }

      this.localDx = dx
      this.localDy = dy
      this.handlePositionChange()
    },
    adjustLayer(type) {
      let zIndex = this.component.zIndex || 1

      switch (type) {
        case 'up':
          zIndex += 1
          break
        case 'down':
          zIndex = Math.max(1, zIndex - 1)
          break
        case 'top':
          zIndex = 999
          break
        case 'bottom':
          zIndex = 1
          break
      }

      this.$emit('update', { zIndex })
    },
    ...mapMutations({
        setHasParentLimit: 'SET_HAS_PARENT_LIMIT'
      }
    )
  }
}
</script>

<style lang="scss" scoped>
.position-size-properties {
  .property-section {
    margin-bottom: 20px;

    .section-title {
      font-size: 14px;
      font-weight: 500;
      margin-bottom: 12px;
      padding: 0 16px;
    }
  }

  .property-group {
    margin-bottom: 16px;
    padding: 0 16px;
  }

  .ratio-buttons {
    display: flex;
    flex-wrap: wrap;
    background-color: #3F4552;
    border-radius: 5px;
    .ratio-btn {
      flex: 1;
      min-width: 40px;
      height: 30px;
      border: none;
      cursor: pointer;
      border-radius: 5px;
      font-size: 12px;
      transition: all 0.2s ease;
      background-color: transparent;
      color: #BEC9E5;
      &:hover {
        background: #555D70;
      }

      &.active {
        background: #555D70;
        color: #fff;
      }
    }
  }

  .size-inputs, .position-inputs {
    display: flex;
    align-items: center;

    .input-group {
      flex: 1;

      label {
        display: block;
        font-size: 12px;
        margin-bottom: 4px;
      }

      :deep(.el-input-number) {
        width: 100%;

        .el-input__inner {
          background: #4a4a4a;
          border-color: #606060;
          color: #ffffff;
          height: 28px;
          line-height: 28px;
        }
      }
    }

    .lock-icon {
      width: 24px;
      height: 24px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      color: #cccccc;
      margin-top: 16px;

      &:hover {
        color: #ff6b35;
      }

      i {
        font-size: 16px;
      }
    }
  }

  .quick-position {
    display: flex;
    background-color: #3F4552;
    border-radius: 6px;
    gap: 12.3px;
    .position-btn {
      width: 30px;
      height: 30px;
      display: inline-block;
      background: transparent;
      border: none;
      color: #B3BACD;
      border-radius: 6px;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.2s ease;

      &:hover {
        background: #555D70;
        color: #fff;
      }

      i {
        font-size: 14px;
      }
    }
  }

  .layer-controls {
    display: flex;
    justify-content: center;
    background-color: #3F4552;
    border-radius: 5px;
    gap: 3px;
    .layer-btn {
      width: 73px;
      height: 30px;
      background: transparent;
      border: none;
      color: #B3BACD;
      border-radius: 5px;
      font-size: 16px;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.2s ease;

      &:hover {
        background: #555D70;
        color: #fff;
      }

      i {
        font-size: 14px;
      }
    }
  }
}
</style>
