<template>
  <div class="program-list">
    <div class="top-area">
      <div>
        <span class="program-tips">注：节目编排不影响今日节目单</span>
        <span class="g-option-btn-2" style="width: 92px;margin-left: 10px" @click="openCalendar()">节目日历</span>
        <span class="g-option-btn-1" style="width: 84px;" @click="addProgramDialog(null)" v-show="roleType !== 2">
          <i class="el-icon-plus" style="margin-right: 5px;font-size: 14px"></i>
          <span>新增</span>
        </span>
      </div>
    </div>
    <div class="program-wrapper">
      <div class="list-header" ref="header">
        <span class="program-checked">序号</span>
        <span class="program-name">节目名称</span>
        <span class="program-number">节目类型
          <el-tooltip effect="light" content="不同类型节目时间冲突时，播出优先级：指定时间>周播>日播" placement="top">
            <span class="el-icon-warning" style="margin-left: 5px"></span>
          </el-tooltip>
        </span>
        <span class="program-time">播出时间</span>
        <span class="program-duration">节目时长</span>
        <span class="cg-name">CG名称</span>
        <span class="audio">音频</span>
        <span class="program-options">操作</span>
      </div>
      <div class="program-ul" id="program-ul">
        <div class="program-li program-drag"
             :key="item.id" v-for="(item, index) in pageList.data">
          <span class="program-checked">
            {{index + 1}}
          </span>
          <div class="program-name">
            <div class="name" :title="item.program.name">{{item.program.name}}</div>
          </div>
          <div class="program-number" style="flex-direction: column;">
            <div>{{item.type === 1 ? '日播节目' : item.type === 2 ? '周播' : '指定时间节目'}}</div>
            <div v-show="item.type === 2">{{getDateText(item.daysOfWeek)}}</div>
          </div>
          <div class="program-time">
            <div class="time">
              <p v-show="item.day">{{item.day}}</p>
              <p v-show="item.time">{{item.time}}</p>
            </div>
          </div>
          <div class="program-duration">
            <div class="time">{{item.duration | formatDuration}}</div>
          </div>
          <div class="cg-name">
            <ul class="g-hide-scroll cg-name-ul" v-if="item.program.cgIds && item.program.cgIds.length">
              <li class="cg-name-li" v-for="(v, i) in item.program.cgIds" :title="getCgName(v)">{{i + 1}}、{{getCgName(v)}}</li>
            </ul>
            <span v-else>-</span>
          </div>
          <div class="audio">
            <ul class="g-hide-scroll audio-ul" v-if="item.program.audios && item.program.audios.length">
              <li class="audio-li" v-for="(v, i) in item.program.audios" :class="{lineThrough: v.isDeleted}" :title="v.audioName">{{i + 1}}、{{v.audioName}}</li>
            </ul>
            <span v-else>-</span>
          </div>
          <div class="program-options">
            <el-button type="text" class="text-btn" @click="showPreview(item)">预览</el-button>
            <el-dropdown @mouseenter="programOptionShow(item)" style="margin-left: 10px" v-if="roleType !== 2">
              <el-button type="text" class="text-btn">更多</el-button>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item @click.native="relateSubtitleShow(item)">关联字幕</el-dropdown-item>
                <el-dropdown-item @click.native="showRelateAudioDialog(item)">关联音频</el-dropdown-item>
                <el-dropdown-item @click.native="showFramesClipDialog(item)">画面裁剪</el-dropdown-item>
               <el-dropdown-item @click.native="showPaletteDialog(item)">调色</el-dropdown-item>
                <el-dropdown-item @click.native="showLayoutSelectDialogForProgram(item)">关联布局</el-dropdown-item>
                <span class="dropdown-line"></span>
                <el-dropdown-item @click.native="editProgramsDialog(item)">编辑</el-dropdown-item>
                <el-dropdown-item @click.native="deleteProgram(item)">删除</el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </div>
        </div>
        <div class="program-empty wh100" v-if="!pageList.data.length">
          <no-data tips="暂无节目"></no-data>
        </div>
      </div>
    </div>
    <el-dialog class="relate-audio-first" title="节目关联音乐" width="700px" top="70px" :visible.sync="programAudioDialog" :close-on-click-modal="false" v-if="programAudioDialog">
      <el-table :data="relateItem.audios" style="width: 100%" stripe height="500" element-loading-spinner="el-icon-loading"
                element-loading-background="rgba(0, 0, 0, 0.8)" border row-key="id" size="small">
        <el-table-column label="音频文件名" prop="audioName"></el-table-column>
        <el-table-column label="类型">
          <template slot-scope="scope">
            <span>{{scope.row.audioType ? '音频流' : '点播'}}</span>
          </template>
        </el-table-column>
        <el-table-column label="音频时长">
          <template slot-scope="scope">
            <span>{{scope.row.duration | formatDurationMs}}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作">
          <template slot-scope="scope">
            <el-button type="info" @click="cancelRelate(scope.row)" size="mini" style="font-size: 12px">取消关联</el-button>
          </template>
        </el-table-column>
        <div class="empty-box wh100" v-if="relateItem.audios && !relateItem.audios.length" slot="empty">
          <no-data tips="暂无音频"></no-data>
        </div>
      </el-table>
      <div  slot="footer" class="dialog-footer">
        <el-button size="small" type="primary" @click="relateAudio" style="font-size: 12px">关联新文件</el-button>
      </div>
      <div class="tips">
        <p>注：</p>
        <p>1、关联多个点播文件时，单个文件播完会自动播放下一个</p>
        <p>2、关联音频直播流时，会一直播放该直播流；需要手动取消关联</p>
      </div>
    </el-dialog>
    <relate-subtitle v-if="relateSubtitleDialog" @close="relateSubtitleDialog=false" :currentProgram="currentProgram" :scheduleId="scheduleId" :currentProgramItem="currentProgramItem"></relate-subtitle>
    <relate-audio @close="showRelateAudio=false" v-if="showRelateAudio" :relateItem="relateItem" @callBack="relateCallBack"
                  :selects="relateItem.audios" :scheduleId="scheduleId"></relate-audio>
    <frames-clip @close="showFramesClip = false" v-if="showFramesClip" :currentProgramItem="currentProgramItem" :scheduleId="scheduleId"></frames-clip>
    <palette  @close="showPalette = false" v-if="showPalette" :currentProgramItem="currentProgramItem" :scheduleId="scheduleId"></palette>
    <pinp-setting @close="showPinpSetting = false" v-if="showPinpSetting" :currentProgramItem="currentProgramItem" :scheduleId="scheduleId"></pinp-setting>
    <preview-dialog v-if="previewDialog" @close="previewDialog = false" :currentProgramItem="currentProgramItem"></preview-dialog>
    <add-program v-if="showProgramDialog" @close="showProgramDialog = false" :currentItem="currentItem" :addType="3" @refresh="getPageInfo()" :calendarItem="calendarItem"></add-program>

    <!-- 布局选择弹窗 -->
    <LayoutSelectDialog
      :visible.sync="showLayoutSelectDialog"
      :target-ratio="currentLiveRatioString"
      @layout-selected="handleLayoutSelected"
      @close="handleLayoutSelectDialogClose"
    />
  </div>
</template>

<script>
  import {mapGetters, mapActions, mapMutations} from 'vuex'
  import relateSubtitle from '@/components/cgTemplate/relate-subtitle'
  import relateAudio from '@/components/base/relateAudio/relate-audio'
  import framesClip from '@/components/framesClip/frames-clip'
  import palette from '@/components/palette'
  import pinpSetting from '@/components/pinpSetting/pinp-setting'
  import previewDialog from '../programList/previewDialog'
  import cloudApi from 'api/cloud'
  import addProgram from '../addProgram'
  import layoutSelectMixin from '@/common/js/layoutSelectMixin'
  export default {
    name: 'program-list',
    components: {relateSubtitle, relateAudio, framesClip, palette, pinpSetting, previewDialog, addProgram},
    mixins: [layoutSelectMixin],
    computed: {
      getMoreBtnPosition () {
        return {top: this.btnTop + 'px', left: this.btnLeft + 'px'}
      },
      isAdd () {
        return this.programForm.id === 0
      },
      urlLabel () {
        if (this.programForm.type === 'stream') {
          return '拉流'
        } else {
          return '点播视频'
        }
      },
      getTime () {
        return (this.programForm.hour * 60 * 60 + this.programForm.minute * 60 + this.programForm.second) * 1000
      },
      ...mapGetters([
        'roleType',
        'programs',
        'cgs',
        'programsSn',
        'audio'
      ])
    },
    data () {
      return {
        calendarItem: null,
        search: {
          name: ''
        },
        btnTop: 0,
        btnLeft: 0,
        moreBtnArr: [
          {id: 1, text: '画中画'},
          {id: 2, text: '画面裁剪'},
          {id: 3, text: '编辑'},
          {id: 4, text: '复制'},
          {id: 5, text: '删除'}
        ],
        programAudioDialog: false,
        scale: 1,
        showRelateAudio: false,
        pageList: {
          data: [],
          page: this.Page()
        },
        isSlotProgram: false,
        slotIndex: -1,
        showProgramDialog: false,
        relateSubtitleDialog: false,
        adding: false,
        programForm: {
          id: 0,
          name: '',
          startTime: '', // 播出时间，本地时间，格式为"2006-01-02 15:04:05"，仅节目表第一个节目有效
          hour: 0, // 节目小时
          minute: 0, // 节目分钟
          second: 0, // 节目秒
          type: 'stream', // "stream"：直播；"record"：录播
          url: '',
          cgIds: [],
          audios: [],
          orderNum: undefined // 节目管理列表排序
        },
        programRules: {
          name: [
            {required: true, message: '请输入节目名称', trigger: 'blur'}
          ],
          url: [
            {required: true, message: '请输入地址', trigger: 'blur'}
          ]
        },
        importData: [],
        currentProgram: '',
        scheduleId: 0,
        relateItem: {},
        currentProgramItem: {},
        showFramesClip: false,
        showPinpSetting: false,
        showPalette: false,
        previewDialog: false
      }
    },
    methods: {
      numberToWeek (number) {
        let text = ''
        switch (number) {
          case 1:
            text = '一'
            break
          case 2:
            text = '二'
            break
          case 3:
            text = '三'
            break
          case 4:
            text = '四'
            break
          case 5:
            text = '五'
            break
          case 6:
            text = '六'
            break
          case 7:
            text = '日'
            break
        }
        return text
      },
      getDateText (daysOfWeek) {
        let text = ''
        if (!daysOfWeek || !daysOfWeek.length) {
          return text
        }
        daysOfWeek.forEach((v, i) => {
          if (i) {
            text += '/'
          }
          text += this.numberToWeek(v)
        })
        return '(' + text + ')'
      },
      openCalendar () {
        if (this.roleType === 2) {
          this.$bus.$emit('showCalendarView', false)
        } else {
          this.$bus.$emit('showCalendarView', true)
        }
      },
      addProgramDialog (item) {
        this.calendarItem = item
        this.currentItem = null
        this.showProgramDialog = true
      },
      getPageInfo () {
        this.pageList.data = []
        let param = {}
        cloudApi.getProgramArrangement(param, res => {
          if (res && res.length) {
            this.pageList.data = res
            this.pageList.data.forEach(v => {
              v.program = JSON.parse(v.program)
            })
          }
        }, (code, msg) => {
          this.$message.error(msg)
        })
      },
      showPreview (item) {
        this.currentProgramItem = item.program
        this.previewDialog = true
      },
      showPinpDialog (item) {
        this.scheduleId = item.id
        this.currentProgramItem = item.program
        this.showPinpSetting = true
      },
      showFramesClipDialog (item) {
        this.scheduleId = item.id
        this.currentProgramItem = item.program
        this.showFramesClip = true
      },
      showPaletteDialog (item) {
        this.currentProgramItem = item.program
        this.showPalette = true
      },
      getCgName (id) {
        let index = this.cgs.findIndex(item => {
          return item.id === id
        })
        if (index >= 0) {
          return this.cgs[index].name
        } else {
          return '该CG已删除'
        }
      },
      relateCallBack (editProgram) {
        this.relateItem = editProgram
        this.getPageInfo()
      },
      async cancelRelate (item) {
        let editProgram = this.relateItem
        editProgram.audios.forEach((v, i) => {
          if (v.audioId === item.audioId) {
            editProgram.audios.splice(i, 1)
          }
        })
        await this.editApiConfirm(editProgram).then(() => {
          this.getPageInfo()
          this.relateItem = editProgram
          this.$message.success('取消关联成功')
        })
      },
      editApiConfirm (editProgram) {
        return new Promise((resolve, reject) => {
          let param = {
            id: this.scheduleId,
            program: JSON.stringify(editProgram)
          }
          cloudApi.uploadProgramArrangementById(param, () => {
            resolve()
          }, () => {
            reject(new Error(''))
          })
        })
      },
      showRelateAudioDialog (item) {
        this.scheduleId = item.id
        this.programAudioDialog = true
        this.relateItem = item.program
      },
      relateAudio () {
        this.showRelateAudio = true
      },
      relateSubtitleShow (item) {
        this.scheduleId = item.id
        this.currentProgram = item.program.id
        this.currentProgramItem = item.program
        this.relateSubtitleDialog = true
      },
      splicePrograms (data) {
        let content = this
        let splicePrograms = []
        let topName = ['排列序号', '节目名称', '媒体机构', '节目时长/秒', '直播方式', '流地址', '备注']
        for (let i = 0; i < data.length; i++) {
          for (const dataKey in data[i]) {
            if (topName.indexOf(dataKey) < 0) {
              return content.$message.error('请使用模板文件规范填写资料')
            }
          }
          splicePrograms.push({
            id: '',
            name: '',
            startTime: '',
            duration: 0,
            type: 'stream',
            url: '',
            orderNum: ''
          })
          splicePrograms[i].id = content.$_createRandomId()
          splicePrograms[i].name = data[i]['节目名称'] ? data[i]['节目名称'] + '' : ''
          splicePrograms[i].duration = data[i]['节目时长/秒'] * 1000
          splicePrograms[i].type = data[i]['直播方式'] === '拉流' ? 'stream' : 'record'
          splicePrograms[i].url = data[i]['流地址']
          splicePrograms[i].orderNum = data[i]['排列序号']
        }
        return splicePrograms
      },
      checkForm (data) {
        let content = this
        let flag = true
        for (let i = 0; i < data.length; i++) {
          if (!data[i].name) {
            content.$message.warning(`第${i + 2}行中的节目名称不能为空`)
            flag = false
            break
          } else if (!data[i].duration) {
            flag = false
            content.$message.warning(`第${i + 2}行中的节目时长不能为空`)
            break
          } else if (!data[i].type) {
            flag = false
            content.$message.warning(`第${i + 2}行中的直播方式不能为空`)
            break
          } else if (!data[i].url) {
            flag = false
            content.$message.warning(`第${i + 2}行中的流地址不能为空`)
            break
          } else if (data[i].orderNum && typeof data[i].orderNum !== 'number') {
            flag = false
            content.$message.warning(`第${i + 2}行中的排列序号不是数字`)
            break
          } else if (data[i].orderNum && typeof data[i].orderNum === 'number' && (data[i].orderNum > 200 || data[i].orderNum < 1)) {
            flag = false
            content.$message.warning(`第${i + 2}行中的排列序号需要在1-200之间`)
            break
          }
        }
        return flag
      },
      editProgramsDialog (item) {
        this.calendarItem = null
        this.scheduleId = item.id
        this.showProgramDialog = true
        this.currentItem = item
      },
      deleteProgram (item) {
        this.$confirm('确定删除该节目吗?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          cloudApi.deleteProgramArrangement({id: item.id}, () => {
            this.getPageInfo()
            this.$message.success('删除成功')
          })
        }).catch(() => {
        })
      },
      ...mapMutations({
        setProgramsData: 'SET_PROGRAMS',
        setProgramsSn: 'SET_PROGRAMS_SN'
      }),
      ...mapActions(['getPrograms', `delProgram`, 'setRepeat', 'modProgram', 'cutProgram'])
    },
    mounted () {
      this.$bus.$on('refreshProgramManage', () => {
        this.getPageInfo()
      })
      this.$bus.$on('programFromCalendar', item => {
        if (item.editId) {
          let findItem = this.pageList.data.find(v => v.id === item.editId)
          this.editProgramsDialog(findItem)
        } else {
          this.addProgramDialog(item)
        }
      })
    },
    created () {
      this.getPageInfo()
    }
  }
</script>

<style scoped lang="scss">
  .program-list {
    height: 100%;
    position: relative;
    font-size: 16px;
    .more-dialog {
      position: absolute;
      width: 100px;
      background: #FFFFFF;
      box-shadow: 0px 3px 8px 0px rgba(0, 0, 0, 0.28);
      border-radius: 4px;
      overflow: hidden;

      .more-li {
        width: 100%;
        height: 32px;
        line-height: 32px;
        padding-left: 19px;
        box-sizing: border-box;
        color: #222222;
        cursor: pointer;
        &:hover {
          color: $color-theme;
          background: #FFEBE3;
        }
      }
    }
    .program-wrapper {
      height: calc(100% - 50px);
      box-sizing: border-box;
    }
    .top-area {
      display: flex;
      justify-content: flex-end;
      align-items: center;
      height: 50px;
      padding: 7px 15px;
      .program-tips {
        margin-left: 17px;
        font-size: 14px;
        font-family: SimSun;
        color: #677187;
      }
    }

    .program-ul {
      height: calc(100% - 60px);
      overflow-y: auto;
      overflow-x: hidden;
      .program-li {
        position: relative;
        display: flex;
        justify-content: flex-start;
        margin: 2px 0;
        padding: 6px 0;
        background: #292E37;
        box-sizing: border-box;
        height: 90px;

        &:hover {
          background: #3E4252;
        }
      }
    }
    .list-header {
      display: flex;
      justify-content: flex-start;
      align-items: center;
      color: #646D80;
      font-size: 16px;
      box-sizing: border-box;
      height: 46px;
    }
    .audio {
      display: flex;
      align-items: center;
      flex: 0 0 13%;
      width: 13%;
      padding-right: 20px;
      box-sizing: border-box;
      .audio-ul {
        width: 100%;
        max-height: 78px;
        display: flex;
        flex-direction: column;
        overflow-y: auto;
      }
      .audio-li {
        flex-shrink: 0;
        display: inline-block;
        width: 100%;
        @include elliptical();
      }
    }
    .cg-name {
      display: flex;
      align-items: center;
      flex: 0 0 13%;
      width: 13%;
      padding-right: 20px;
      box-sizing: border-box;
      .cg-name-ul {
        width: 100%;
        max-height: 78px;
        display: flex;
        flex-direction: column;
        overflow-y: auto;
      }
      .cg-name-li {
        display: inline-block;
        width: 100%;
        flex-shrink: 0;
        @include elliptical();
      }
    }
    .program-options {
      display: flex;
      align-items: center;
      flex: 0 0 12%;
      width: 12%;
    }
    .program-time {
      display: flex;
      align-items: center;
      flex: 0 0 15%;
      width: 15%;
      text-indent: 30px;
    }
    .program-checked {
      display: flex;
      align-items: center;
      justify-content: center;
      flex: 0 0 5%;
      width: 5%;
    }
    .program-number {
      display: flex;
      align-items: center;
      justify-content: center;
      flex: 0 0 12%;
      width: 12%;
      white-space: nowrap;
    }
    .program-name {
      display: flex;
      align-items: center;
      text-indent: 40px;
      flex: 0 0 20.5%;
      width: 20.5%;
      padding-right: 40px;
      box-sizing: border-box;
      .name {
        display: inline-block;
        width: 200px;
        @include elliptical();
      }
    }
    .program-duration {
      display: flex;
      align-items: center;
      flex: 0 0 10%;
      width: 10%;
    }
  }

  .text-btn {
    color: #627FC2;
  }
  ::v-deep .el-checkbox__label {
    padding-left: 0!important;
  }
  ::v-deep .el-input-group__append {
    background-color: #535D72;
  }
  ::v-deep .el-checkbox__input {
    transform: scale(1.3);
  }
  ::v-deep .el-table td.el-table__cell {
    border-bottom: 1px solid #434A58;
  }
  .el-icon-warning {
    color: #B2BFDE;
  }
  .tips {
    position: absolute;
    bottom: 25px;
    color: #8892A7;
  }
  ::v-deep .el-button--text:focus {
    color: #627FC2;
  }
  ::v-deep .el-button--text:hover {
    color: #FF5B0C;
  }
  ::v-deep .orderInput.el-input-number .el-input__inner {
    text-align: left;
  }
  .dropdown-line {
    display: inline-block;
    height: 9px;
    width: 100%;
    border-top: 1px solid #E6E6E6;
  }
  ::v-deep .el-button {
  font-size: 16px;
  }
</style>
