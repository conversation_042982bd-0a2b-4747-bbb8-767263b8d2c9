import cloudApi from 'api/cloud'
import webrtcMgr from 'common/js/webrtc-manager'

class ChannelVideo {
  constructor (video, program, audio, defaultPic, cgs, callback, failback) {
    this.$_program = program
    this.$_audio = audio
    this.$_defaultPic = defaultPic
    this.$_cgs = cgs
    if (typeof video === 'string') {
      this.el = document.getElementById(video)
    } else {
      this.el = video
    }
    let pc = new RTCPeerConnection({})
    pc.onconnectionstatechange = event => {
      if (pc.connectionState === 'disconnected') {
        this.connect(pc, callback, failback)
      }
    }
    pc.onicecandidate = event => {
    }
    pc.addTransceiver('video')
    pc.addTransceiver('audio')
    this.sdpFailCount = 0 // 获取sdp失败次数
    this.connect(pc, callback, failback)
    webrtcMgr.registerPreview(pc)
    pc.ontrack = event => {
      this.el.srcObject = event.streams[0]
      this.el.style = 'display: block'
    }
  }

  connect (pc, callback, failback) {
    pc.createOffer()
        .then(d => {
          pc.setLocalDescription(d, () => {
            this.getSdp(pc, callback, failback)
          })
        })
  }

  getSdp (pc, callback, failback) {
    cloudApi.getPreviewVideo({program: this.$_program, audio: this.$_audio, defaultPic: this.$_defaultPic, cgs: this.$_cgs}, pc.localDescription, (message) => {
      if (message === '' && this.sdpFailCount < 5) {
        this.sdpFailCount++
        setTimeout(() => {
          this.getSdp(pc, callback)
        }, 500)
        return
      }
      try {
        pc.previewId = message.previewId
        pc.setRemoteDescription(new RTCSessionDescription(JSON.parse(atob(message.sdp))))
        if (typeof callback === 'function') {
          callback(message)
        }
      } catch (e) {
        console.log('setRemoteDescription error:' + e)
      }
    }, (code, msg) => {
      failback(msg)
    })
  }
}

export default ChannelVideo
