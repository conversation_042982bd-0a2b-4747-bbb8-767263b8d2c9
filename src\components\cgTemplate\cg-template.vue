<template>
  <div class="cg-template">
    <div class="cg-list">
      <ul class="cg-container">
        <li class="showCg-item" :class="{'is-vertical': isVertical}" :style="getItemWidth()" :key="item.id" v-for="(item, index) in cgsFromType">
          <div class="mask">
          <div class="cg-item">
            <i class="closeCgicon el-icon-delete-solid" @click.stop="deleteItem(item, index)"></i>
            <div class="showCg-preview" :class="{'is-vertical': isVertical}" v-if="item.type === 'clock'">
              <div class="cg-ratio">{{item.displayWidth + '*' + item.displayHeight}}</div>
              <div class="pos-abs" :style="previewStyle(item)">
                <div style="position: relative;height: 100%;width: 100%">
                  <div class="pos-abs" :style="clockStyle(item.obj)" :key="item.obj.id">
                    <div class="pos-abs" :style="clockStyleDiv(item.obj)">
                      <div :style="textStyle(item.obj)">
                        <span v-show="item.obj.text === '15:04:05'" style="white-space: nowrap">00:00:00</span>
                        <span v-show="item.obj.text === '2006-01-02'" style="white-space: nowrap">2001-01-01</span>
                        <span v-show="item.obj.text === '2006-01-02 15:04:05'" style="white-space: nowrap">2001-01-01 00:00:00</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="showCg-preview" :class="{'is-vertical': isVertical}" v-if="item.type === 'timerUp' || item.type === 'timerDown'">
              <div class="cg-ratio">{{item.displayWidth + '*' + item.displayHeight}}</div>
              <div :style="timerStyle(item)">
                <template v-if="!item.obj.box">
                  <span>00</span>
                  <span v-show="item.obj.text.includes('day')" :style="{fontSize: item.obj.fontSize / 5 * 3 + 'px'}">天</span>
                  <span v-show="item.obj.text === 'hour:minute:second'">:</span>
                  <span v-show="item.obj.text !== 'day'">00</span>
                  <span v-show="item.obj.text === 'day hour:minute'" :style="{fontSize: item.obj.fontSize / 5 * 3 + 'px'}">时</span>
                  <span v-show="item.obj.text === 'hour:minute:second'">:</span>
                  <span v-show="item.obj.text !== 'day'">00</span>
                  <span v-show="item.obj.text === 'day hour:minute'" :style="{fontSize: item.obj.fontSize / 5 * 3 + 'px'}">分</span>
                </template>
                <template v-else>
                  <span class="hasBorder" :style="{backgroundColor: '#' + item.obj.boxColor}">00</span>
                  <span v-show="item.obj.text.includes('day')" :style="{fontSize: item.obj.fontSize / 5 * 3 + 'px'}">天</span>
                  <span v-show="item.obj.text === 'hour:minute:second'">:</span>
                  <span class="hasBorder" v-show="item.obj.text !== 'day'" :style="{backgroundColor: '#' + item.obj.boxColor}">00</span>
                  <span v-show="item.obj.text === 'day hour:minute'" :style="{fontSize: item.obj.fontSize / 5 * 3 + 'px'}">时</span>
                  <span v-show="item.obj.text === 'hour:minute:second'">:</span>
                  <span class="hasBorder" v-show="item.obj.text !== 'day'" :style="{backgroundColor: '#' + item.obj.boxColor}">00</span>
                  <span v-show="item.obj.text === 'day hour:minute'" :style="{fontSize: item.obj.fontSize / 5 * 3 + 'px'}">分</span>
                </template>
              </div>
            </div>
            <div class="showCg-preview" v-if="item.type === 'weather'" :class="{'is-vertical': liveRatio < 1}">
              <div class="cg-ratio">{{item.displayWidth + '*' + item.displayHeight}}</div>
              <div class="pos-abs" :style="weatherStyle(item)">
                <weather :currentCgInfo="item" class="preview-weather"></weather>
              </div>
            </div>
            <div class="showCg-preview" :class="{'is-vertical': isVertical}" v-else-if="item.type === 'movingSubtitle'">
              <div class="cg-ratio">{{item.displayWidth + '*' + item.displayHeight}}</div>
              <div class="pos-abs movingSubtitle" :style="previewStyle(item)">
                <div style="position: relative;height: 100%;width: 100%">
                  <div class="pos-abs" :style="subTitleStyle(item.obj)" :key="item.obj.id">
                    <div class="pos-abs" :style="movingSubtitleStyleDiv(item.obj)">{{item.obj.text}}</div>
                  </div>
                </div>
              </div>
            </div>
            <div class="showCg-preview" :class="{'is-vertical': isVertical}" v-else-if="item.type === 'subtitle'">
              <div class="cg-ratio">{{item.displayWidth + '*' + item.displayHeight}}</div>
              <div class="pos-abs" :style="previewStyle(item)">
                <div style="position: relative;height: 100%;width: 100%;overflow:hidden;" v-if="item.obj">
                  <template v-for="val in item.obj.layers">
                    <div class="pos-abs" :style="clockStyle(val)" v-if="val.type === 'clock'" :key="val.id">
                      <div class="pos-abs" :style="clockStyleDiv(val)">
                        <div :style="textStyle(val)" v-html="val.text"></div>
                      </div>
                    </div>
                    <img class="pos-abs" :src="$_getPicture(val.url)" :style="imgStyle(val)" alt="" :key="val.id" v-if="val.type === 'logo'">
                    <div class="pos-abs" :style="subTitleStyle(val)" :key="val.id" v-if="val.type === 'subtitle'">
                      <div class="pos-abs" :style="subTitleStyleDiv(val)">
                        <div :style="textStyle(val)" v-html="val.text"></div>
                      </div>
                    </div>
                  </template>
                </div>
              </div>
            </div>
            <div class="showCg-preview" :class="{'is-vertical': isVertical}" v-else-if="item.type === 'gif'">
              <div class="cg-ratio">{{item.displayWidth + '*' + item.displayHeight}}</div>
              <div class="pos-abs" :style="previewStyle(item)">
                <div style="position: relative;height: 100%;width: 100%;overflow:hidden;" v-if="item.obj">
                  <img class="pos-abs" :src="$_getPicture(item.obj.url)" :style="gifStyle(item)">
                </div>
              </div>
            </div>
            <div class="showCg-option">
              <span class="button-dark cg-choose" :class="{active: item.selected}" @click.stop="cgUpDown(item, index, 'pgm')">
                <i class="icon el-icon-top"></i>
                <span>直播</span>
              </span>
              <span class="button-dark" @click="editCg(item)">
                <span class="icon el-icon-s-tools"></span>
              </span>
            </div>
            <div class="showCg-title" :title="item.name">
              {{item.name || '-'}}
            </div>
          </div>
          </div>
        </li>
        <li class="empty-box" :style="getItemWidth()"></li>
        <li class="empty-box" :style="getItemWidth()"></li>
        <li class="empty-box" :style="getItemWidth()"></li>
        <li class="empty-box" :style="getItemWidth()"></li>
        <li class="empty-box" :style="getItemWidth()"></li>
        <div class="empty-box wh100" style="height: 100%" v-if="cgsFromType && !cgsFromType.length">
          <no-data tips="暂无LOGO"></no-data>
        </div>
      </ul>
    </div>
    <div class="bottom-btn-area">
      <span class="g-option-btn" @click="addCd()">
        <i class="icon icon-fount-cg-add"></i>
        <span>添加</span>
      </span>
      <span class="g-option-btn" @click="clearCg" v-if="false">
        <i class="icon icon-fount-cg-clear"></i>
        <span>清空</span>
      </span>
    </div>
    <subtitle-cg-dialog @updateCg="updateCg" :cgInfo="cgInfo" :isEditCg="isEditCg" v-if="showSubtitleDialog" @close="showSubtitleDialog=false" :usedType="0"></subtitle-cg-dialog>
  </div>
</template>

<script>
  import {mapGetters, mapMutations} from 'vuex'
  import subtitleCgDialog from './subtitle-cg-dialog'
  import liveApi from 'api/live'
  import weather from 'components/cgTemplate/weather'

  let scale = 0
  let cgItemHeight = 94
  export default {
    name: 'cg-template',
    components: {subtitleCgDialog, weather},
    computed: {
      ...mapGetters([
        'cgs',
        'liveWidth',
        'liveHeight',
        'liveRatio'
      ])
    },
    data () {
      return {
        showSubtitleDialog: false,
        isEditCg: false,
        cgInfo: null,
        cgsFromType: [],
        isVertical: false
      }
    },
    methods: {
      updateCg () {
        this.getInit()
      },
      clearCg () {
        if (!this.cgs || !this.cgs.length) {
          return false
        }
        let cgsSelected = this.cgs.filter(item => item.selected)
        if (cgsSelected.length) {
          cgsSelected.forEach((item, index) => {
            let param = {id: item.id}
            liveApi.deSelectCg(param, () => {
              if (index === cgsSelected.length - 1) {
                this.clearPgmCgs()
              }
            })
          })
        }
      },
      selectCg (item, index) {
        liveApi.selectCg({id: item.id}, () => {
          let cgItem = this.$_deepCopy(item)
          cgItem.selected = true
          this.setCgs({index: index, deleteNum: 1, cgItem: cgItem, id: item.id})
          this.getInit()
        }, (code, msg) => {
          this.$message.error(msg)
        })
      },
      deSelectCg (item, index) {
        liveApi.deSelectCg({id: item.id}, () => {
          let cgItem = this.$_deepCopy(item)
          cgItem.selected = false
          this.setCgs({index: index, deleteNum: 1, cgItem: cgItem, id: item.id})
          this.getInit()
        }, (code, msg) => {
          this.$message.error(msg)
        })
      },
      cgUpDown (item, index) {
        if (item.selected) return this.deSelectCg(item, index)
        return this.selectCg(item, index)
      },
      imgStyle (item) {
        return {
          left: item.dx * scale + 'px',
          top: item.dy * scale + 'px',
          opacity: item.refract ? item.refract / 100 : 1,
          width: item.width * scale + 'px',
          height: item.height * scale + 'px'
        }
      },
      gifStyle (item) {
        return {
          width: item.width * scale + 'px',
          height: item.height * scale + 'px'
        }
      },
      deleteItem (item, index) {
        this.$confirm('确定删除吗?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          let param = {id: item.id}
          liveApi.removeCg(param, () => {
            this.setCgs({index: index, deleteNum: 1, id: item.id})
            this.getInit()
            this.$message.success('删除成功')
          }, (code, msg) => {
            this.$message.error(msg)
          })
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
      },
      getBoxColor (backgroundColor, bgOpacity) {
        return backgroundColor.substr(1, 6) + this.$_parseOpacity(bgOpacity)
      },
      indexSort (prop) {
        return function (a, b) {
          let index1 = a[prop]
          let index2 = b[prop]
          return index1 - index2
        }
      },
      getLayers (tempObj) {
        let scale = 640 / this.liveWidth
        return Object.values(tempObj.list).map(item => {
          let baseObj = {
            dx: item.left / scale,
            dy: item.top / scale,
            height: item.h / scale,
            width: item.w / scale,
            refract: 100 / scale,
            zIndex: item.zIndex
          }
          if (item.fontFamily) {
            return Object.assign(baseObj, {
              boldItalic: '',
              boxColor: this.getBoxColor(item.bgColor, item.bgOpacity),
              color: item.color,
              font: item.fontFamily,
              position: this.$_getPosition(item.horizon, item.vertical),
              size: parseInt(item.size / scale),
              text: item.text,
              type: 'subtitle'
            })
          } else {
            return Object.assign(baseObj, {
              type: 'logo',
              url: item.url
            })
          }
        })
      },
      moveTemplateVal (tempObj) {
        let scale = 640 / this.liveWidth
        let layers = this.getLayers(tempObj)
        return {
          dx: tempObj.container.left / scale,
          dy: tempObj.container.top / scale,
          height: tempObj.container.h / scale,
          id: parseInt(Math.random() * 1000000),
          loop: Number(tempObj.container.animate.loop),
          obj: layers[0],
          pgmFlag: false,
          pvwFlag: false,
          startType: 0,
          type: 'movingSubtitle',
          width: tempObj.container.w / scale
        }
      },
      subtitleTemplateVal (tempObj) {
        let scale = 640 / this.liveWidth
        let layers = this.getLayers(tempObj).sort(this.indexSort('zIndex'))
        return {
          dx: tempObj.container.left / scale,
          dy: tempObj.container.top / scale,
          height: tempObj.container.h / scale,
          id: parseInt(Math.random() * 1000000),
          loop: 0,
          obj: {
            layers: layers
          },
          pgmFlag: false,
          pvwFlag: false,
          startType: 0,
          type: 'subtitle',
          width: tempObj.container.w / scale
        }
      },
      editCgTemplate (cgTemplateItem) {
        let cgInfo
        if (Number(cgTemplateItem.contentObj.cg640.container.animate.start) === 1) {
          cgInfo = this.moveTemplateVal(cgTemplateItem.contentObj.cg640)
        } else {
          cgInfo = this.subtitleTemplateVal(cgTemplateItem.contentObj.cg640)
        }
        this.addCd(cgInfo)
      },
      addCd (cgInfo) {
        this.isEditCg = false
        this.cgInfo = cgInfo
        this.showSubtitleDialog = true
      },
      editCg (item) {
        this.isEditCg = true
        this.cgInfo = item
        this.showSubtitleDialog = true
      },
      getItemWidth () {
        return {width: cgItemHeight * this.liveRatio + 'px', flexBasis: cgItemHeight * this.liveRatio + 'px'}
      },
      previewStyle (item) {
        return {
          width: item.width * scale + 'px',
          height: item.height * scale + 'px',
          left: item.dx * scale + 'px',
          top: item.dy * scale + 'px',
          position: 'absolute'
        }
      },
      timerStyle (item) {
        let styles = {
          zoom: scale,
          position: 'absolute',
          left: item.dx + 'px',
          fontSize: item.obj.fontSize + 'px',
          top: item.dy + 'px',
          width: item.width + 'px',
          height: item.height + 'px',
          color: '#' + item.obj.fontColor
      }
        return styles
      },
      weatherStyle (item) {
        let styles = {
          zoom: scale,
          position: 'absolute',
          fontSize: item.obj.fontSize + 'px',
          color: '#' + item.obj.fontColor,
          transform: `scale(${item.obj.scale})`
        }

        if (item.obj.coordinates === 2) {
          styles.left = item.dx + 'px'
          styles.top = item.dy + 'px'
          styles.transformOrigin = 'top left'
        } else if (item.obj.coordinates === 0) {
          styles.right = (item.displayWidth - item.width * item.obj.scale) - item.dx + 'px'
          styles.top = item.dy + 'px'
          styles.transformOrigin = 'top right'
        } else if (item.obj.coordinates === 1) {
          styles.right = (item.displayWidth - item.width * item.obj.scale) - item.dx + 'px'
          styles.bottom = (item.displayHeight - item.height * item.obj.scale) - item.dy + 'px'
          styles.transformOrigin = 'bottom right'
        } else if (item.obj.coordinates === 3) {
          styles.left = item.dx + 'px',
            styles.bottom = (item.displayHeight - item.height * item.obj.scale) - item.dy + 'px'
          styles.transformOrigin = 'bottom left'
        }
        return styles
      },
      clockStyle (item) {
        let styles = {
          left: item.dx * scale + 'px',
          fontSize: item.size + 'px',
          top: item.dy * scale + 'px',
          opacity: item.refract / 100,
          width: item.width * scale + 'px',
          height: item.height * scale + 'px',
          color: '#' + item.color,
          fontFamily: item.font
        }
        let boldItalic = this.getBoldItalic(item.boldItalic)
        return Object.assign({}, styles, boldItalic)
      },
      clockStyleDiv (item) {
        return {
          transform: 'scale(' + scale + ')',
          width: item.width + 'px',
          height: item.height + 'px',
          marginLeft: '-' + item.width * (1 - scale) / 2 + 'px',
          marginTop: '-' + item.height * (1 - scale) / 2 + 'px'
        }
      },
      getBoldItalic (str) {
        let styles = {}
        if (str.indexOf('B') >= 0) {
          styles = {fontWeight: 'bold'}
        }
        if (str.indexOf('I') >= 0) {
          styles = {fontStyle: 'italic'}
        }
        return styles
      },
      subTitleStyle (item) {
        let styles = {
          left: item.dx * scale + 'px',
          fontSize: item.size + 'px',
          top: item.dy * scale + 'px',
          opacity: item.refract / 100,
          width: item.width * scale + 'px',
          height: item.height * scale + 'px',
          color: '#' + item.color,
          fontFamily: item.font,
          backgroundColor: this.$_colorRgba(item.boxColor)
        }
        let boldItalic = this.getBoldItalic(item.boldItalic)
        return Object.assign({}, styles, boldItalic)
      },
      textStyle (item) {
        let styles = {
          display: 'table-cell',
          wordBreak: 'break-all',
          width: item.width + 'px',
          height: item.height + 'px'
        }
        let flexStyle = this.getAlignStyle(item.position)
        return Object.assign({}, styles, flexStyle)
      },
      getVerticalAlign (position) {
        if ([1, 2, 3].includes(position)) return 'top'
        if ([4, 5, 6].includes(position)) return 'middle'
        return 'bottom'
      },
      getTextAlign (position) {
        if ([1, 4, 7].includes(position)) return 'left'
        if ([2, 5, 8].includes(position)) return 'center'
        return 'right'
      },
      getAlignStyle (position) {
        let style = {}
        style.verticalAlign = this.getVerticalAlign(position)
        style.textAlign = this.getTextAlign(position)
        return style
      },
      movingSubtitleStyleDiv (item) {
        return {
          transform: 'scale(' + scale + ')',
          width: item.width + 'px',
          height: item.height + 'px',
          marginLeft: '-' + item.width * (1 - scale) / 2 + 'px',
          marginTop: '-' + item.height * (1 - scale) / 2 + 'px',
          whiteSpace: 'nowrap'
        }
      },
      subTitleStyleDiv (item) {
        return {
          transform: 'scale(' + scale + ')',
          width: item.width + 'px',
          height: item.height + 'px',
          marginLeft: '-' + item.width * (1 - scale) / 2 + 'px',
          marginTop: '-' + item.height * (1 - scale) / 2 + 'px'
      }
      },
      getInit () {
        let cgs = this.cgs.filter(item => {
          return item.usedType === 0
        })
        this.cgsFromType = this.$_deepCopy(cgs)
      },
      ...mapMutations({
        clearPgmCgs: 'CLEAR_PGM_CGS',
        setCgs: 'SET_CGS'
      })
    },
    mounted () {
      this.$bus.$on('initLogoList', this.getInit)
    },
    created () {
      this.getInit()
      if (this.liveRatio < 1) {
        this.isVertical = true
        cgItemHeight = 200
      } else {
        cgItemHeight = 94
      }
      scale = cgItemHeight / this.liveHeight
    }
  }
</script>

<style scoped lang="scss">
  .cg-template {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    height: 100%;
    width: 100%;
    .tab-title {
      width: 30%;
      flex: 0 0 40px;
      height: 40px;
      margin-left: 20px;
    }
    .bottom-btn-area {
      margin-left: 20px;
      line-height: 50px;
      flex: 0 0 50px;
      height: 50px;
    }
    .cg-list {
      height: calc(100% - 50px);
      overflow: hidden;
      background-color: #232631;
      margin: 0 13px;
      .cg-container {
        display: flex;
        justify-content: space-around;
        flex-wrap: wrap;
        position: relative;
        width: 100%;
        height: 100%;
        overflow-y: auto;
        overflow-x: hidden;
        padding-top: 10px;
        box-sizing: border-box;
      }
      .empty-box {
        height: 0;
        margin: 0 5px;
      }
      .showCg-item {
        margin-bottom: 5px;
        padding: 5px 5px;
        height: 146px;
        &.is-vertical {
          height: 240px;
        }
        &:hover {
          background-color: rgba(52, 58, 70, 1);
        }
        .cg-item {
          position: relative;
          cursor: pointer;
          .ai-img {
            position: relative;
            top: 3px;
            width: 100%;
            height: 100%;
          }
          &:hover {
            .closeCgicon {
              display: block;
            }
          }
          .closeCgicon {
            display: none;
            position: absolute;
            width: 25px;
            height: 25px;
            line-height: 25px;
            right: 0;
            top: 0;
            text-align: center;
            z-index: 1;
            cursor: pointer;
            color: #fff;
            background-color: #FF2A2A;
            font-size: 16px;
          }
          .showCg-preview {
            height: 94px;
            position: relative;
            background-image: url(./img/blank.png);
            overflow: hidden;
            .cg-ratio {
              position: absolute;
              bottom: 5px;
              left: 5px;
              padding: 2px 5px;
              border-radius: 10px;
              z-index: 200;
              background-color: rgba(0,0,0, .4);
            }
            &.is-vertical {
              height: 200px;
            }
            .pos-abs {
              position: absolute;
              &.movingSubtitle {
                left: 0 !important;
                width: 100% !important;
                text-align: center;
                .pos-abs {
                  width: 100% !important;
                  margin-left: 0 !important;
                }
              }
            }
          }
          .showCg-option {
            display: flex;
            height: 22px;
            border-top: 1px solid rgb(40, 45, 55);
            color: rgb(177, 186, 207);
            font-size: 12px;
            background-color: rgb(76, 84, 101);
            z-index: 10;
            .button-dark {
              @include flex-c-c();
              flex: 1;
              height: 100%;
              text-align: center;
              box-sizing: border-box;
              border-right: 1px solid $color-divider;
              cursor: pointer;
              color: rgb(177, 186, 207);
              background-image: linear-gradient(to bottom, rgb(81, 89, 107) 0px, rgb(61, 67, 82) 100%);
              &:hover {
                background-image: linear-gradient(to bottom, #5e6678 0, #4c5261 100%);
              }
              &.cg-choose {
                &.active {
                  color: #ff631e;
                  background-image: linear-gradient(to bottom, #464c5c 0, #343a46 100%);
                }
              }
            }
            .icon {
              font-size: 16px;
              font-weight: bold;
            }
          }
          .showCg-title {
            padding: 6px 0;
            text-align: center;
            @include elliptical();
          }
          .mask {
            padding: 10px;
            background-color: rgba(255, 255, 255, .3);
            box-sizing: border-box;
          }
        }
      }
    }
  }
  .hasBorder {
    display: inline-block;
    padding: 0px 8px;
    border-radius: 2px;
  }
</style>
