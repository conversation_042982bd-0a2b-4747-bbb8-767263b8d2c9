<template>
  <div>
    <el-dialog class="relate-subtitle" title="关联字幕" width="800px" top="70px" heigh="800px" :visible.sync="showDialog" :close-on-click-modal="false"
               @close="$emit('close')">
      <cg-subtitle-list :programType="programType" :scheduleId="scheduleId" :manageId="manageId" :type="'relate'" :currentProgram="currentProgram" :currentProgramItem="currentProgramItem" ref="subtitle"></cg-subtitle-list>
      <span slot="footer" class="dialog-footer">
        <el-button type="info" class="add-relate-subtitle el-icon-plus" @click="addRelateSubtitle" style="font-size: 14px">添加关联字幕</el-button>
      </span>
    </el-dialog>
    <el-dialog class="relate-subtitle" title="添加关联字幕" width="800px" top="70px" heigh="800px" :visible.sync="showAddDialog" :close-on-click-modal="false"
               @close="showAddDialog=false">
      <cg-subtitle-list :programType="programType" :scheduleId="scheduleId" :manageId="manageId" @updateRelate="updateRelate" :type="'relate'" :isAdd="true" :currentProgramItem="currentProgramItem" :currentProgram="currentProgram" ref="addSubtitle" @close="showAddDialog = false" v-if="showAddDialog"></cg-subtitle-list>
      <span slot="footer" class="dialog-footer">
        <el-button type="text" class="el-icon-plus text-btn" @click="addSubtitleFile">新增字幕文件</el-button>
        <span>
          <el-button type="primary" size="small" @click="confirmAdd">确 定</el-button>
          <el-button type="info" size="small" @click="showAddDialog=false">取 消</el-button>
        </span>
      </span>
    </el-dialog>
  </div>
</template>

<script>
  import cgSubtitleList from './cg-subtitle-list'

  export default {
    name: 'relate-subtitle',
    components: {cgSubtitleList},
    props: {
      programType: {
        type: String,
        default: ''
      },
      currentProgramItem: {
        type: Object,
        default: () => {}
      },
      scheduleId: {
        type: Number,
        default: 0
      },
      manageId: {
        type: Number,
        default: 0
      },
      currentProgram: {
        type: String,
        default: ''
      }
    },
    data () {
      return {
        showDialog: true,
        showAddDialog: false
      }
    },
    methods: {
      updateRelate () {
        this.$emit('close')
      },
      addSubtitleFile () {
        this.$bus.$emit('goToSubtitle')
        this.$emit('close')
        this.showAddDialog = false
      },
      confirmAdd () {
        this.$refs.addSubtitle.addRelateSubtitle()
      },
      addRelateSubtitle () {
        this.showAddDialog = true
      }
    }
  }
</script>

<style scoped lang="scss">
  .dialog-footer {
    display: flex;
    justify-content: space-between;
  }
  .add-relate-subtitle {
    border: 1px solid #868FA3;
    border-radius: 4px 4px 4px 4px;
  }
  .text-btn {
    color: #627FC2;
  }
  ::v-deep .el-dialog__body {
    padding: 0!important;
    height: 600px;
  }
</style>
