<template>
  <div class="component-renderer" :class="`component-${component.type}`">
    <!-- 主画面组件 -->
    <MainScreenRenderer
      v-if="component.type === 'main'"
      :component="component"
      :is-selected="isSelected"
    />

    <!-- 直播源组件 -->
    <LiveStreamRenderer
      v-else-if="component.type === 'stream'"
      :component="component"
      :is-selected="isSelected"
    />

    <!-- 点播源组件 -->
    <VideoRenderer
      v-else-if="component.type === 'video'"
      :component="component"
      :is-selected="isSelected"
    />

    <!-- 背景组件 -->
    <BackgroundRenderer
      v-else-if="component.type === 'bg'"
      :component="component"
      :is-selected="isSelected"
    />

    <!-- 图片组件 -->
    <ImageRenderer
      v-else-if="component.type === 'logo'"
      :component="component"
      :is-selected="isSelected"
    />

    <!-- 文字组件 -->
    <TextRenderer
      v-else-if="component.type === 'subtitle'"
      :component="component"
      :is-selected="isSelected"
      @update-text="handleTextUpdate"
    />

    <!-- 网页组件 -->
    <WebpageRenderer
      v-else-if="component.type === 'web'"
      :component="component"
      :is-selected="isSelected"
    />

    <!-- 节目单组件 -->
    <ProgramRenderer
      v-else-if="component.type === 'program'"
      :component="component"
      :is-selected="isSelected"
    />

    <!-- 未知组件 -->
    <div v-else class="unknown-component">
      <span>未知组件类型: {{ component.type }}</span>
    </div>
  </div>
</template>

<script>
import MainScreenRenderer from './renderers/MainScreenRenderer.vue'
import LiveStreamRenderer from './renderers/LiveStreamRenderer.vue'
import VideoRenderer from './renderers/VideoRenderer.vue'
import BackgroundRenderer from './renderers/BackgroundRenderer.vue'
import ImageRenderer from './renderers/ImageRenderer.vue'
import TextRenderer from './renderers/TextRenderer.vue'
import WebpageRenderer from './renderers/WebpageRenderer.vue'
import ProgramRenderer from './renderers/ProgramRenderer.vue'

export default {
  name: 'ComponentRenderer',
  components: {
    MainScreenRenderer,
    LiveStreamRenderer,
    VideoRenderer,
    BackgroundRenderer,
    ImageRenderer,
    TextRenderer,
    WebpageRenderer,
    ProgramRenderer
  },
  props: {
    component: {
      type: Object,
      required: true
    },
    isSelected: {
      type: Boolean,
      default: false
    }
  },
  methods: {
    handleTextUpdate(newText) {
      // 传递文字更新事件到父组件
      this.$emit('update-component', {
        ...this.component,
        text: newText
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.component-renderer {
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.unknown-component {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 0, 0, 0.1);
  border: 1px dashed rgba(255, 0, 0, 0.3);
  color: #ff0000;
  font-size: 12px;
  text-align: center;
}
</style>
