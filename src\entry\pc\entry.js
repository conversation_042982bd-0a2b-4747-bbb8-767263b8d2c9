import Vue from 'vue'
import fullscreen from 'vue-fullscreen'
import './element.js'
import Util from 'common/js/util'
import 'common/js/baseComponents'
import VideoPlayer from 'vue-video-player'
import 'vue-video-player/src/custom-theme.css'
import 'video.js/dist/video-js.css'
import 'video.js/dist/lang/zh-CN'
import 'videojs-flash'
import 'videojs-contrib-hls/dist/videojs-contrib-hls.js'
import 'common/scss/_global.scss'
import 'common/theme/index.css'
import 'common/theme/qweather-icons.css'
import 'common/scss/_icon.scss'
import 'common/icons'
import 'vue-draggable-resizable/dist/VueDraggableResizable.css'
import Server from 'common/js/request'
import liveApi from 'api/live'
import ctrlApi from 'api/ctrl'
import store from 'store'
import * as types from '../../store/mutation-types'
import cloudApi from 'api/cloud'
import router from '@/router/index.js'
import App from '@/App'
import VueShortkey from 'vue-shortkey'
const pcInstance = () => import(/* webpackChunkName: "pcInstance" */ 'entry/pcInstance')

Vue.use(VideoPlayer)

Vue.config.productionTip = false
Vue.use(fullscreen)
Vue.use(VueShortkey, {prevent: ['input', 'textarea']})
Vue.use(Util)
let bus = new Vue()
Vue.prototype.$bus = bus
Vue.prototype.$_isLocal = true // 是否是本地
window.WebRTCLogOpen = false

let instance = null

getMachineInfo()

function createVue (router) {
  instance = new Vue({
    store,
    router,
    render: h => h(App)
  }).$mount('#app')
  Server.bind(instance)
  liveApi.bind(instance)
  ctrlApi.bind(instance)
}

function getMachineInfo () {
  cloudApi.getMachineInfo({}, res => {
    store.commit(types.SET_VERSION_INFO, {deviceCode: res.deviceCode, currentVersion: res.currentVersion, latestVersion: res.latestVersion})
    store.commit(types.SET_MACHINE_INFO, {authorized: res.authorized, instanceCount: res.instanceCount, is4k: res.enable4K})

    checkEntrance(res)
  })
}
function checkEntrance (res) {
  if (res.instanceCount === 1) {
    store.commit(types.SET_INSTANCE_ID, 1)
    instance = new Vue({
      store,
      router,
      render: h => h(pcInstance)
    }).$mount('#app')
    Server.bind(instance)
    liveApi.bind(instance)
    ctrlApi.bind(instance)
  } else {
    createVue(router)
  }
}
