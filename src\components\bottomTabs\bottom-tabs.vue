<template>
  <div>
    <el-tabs v-model="activeName">
      <el-tab-pane label="调音混音" name="soundMixer">
        <sound-mixer class="sound-mixer" ref="soundMixer" :info="info"></sound-mixer>
      </el-tab-pane>
      <el-tab-pane label="LOGO水印" name="logo" v-if="roleType !== 2">
        <cg-template class="cg-template"></cg-template>
      </el-tab-pane>
      <el-tab-pane label="视频布局" name="layout">
        <layout-tab
          @add-layout="handleAddLayout"
          @select-layout="handleSelectLayout"
          @edit-layout="handleEditLayout"
        ></layout-tab>
      </el-tab-pane>
      <el-tab-pane label="转场特效" name="transition">
        <TransitionEffects />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
  import soundMixer from '../soundMixer/sound-mixer'
  import cgTemplate from '../cgTemplate/cg-template'
  import layoutTab from './layout-tab'
  import TransitionEffects from '@/components/transitionEffects'
  import {mapGetters} from 'vuex'

  export default {
    name: 'bottom-tabs',
    components: {soundMixer, cgTemplate, layoutTab, TransitionEffects},
    props: {
      info: {
        type: Object,
        default: () => {}
      }
    },
    computed: {
      ...mapGetters([
        'roleType'
      ])
    },
    data () {
      return {
        activeName: 'soundMixer'
      }
    },
    methods: {
      handleAddLayout() {
        console.log('触发添加布局')
        // 这里可以触发打开布局管理页面或弹窗
        this.$emit('add-layout')
      },

      handleSelectLayout(layout) {
        console.log('选择布局:', layout)
        // 这里可以应用选中的布局到当前实例
        this.$emit('select-layout', layout)
      },

      handleEditLayout(layout) {
        console.log('编辑布局:', layout)
        // 这里可以打开布局编辑器
        this.$emit('edit-layout', layout)
      }
    },
    watch: {
      activeName () {
        if (this.$refs.soundMixer) {
          this.$refs.soundMixer.resizeChange()
        }
      }
    }
  }
</script>

<style scoped lang="scss">
  ::v-deep .el-tabs {
    height: 100%;
    width: 100%;
  }
  ::v-deep .el-tabs__content {
    height: calc(100% - 55px);
    width: 100%;
  }
  ::v-deep .el-tab-pane {
    height: 100%;
    width: 100%;
  }
  ::v-deep .el-tabs__nav-scroll {
    display: flex;
    justify-content: center;
  }
  ::v-deep .el-tabs__nav-wrap::after {
    background-color: $color-content-bg;
  }
  ::v-deep .el-tabs__item {
    font-size: 16px;
  }
</style>
