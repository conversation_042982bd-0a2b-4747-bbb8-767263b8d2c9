import Vue from 'vue'
import Router from 'vue-router'
Vue.use(Router)

const router = new Router({
  routes: [
    {
      path: '/',
      name: 'home',
      component: () => import('entry/pcInstanceList/index.vue')
    },
    {
        path: '/instance/:id',
        name: 'instancePage',
        component: () => import('entry/pcInstance/index.vue')
    },
    {
        path: '/console4qukansuperadmin',
        name: 'admin',
        component: () => import('entry/consoleAdmin')
    }
  ]
})
router.beforeEach((to, from, next) => {
  if (to.matched.length === 0) {
    from.path ? next({ path: from.path }) : next('/')
  } else {
    next()
  }
})
export default router
