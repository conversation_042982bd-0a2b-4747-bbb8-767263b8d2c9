<template>
  <div class="canvas-area" ref="canvasContainer">
    <div
      class="canvas"
      :style="canvasStyle"
    >
      <!-- 渲染所有组件 -->
      <template v-for="component in sortedComponents">
        <!-- 背景组件不可拖拽 -->
        <div
          :key="component.id"
          v-if="component.type === 'bg'"
          class="component-wrapper background"
          :class="{ 'selected': selectedComponent && selectedComponent.id === component.id }"
          :style="getComponentStyle(component)"
          @click.stop="selectComponent(component)"
        >
          <ComponentRenderer
            :component="component"
            :is-selected="selectedComponent && selectedComponent.id === component.id"
            @update-component="$emit('update-component', $event)"
          />
        </div>

        <!-- 其他组件使用 vue-draggable-resizable -->
        <vue-draggable-resizable
          v-else
          :key="component.id"
          :active="selectedComponent && selectedComponent.id === component.id"
          :w="component.width"
          :h="component.height"
          :x="component.dx"
          :y="component.dy"
          :z="component.zIndex"
          :min-width="20"
          :min-height="20"
          :resizable="true"
          :draggable="true"
          :scale="scale"
          :lock-aspect-ratio="component.whLocked"
          :parent="hasParentLimit"
          :preventDeactivation="true"
          @activated="selectComponent(component)"
          @deactivated="handleDeactivated(component)"
          @dragging="onDrag"
          @resizing="onResize"
          class="component-wrapper draggable"
        >
          <ComponentRenderer
            :component="component"
            :is-selected="selectedComponent && selectedComponent.id === component.id"
            @update-component="$emit('update-component', $event)"
          />
        </vue-draggable-resizable>
      </template>
    </div>
  </div>
</template>

<script>
import ComponentRenderer from './ComponentRenderer.vue'
import VueDraggableResizable from 'vue-draggable-resizable'
import {mapGetters} from 'vuex'

export default {
  name: 'CanvasArea',
  components: {
    ComponentRenderer,
    VueDraggableResizable
  },
  props: {
    ratio: {
      type: String,
      default: '16:9'
    },
    components: {
      type: Array,
      default: () => []
    },
    selectedComponent: {
      type: Object,
      default: null
    }
  },
  computed: {
    canvasSize() {
      // 根据比例计算画布尺寸，长边固定为1920
      const longSide = 1920
      let width, height

      switch (this.ratio) {
        case '16:9':
          width = longSide
          height = Math.floor(longSide * 9 / 16)
          break
        case '9:16':
          width = Math.floor(longSide * 9 / 16)
          height = longSide
          break
        case '4:3':
          width = longSide
          height = Math.floor(longSide * 3 / 4)
          break
        case '4:9':
          width = Math.floor(longSide * 4 / 9)
          height = longSide
          break
        default:
          width = longSide
          height = Math.floor(longSide * 9 / 16)
      }

      return { width, height }
    },
    canvasStyle() {
      const availableWidth = this.containerSize.width
      const availableHeight = this.containerSize.height

      // 计算缩放比例：确保画布完全包含在容器内
      const scaleX = availableWidth / this.canvasSize.width
      const scaleY = availableHeight / this.canvasSize.height
      const scale = Math.min(scaleX, scaleY) // 取较小的缩放比例，确保完全包含

      // 计算缩放后的实际尺寸
      const scaledWidth = this.canvasSize.width * scale
      const scaledHeight = this.canvasSize.height * scale

      // 计算居中偏移量
      const offsetX = (availableWidth - scaledWidth) / 2
      const offsetY = (availableHeight - scaledHeight) / 2
      this.scale = scale
      return {
        width: `${this.canvasSize.width}px`,
        height: `${this.canvasSize.height}px`,
        transform: `scale(${scale})`,
        transformOrigin: 'top left', // 缩放原点是左上角
        left: `${offsetX}px`,
        top: `${offsetY}px`
      }
    },
    sortedComponents() {
      // 按 zIndex 排序组件
      return [...this.components].sort((a, b) => (a.zIndex || 0) - (b.zIndex || 0))
    },
    ...mapGetters([
      'hasParentLimit'
    ])
  },
  data() {
    return {
      containerSize: {
        width: 800,
        height: 800
      },
      scale: 1
    }
  },
  mounted() {
    this.updateContainerSize()
    window.addEventListener('resize', this.updateContainerSize)
  },
  beforeUnmount() {
    window.removeEventListener('resize', this.updateContainerSize)
  },
  methods: {
    updateContainerSize() {
      // 使用固定的视口单位尺寸
      this.containerSize = {
        width: this.$refs.canvasContainer.offsetWidth,
        height: this.$refs.canvasContainer.offsetHeight
      }
    },
    getComponentStyle(component) {
      return {
        position: 'absolute',
        left: `${component.dx}px`,
        top: `${component.dy}px`,
        width: `${component.width}px`,
        height: `${component.height}px`,
        zIndex: component.zIndex || 0
      }
    },
    selectComponent(component) {
      this.$emit('select-component', component)
    },
    handleDeactivated(component) {
      // 点击空白区域取消选择
      this.$emit('deactiva-component', component)
    },
    onDrag(x, y) {
      // 拖拽结束时更新组件数据
      const updatedComponent = {
        ...this.selectedComponent,
        dx: x,
        dy: y
      }
      this.$emit('update-component', updatedComponent)
    },
    onResize(x, y, width, height) {
      // 调整大小结束时更新组件数据
      const updatedComponent = {
        ...this.selectedComponent,
        dx: x,
        dy: y,
        width,
        height
      }
      this.$emit('update-component', updatedComponent)
    }
  }
}
</script>

<style lang="scss" scoped>
.canvas-area {
  width: 100%;
  height: 100%;
  overflow: hidden;
  position: relative;
}

.canvas {
  position: absolute;
  background: #1a1a1a;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  overflow: hidden;
}

.component-wrapper {
  &.background {
    cursor: default;

    &.selected {
      outline: 2px dashed #ff6b35;
      outline-offset: -1px;
    }
  }

  &.draggable {
    cursor: move;
  }
}

// vue-draggable-resizable 组件的自定义样式
:deep(.vdr) {
  border: none !important;

  &.active {
    border: 2px solid #ff6b35 !important;
  }

  .handle {
    background: #ff6b35 !important;
    border: 1px solid #ffffff !important;
    width: 8px !important;
    height: 8px !important;
    border-radius: 50% !important;
  }

  .handle-tl { cursor: nw-resize !important; }
  .handle-tm { cursor: n-resize !important; }
  .handle-tr { cursor: ne-resize !important; }
  .handle-ml { cursor: w-resize !important; }
  .handle-mr { cursor: e-resize !important; }
  .handle-bl { cursor: sw-resize !important; }
  .handle-bm { cursor: s-resize !important; }
  .handle-br { cursor: se-resize !important; }
}
</style>
