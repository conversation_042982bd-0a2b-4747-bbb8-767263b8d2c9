<template>
  <div class="pgm-menu">
    <div class="pgm-list">
      <div class="list-top">
        <div class="list-top-title">节目单列表</div>
        <span class="list-top-tips">注：优先播定时节目单，空闲时间再播默认节</span>
      </div>
      <div class="list-wrapper">
        <div class="list-item" v-for="item in pgmList" :key="item.id"
          :class="{ active: activeItem.id === item.id }" @click="pickPgmMenu(item)">
          <div class="item-name">
            <span class="default-tag" v-if="item.isDefault">默认</span>
            {{ item.name }}
            <span class="ban-tag" v-if="!item.enable">已禁用</span>
          </div>
          <div class="item-duration" v-if="!item.isDefault && item.scheduleConfig.mode !== 'none'">
            {{ getDateText(item) }}{{ item.scheduleConfig.startTime }}~{{ getEndTime(item) }}
          </div>
          <div class="iten-operation">
            <span style="margin-right: 5px" @click.stop="setDefaultPgm(item)"
                v-if="!item.isDefault && item.scheduleConfig.mode === 'none'">设为默认</span>
            <svg-icon class="mr-icon" icon-class="edit" @click.native="editPgm(item)"></svg-icon>
            <svg-icon class="mr-icon" icon-class="ban" @click.native="switchPgmAvailable(item)" v-if="item.enable"></svg-icon>
            <svg-icon class="mr-icon" icon-class="unban" @click.native="switchPgmAvailable(item)" v-else></svg-icon>
            <svg-icon icon-class="delete" @click.native="delPgm(item)" v-if="!item.isDefault"></svg-icon>
          </div>
        </div>
      </div>
      <div class="abso-box">
        <div class="btn-like" @click="addPgm">
          <i class="el-icon-plus add-icon"></i>
          新增节目单
        </div>
      </div>
    </div>
    <div class="pgm-items">
      <div class="items-top">
        <div class="btn-like" @click="showAddProgram">
          <i class="el-icon-plus add-icon"></i>
          新增节目
        </div>
        <div class="total-duration">
          节目总时长：
          <span class="total-duration-time">{{ sumDuration | formatDuration }}</span>
        </div>
      </div>
      <div class="table-wrapper">
        <div class="table-column table-header">
          <div class="row-1">序号</div>
          <div class="row-2">节目名称</div>
          <div class="row-3">节目时长</div>
          <div class="row-4">操作</div>
        </div>
        <div class="table-column" :key="item.id" v-for="(item, index) in programList">
          <div class="row-1">{{index + 1}}</div>
          <div class="row-2">
            <div class="name" :title="item.name">{{item.name}}</div>
          </div>
          <div class="row-3">{{ item.duration | formatDuration }}</div>
          <div class="row-4">
            <span class="btn-like mr-btn" @click="viewPgmItem(item)">预览</span>
            <el-dropdown>
              <span class="btn-like">更多</span>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item @click.native="relateSubtitleShow(item)">关联字幕</el-dropdown-item>
                <el-dropdown-item @click.native="showFramesClipDialog(item)">画面裁剪</el-dropdown-item>
                <el-dropdown-item @click.native="showPaletteDialog(item)">调色</el-dropdown-item>
                <el-dropdown-item @click.native="showLayoutSelectDialogForProgram(item)">关联布局</el-dropdown-item>
                <span class="dropdown-line"></span>
                <el-dropdown-item @click.native="editPgmItem(item)">编辑</el-dropdown-item>
                <el-dropdown-item @click.native="deleteProgram(item)">删除</el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </div>
        </div>
      </div>
    </div>

    <PgmDialog :editItem="editItem" @refresh="getPgmList" @close="pgmDialogShow=false"
      v-if="pgmDialogShow" />
    <AddProgram :addType="5" :currentItem="currentItem" :activeItem="activeItem"
      @refresh="getPgmList" @close="showProgramDialog = false" v-if="showProgramDialog" />
    <preview-dialog v-if="previewDialog" @close="previewDialog = false"
      :currentProgramItem="currentProgramItem"></preview-dialog>
    <relate-subtitle v-if="relateSubtitleDialog" @close="relateSubtitleDialog=false"
      :currentProgram="currentProgram" :manageId="manageId"
      :currentProgramItem="currentProgramItem"></relate-subtitle>
    <pinp-setting @close="showPinpSetting = false" v-if="showPinpSetting"
      :currentProgramItem="currentProgramItem" :manageId="manageId"></pinp-setting>
    <frames-clip @close="showFramesClip = false" v-if="showFramesClip"
      :currentProgramItem="currentProgramItem" :manageId="manageId"></frames-clip>
    <palette @close="showPalette = false" v-if="showPalette"
      :currentProgramItem="currentProgramItem" :manageId="manageId"></palette>

    <!-- 布局选择弹窗 -->
    <LayoutSelectDialog
      :visible.sync="showLayoutSelectDialog"
      :target-ratio="currentLiveRatioString"
      @layout-selected="handleLayoutSelected"
      @close="handleLayoutSelectDialogClose"
    />
  </div>
</template>

<script>
import liveApi from 'api/live'
import PgmDialog from './pgm-dialog'
import AddProgram from '@/components/addProgram'
import previewDialog from '@/components/programList/previewDialog'
import relateSubtitle from '@/components/cgTemplate/relate-subtitle'
import pinpSetting from '@/components/pinpSetting/pinp-setting'
import framesClip from '@/components/framesClip/frames-clip'
import palette from '@/components/palette'
import { mapGetters, mapActions } from 'vuex'
import moment from 'moment'
import layoutSelectMixin from '@/common/js/layoutSelectMixin'

export default {
  name: 'pgmMenu',
  mixins: [layoutSelectMixin],
  components: {
    PgmDialog,
    AddProgram,
    previewDialog,
    relateSubtitle,
    pinpSetting,
    framesClip,
    palette
  },
  props: {},
  computed: {
    sumDuration() {
      return this.programList.reduce((acc, current) => acc + current.duration, 0)
    },
    ...mapGetters(['programsSn'])
  },
  data() {
    return {
      pgmDialogShow: false,
      editItem: {},
      pgmList: [],
      showProgramDialog: false,
      currentItem: {},
      activeItem: {},
      programList: [],
      previewDialog: false,
      relateSubtitleDialog: false,
      manageId: 0,
      currentProgram: '',
      currentProgramItem: {},
      showPinpSetting: false,
      showFramesClip: false,
      showPalette: false
    }
  },
  methods: {
    ...mapActions(['getPrograms']),
    getPgmList() {
      liveApi.getPgmList({}, (res) => {
        if (Array.isArray(res.schedules)) {
          this.pgmList = res.schedules
          if (this.pgmList.length) {
            this.activeItem = this.pgmList[0]
            this.programList = Array.isArray(this.activeItem.programs)
              ? this.activeItem.programs
              : []
          }
        }
      })
    },
    pickPgmMenu(item) {
      this.activeItem = item
      this.programList = Array.isArray(item.programs) ? item.programs : []
    },
    addPgm() {
      this.editItem = {}
      this.pgmDialogShow = true
    },
    setDefaultPgm(item) {
      const param = this.getPgmUpdParam(item)
      param.isDefault = true
      param.enable = true
      liveApi.updPgm(
        {
          schedule: param,
          sn: this.programsSn
        },
        (res) => {
          this.$message.success('操作成功')
          this.getPrograms()
          this.getPgmList()
        },
        (code, msg) => {
          this.$message.error(msg)
        }
      )
    },
    editPgm(item) {
      this.editItem = item
      this.pgmDialogShow = true
    },
    getPgmUpdParam(item) {
      return {
        id: item.id,
        name: item.name,
        enable: item.enable,
        isDefault: !!item.isDefault,
        programs: item.programs || [],
        scheduleConfig: item.scheduleConfig
      }
    },
    switchPgmAvailable(item) {
      const tipMsg = item.enable ? '禁用' : '启用'
      this.$confirm('确定要' + tipMsg + '该节目吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          const param = this.getPgmUpdParam(item)
          param.enable = !item.enable
          liveApi.updPgm(
            {
              schedule: param,
              sn: this.programsSn
            },
            (res) => {
              this.$message.success('操作成功')
              this.getPrograms()
              this.getPgmList()
            },
            (code, msg) => {
              this.$message.error(msg)
            }
          )
        })
        .catch(() => {
          this.$message.info('已取消禁用')
        })
    },
    delPgm(item) {
      this.$confirm('确定要删除该节目单吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          liveApi.removePgm(
            {
              id: item.id,
              sn: this.programsSn
            },
            (res) => {
              this.$message.success('操作成功')
              this.getPrograms()
              this.getPgmList()
            },
            (code, msg) => {
              this.$message.error(msg)
            }
          )
        })
        .catch(() => {
          this.$message.info('已取消')
        })
    },
    getDateText(item) {
      if (item.scheduleConfig.mode === 'daily') return '每天'
      if (item.scheduleConfig.mode === 'weekly') {
        if (!item.scheduleConfig.daysOfWeek || item.scheduleConfig.daysOfWeek.length === 0) return ''
        const weekMap = ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
        const weekText = item.scheduleConfig.daysOfWeek.map((day) => weekMap[day - 1]).join('、')
        return '周播(' + weekText + ')'
      }
      return '指定日期 ' + item.scheduleConfig.date
    },
    getEndTime(item) {
      if (!item.scheduleConfig || !item.scheduleConfig.startTime || !item.scheduleConfig.duration) { return '' }
      const timePrefix = moment().format('YYYY-MM-DD')
      return moment(timePrefix + ' ' + item.scheduleConfig.startTime)
        .add(item.scheduleConfig.duration, 'seconds')
        .format('HH:mm:ss')
    },
    viewPgmItem(item) {
      this.currentProgramItem = item
      this.previewDialog = true
    },
    relateSubtitleShow(item) {
      this.manageId = this.activeItem.id
      this.currentProgram = item.id
      this.currentProgramItem = item
      this.relateSubtitleDialog = true
    },
    showPinpDialog(item) {
      this.manageId = this.activeItem.id
      this.currentProgramItem = item
      this.showPinpSetting = true
    },
    showFramesClipDialog(item) {
      this.manageId = this.activeItem.id
      this.currentProgramItem = item
      this.showFramesClip = true
    },
    showPaletteDialog(item) {
      this.manageId = this.activeItem.id
      this.currentProgramItem = item
      this.showPalette = true
    },
    showAddProgram() {
      this.currentItem = {}
      this.showProgramDialog = true
    },
    editPgmItem(item) {
      this.currentItem = item
      this.showProgramDialog = true
    },
    deleteProgram(item) {
      // 删除节目
      this.$confirm('确定要删除该节目吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          const schedule = {
            id: this.activeItem.id,
            name: this.activeItem.name,
            enable: this.activeItem.enable,
            isDefault: this.activeItem.isDefault,
            programs: this.activeItem.programs,
            scheduleConfig: this.activeItem.scheduleConfig
          }
          const index = schedule.programs.findIndex((p) => p.id === item.id)
          if (index >= 0) {
            schedule.programs.splice(index, 1)
          }
          liveApi.updPgm(
            {
              id: this.activeItem.id,
              sn: this.programsSn,
              schedule
            },
            () => {
              this.$message.success('删除成功')
              this.getPrograms()
              this.getPgmList()
            },
            (code, msg) => {
              this.$message.error(msg)
            }
          )
        })
        .catch(() => {
          this.$message.info('已取消删除')
        })
    }
  },
  created() {
    this.getPgmList()
  },
  mounted() {}
}
</script>

<style scoped lang="scss">
@import '../../common/scss/variables';
@import '../../common/scss/mixin';

.pgm-menu {
  width: 100%;
  height: calc(100% - 13px);
  margin-top: 13px;
  display: flex;
  overflow: hidden;

  .pgm-list {
    position: relative;
    flex: 0 0 390px;
    height: calc(100% - 8px);
    background-color: #292e37;
    border-radius: 12px;
    padding: 0 12px;
    box-sizing: border-box;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    .list-top {
      margin-top: 22px;
      .list-top-title {
        font-size: #bec9e5;
        font-weight: bold;
        color: #bec9e5;
      }
      .list-top-tips {
        margin-top: 16px;
        font-size: 12px;
        color: #7a8195;
      }
    }
    .list-wrapper {
      margin-top: 18px;
      flex: 1;
      overflow: auto;
      scrollbar-width: none; /* firefox */
      -ms-overflow-style: none; /* IE 10+ */
      &::-webkit-scrollbar {
        display: none; /* Chrome Safari */
      }
      .list-item {
        position: relative;
        width: 100%;
        height: 70px;
        margin-bottom: 6px;
        border-radius: 6px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        padding: 0 12px;
        box-sizing: border-box;
        cursor: pointer;
        .item-name {
          font-size: 16px;
          color: #bec9e5;
          max-width: calc(100% - 87px);
          @include elliptical();
          .default-tag {
            display: inline-block;
            width: 36px;
            height: 20px;
            background-color: #535d7f;
            font-size: 12px;
            color: #bec9e5;
            text-align: center;
            line-height: 20px;
            border-radius: 4px;
            margin-right: 5px;
          }
          .ban-tag {
            display: inline-block;
            width: 44px;
            height: 18px;
            font-size: 12px;
            color: #e91010;
            text-align: center;
            line-height: 20px;
            outline: 1px solid #e91010;
            border-radius: 4px;
            margin-left: 5px;
            vertical-align: text-bottom;
          }
        }
        .item-duration {
          font-size: 14px;
          color: #7a8195;
          margin-top: 12px;
        }
        .iten-operation {
          position: absolute;
          right: 12px;
          top: 17px;
          font-size: 12px;
          color: #bec9e5;
          cursor: pointer;
          opacity: 0;
          .mr-icon {
            margin-right: 12px;
          }
        }
        &:hover {
          background-color: #363943;
          .iten-operation {
            opacity: 1;
          }
        }
        &.active {
          background-color: #1f232b;
          border: 1px solid #e95c2b;
          .item-name {
            color: #e95c2b;
          }
        }
      }
    }

    .abso-box {
      position: absolute;
      right: 12px;
      top: 15px;
      .btn-like {
        width: 104px;
        height: 32px;
        border-radius: 4px;
        background-color: #555d70;
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 14px;
        color: #bec9e5;
        cursor: pointer;
        .add-icon {
          margin-right: 5px;
        }
      }
    }
  }
  .pgm-items {
    flex: 1;
    min-width: 0;
    margin-left: 16px;
    .items-top {
      display: flex;
      align-items: center;
      justify-content: space-between;
      .btn-like {
        width: 92px;
        height: 32px;
        background-color: #e95c2b;
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 14px;
        color: #fff;
        cursor: pointer;
        border-radius: 4px;
        .add-icon {
          margin-right: 5px;
        }
      }
      .total-duration {
        font-size: 16px;
        color: #7a8195;
        .total-duration-time {
          color: #bec9e5;
        }
      }
    }
    .table-wrapper {
      height: calc(100% - 57px);
      margin-top: 17px;
      overflow: auto;
      scrollbar-width: none; /* firefox */
      -ms-overflow-style: none; /* IE 10+ */
      &::-webkit-scrollbar {
        display: none; /* Chrome Safari */
      }
      .table-column {
        height: 46px;
        display: flex;
        font-size: 16px;
        line-height: 46px;
        color: #b4bfdb;
        &.table-header {
          color: #666d7e;
          background-color: #272c35;
        }
        .row-1 {
          flex: 0 0 92px;
          text-align: center;
        }
        .row-2 {
          flex: 1;
          padding: 0 30px;
          box-sizing: border-box;
          text-align: center;
        }
        .row-3 {
          flex: 0 0 132px;
          text-align: center;
        }
        .row-4 {
          flex: 0 0 157px;
          padding: 0 30px;
          box-sizing: border-box;
          .btn-like {
            font-size: 16px;
            color: #687ebd;
            cursor: pointer;
            &.mr-btn {
              margin-right: 33px;
            }
          }
        }
      }
    }
    .program-wrapper {
      flex: 1;
      overflow: hidden;
      .list-header {
        display: flex;
        justify-content: flex-start;
        align-items: center;
        color: #646d80;
        font-size: 16px;
        box-sizing: border-box;
        height: 46px;
        background: #454d5c;
      }
      .program-ul {
        height: calc(100% - 46px);
        overflow-y: auto;
        overflow-x: hidden;
        .program-li {
          position: relative;
          display: flex;
          justify-content: flex-start;
          margin: 2px 0;
          padding: 6px 0;
          background: #292e37;
          box-sizing: border-box;
          height: 90px;
          &:hover {
            background: #3e4252;
          }
        }
      }
      .program-checked {
        display: flex;
        align-items: center;
        justify-content: center;
        flex: 0 0 5%;
        width: 5%;
      }
      .program-name {
        display: flex;
        align-items: center;
        text-indent: 40px;
        flex: 0 0 20.5%;
        width: 20.5%;
        padding-right: 40px;
        box-sizing: border-box;
        .name {
          display: inline-block;
          width: 200px;
          @include elliptical();
        }
      }
      .program-number {
        display: flex;
        align-items: center;
        justify-content: center;
        flex: 0 0 12%;
        width: 12%;
        white-space: nowrap;
      }
      .program-time {
        display: flex;
        align-items: center;
        flex: 0 0 15%;
        width: 15%;
        text-indent: 30px;
      }
      .program-duration {
        display: flex;
        align-items: center;
        flex: 0 0 10%;
        width: 10%;
      }
      .cg-name {
        display: flex;
        align-items: center;
        flex: 0 0 13%;
        width: 13%;
        padding-right: 20px;
        box-sizing: border-box;
        .cg-name-ul {
          width: 100%;
          max-height: 78px;
          display: flex;
          flex-direction: column;
          overflow-y: auto;
        }
        .cg-name-li {
          display: inline-block;
          width: 100%;
          flex-shrink: 0;
          @include elliptical();
        }
      }
      .audio {
        display: flex;
        align-items: center;
        flex: 0 0 13%;
        width: 13%;
        padding-right: 20px;
        box-sizing: border-box;
        .audio-ul {
          width: 100%;
          max-height: 78px;
          display: flex;
          flex-direction: column;
          overflow-y: auto;
        }
        .audio-li {
          flex-shrink: 0;
          display: inline-block;
          width: 100%;
          @include elliptical();
        }
      }
      .program-options {
        display: flex;
        align-items: center;
        flex: 0 0 12%;
        width: 12%;
      }
    }
  }
}
// 按钮样式
.g-option-btn-1 {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 6px 12px;
  background: $color-theme;
  color: #fff;
  border-radius: $border-radius;
  cursor: pointer;
  font-size: 14px;
  margin-left: 10px;

  &:hover {
    background: $color-theme-hover;
  }
}

.g-option-btn-2 {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 6px 12px;
  background: $color-bg-btn;
  color: $color-text;
  border: 1px solid $color-border-btn;
  border-radius: $border-radius;
  cursor: pointer;
  font-size: 14px;

  &:hover {
    background: #656e7f;
  }
}

// Element UI 按钮样式覆盖
::v-deep .el-button--text {
  color: #627fc2;

  &:hover {
    color: #ff5b0c;
  }

  &:focus {
    color: #627fc2;
  }
}
</style>
