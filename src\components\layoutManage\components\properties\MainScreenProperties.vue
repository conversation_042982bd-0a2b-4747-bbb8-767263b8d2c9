<template>
  <div class="main-screen-properties">
    <!-- 位置大小 -->
    <PositionSizeProperties
      :component="component"
      :canvas-size="canvasSize"
      @update="handleUpdate"
    />

    <!-- 画面显示 -->
    <DisplayModeProperties
      :component="component"
      @update="handleUpdate"
    />
  </div>
</template>

<script>
import PositionSizeProperties from './common/PositionSizeProperties.vue'
import DisplayModeProperties from './common/DisplayModeProperties.vue'

export default {
  name: 'MainScreenProperties',
  components: {
    PositionSizeProperties,
    DisplayModeProperties
  },
  props: {
    component: {
      type: Object,
      required: true
    },
    canvasSize: {
      type: Object,
      default: () => ({ width: 1920, height: 1080 })
    }
  },
  data() {
    return {
      displayModes: [
        { label: '适应', value: 'contain' },
        { label: '拉伸', value: 'fill' },
        { label: '裁剪', value: 'cover' }
      ]
    }
  },
  methods: {
    handleUpdate(data) {
      this.$emit('update', data)
    },
    handleDisplayModeChange(mode) {
      this.$emit('update', { objectFit: mode })
    }
  }
}
</script>

<style lang="scss" scoped>
.main-screen-properties {
  .property-section {
    margin-bottom: 20px;

    .section-title {
      font-size: 14px;
      font-weight: 500;
      margin-bottom: 12px;
      padding: 0 16px;
    }
  }

  .property-group {
    margin-bottom: 16px;
    padding: 0 16px;
  }
}
</style>
