<template>
  <div class="upload-img-wrapper">
    <v-upload :disabled="disabled" class="upload-img" @uploadUrl="uploadUrl" :isGetImgResolution="isGetImgResolution" :isNeedLogin="isNeedLogin"
              :fileSize="fileSize" :style="{width: imgWidth + 'px', height: imgHeight + 'px'}">
      <img class="img-cover" :src="imgUrl" alt="" v-if="imgUrl">
      <div class="empty-img" v-else>
        <div>
          <i class="icon el-icon-plus" v-if="isLogo"></i>
          <template v-else>
            <slot name="noPicBg" v-if="noPicBg"></slot>
            <img class="img" src="./no-pic.png" alt="" v-else>
          </template>
          <div class="upload-tips">{{tips}}</div>
        </div>
      </div>
      <div class="mask" v-if="!disabled">
        <div class="upload-btn">
          <i class="el-icon-plus"></i>
          {{imgUrl ? '编辑' : '上传'}}图片
        </div>
      </div>
      <div class="delete-img" @click.stop="deleteImg" v-if="hasDelete && imgUrl">
        <i class="icon el-icon-delete"></i>
      </div>
    </v-upload>
    <div class="color97" style="margin-left: 20px;">
      <slot name="tips"></slot>
    </div>
  </div>
</template>

<script>
  import vUpload from './v-upload'
  import {mapGetters} from 'vuex'

  export default {
    name: 'upload-img',
    components: {vUpload},
    props: {
      hasDelete: {
        type: Boolean,
        default: false
      },
      noPicBg: {
        type: Boolean,
        default: false
      },
      disabled: {
        type: Boolean,
        default: false
      },
      isGetImgResolution: {
        type: Boolean,
        default: false
      },
      isNeedLogin: {
        type: Boolean,
        default: true
      },
      fileSize: {
        type: Number,
        default: 2
      },
      isLogo: {
        type: Boolean,
        default: false
      },
      editImgUrl: {
        type: String,
        default: ''
      },
      tips: {
        type: String,
        default: '点击上传图片'
      }
    },
    computed: {
      imgWidth () {
        return this.imgHeight * this.liveRatio
      },
      imgHeight () {
        return 211
      },
      ...mapGetters([
        'liveRatio'
      ])
    },
    data () {
      return {
        imgUrl: this.editImgUrl,
        ratioList: [
          {id: 1, label: '16:9', width: 1920, height: 1080},
          // {id: 2, label: '16:9', width: 3840, height: 2160},
          {id: 3, label: '16:9', width: 1280, height: 720},
          {id: 4, label: '4:3', width: 1280, height: 960},
          {id: 5, label: '4:3', width: 1024, height: 768},
          {id: 6, label: '9:16', width: 720, height: 1280},
          {id: 7, label: '4:9', width: 720, height: 1620},
          {id: 8, label: '9:16', width: 1080, height: 1920},
          {id: 9, label: '4:9', width: 1080, height: 2430}
          // {id: 10, label: '9:16', width: 2160, height: 3840}
        ]
      }
    },
    methods: {
      uploadUrl (imgUrl, file, uploadFileType, AuthData) {
        this.imgUrl = imgUrl
        this.$emit('uploadImgUrl', this.imgUrl, file, uploadFileType, AuthData)
      },
      deleteImg () {
        this.imgUrl = ''
        this.$emit('uploadImgUrl', '')
      }
    },
    watch: {
      editImgUrl (imgUrl) {
        this.imgUrl = imgUrl
      }
    }
  }
</script>

<style scoped lang="scss">
  .upload-img-wrapper {
    display: flex;
    align-items: center;
    line-height: 24px;
    .upload-img {
      position: relative;
      width: 380px;
      height: 100px!important;
      border: 1px solid $color-border;
      background-color: #000;
      /deep/ .el-upload {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 100%;
        height: 100%;
      }
      &:hover {
        .mask {
          opacity: 1;
        }
      }
      .empty-img {
        position: absolute;
        @include flex-c-c();
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        line-height: 20px;
        .icon {
          padding: 6px;
          border: 2px solid #ccc;
          border-radius: 50%;
          color: #ccc;
          font-size: 24px;
          margin-bottom: 6px;
        }
        .img {
          width: 56%;
        }
        .upload-tips {
          color: $color-sub-text;
        }
      }
      .mask {
        opacity: 0;
        position: absolute;
        z-index: 10;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        display: flex;
        background-color: rgba(0, 0, 0, .5);
        .upload-btn {
          margin: auto;
          border: 1px solid #fff;
          border-radius: 20px;
          padding: 0 6px;
          line-height: 30px;
          color: #fff;
        }
      }
      .delete-img {
        display: flex;
        position: absolute;
        z-index: 100;
        top: 0;
        right: 0;
        width: 24px;
        height: 24px;
        background-color: #F56C6C;
        color: #fff;
        .icon {
          margin: auto;
        }
      }
      .img-cover {
        width:auto;
        height:100%;
        max-width: 100%;
      }
    }
  }
</style>
