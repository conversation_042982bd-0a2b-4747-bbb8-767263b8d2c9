<template>
  <div class="footer-pagination">
    <pagination style="margin: 0;" :small="small" :page="page" @clickPage="getPageInfo" />
  </div>
</template>

<script>
  import pagination from '@/components/pagination'

  export default {
    name: 'footer-pagination',
    components: {pagination},
    props: {
      small: {
        type: Boolean,
        default: false
      },
      page: {
        type: Object
      }
    },
    data () {
      return {}
    },
    methods: {
      getPageInfo () {
        this.$emit('getPageInfo')
      }
    }
  }
</script>

<style scoped lang="scss">
  .footer-pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    box-sizing: border-box;
    padding: 0 14px;
    height: 54px;
    color: #979797;
    font-size: 15px;
    .page-data {
      &.small {
        font-size: 14px;
      }
      .num {
        font-weight: bold;
      }
    }
  }
</style>
