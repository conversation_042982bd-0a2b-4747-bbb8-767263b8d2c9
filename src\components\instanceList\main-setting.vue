<template>
  <el-dialog title="设置" width="640px" :visible.sync="showDialog" :close-on-click-modal="false"
             @close="cancel()">
    <div class="live-setting">
      <div class="left-tabs">
        <ul>
          <li v-for="item in tabList" class="tab-item" :class="{active: item.active}" @click.stop="selectItem(item)">
            <span class="tab-title">{{item.name}}</span>
          </li>
        </ul>
        <div class="footer">
          <span class="g-option-btn-2" @click="loginOut()" style="height: 28px;line-height: 26px;width: 66px">退 出</span>
        </div>
      </div>
      <div class="right-content">
        <template v-if="currentId===1">
          <div class="pwd-set wh100">
            <p class="label">管理员口令</p>
            <p class="tips">输入该口令进入全时播控，可拥有所有实例管理权限</p>
            <p>
              <el-input v-if="!visibleInput1" type="password" v-model.trim="password1" ref="password" style="width: 220px;">
                <i slot="suffix"  class="icon-no-view" @click="changePass1('show')"></i>
              </el-input>
              <el-input v-else type="text" v-model.trim="password1" ref="password" style="width: 220px;">
                <i slot="suffix"  class="icon-view" @click="changePass1('hide')"></i>
              </el-input>
              <span class="g-option-btn-2 copy-btn1" style="width: 84px;margin-left: 9px" :data-clipboard-text="oldPassword1">复制口令</span>
            </p>
            <p class="label" style="margin-top: 36px">巡查口令</p>
            <p class="tips">输入该口令进入全时播控，仅拥有所有实例查看权限</p>
            <p>
              <el-input v-if="!visibleInput2" type="password" v-model.trim="password2" ref="password" style="width: 220px;">
                <i slot="suffix"  class="icon-no-view" @click="changePass2('show')"></i>
              </el-input>
              <el-input v-else type="text" v-model.trim="password2" ref="password" style="width: 220px;">
                <i slot="suffix"  class="icon-view" @click="changePass2('hide')"></i>
              </el-input>
              <span class="g-option-btn-2 copy-btn2" style="width: 84px;margin-left: 9px" :data-clipboard-text="oldPassword2">复制口令</span>
            </p>
            <div class="dialog-footer">
              <span class="g-option-btn-1" style="width: 84px" @click="modifyPassword()">确定</span>
              <span class="g-option-btn-2" style="width: 84px" @click="cancel()">取消</span>
            </div>
          </div>
        </template>
        <template v-if="currentId===2">
          <div class="version-box wh100">
            <div class="version-info">
              <span style="margin-right: 40px">当前版本号: V{{versionInfo.currentVersion}}</span>
              <!-- <el-button type="primary" @click="upgrade" v-if="checkVersion()">升级</el-button> -->
            </div>
            <p class="version-info">设备编号: {{versionInfo.deviceCode}}</p>
            <div class="upload-block flex-c-c">
            <!-- <div class="upload-block flex-c-c" v-if="checkVersion()"> -->
              <template v-if="versionInfo.upgradeDownloadStatus === 'downloading'">
                <div class="tips-text">正在下载，请稍后 <i class="el-icon-loading"></i></div>
              </template>
              <template v-else-if="versionInfo.upgradeDownloadStatus === 'finish'">
                <div class="tips-text">已完成下载，是否升级</div>
                <el-button type="primary" @click="upgrade" size="small">立即升级</el-button>
              </template>
              <template v-else>
                <div class="tips-text">检测到有新版本V{{versionInfo.latestVersion}}</div>
                <el-button type="primary" size="small" @click="doDownloadUpgrade">下载</el-button>
              </template>
            </div>
          </div>
        </template>
        <template v-if="currentId===9">
          <el-form class="pwd-set">
            <el-form-item label="UDP端口范围：" label-width="120px">
              <div style="display: flex;justify-content: flex-start;align-items: center">
                <el-input v-model.trim="port.portMin"
                          onkeypress="return( /[\d]/.test(String.fromCharCode(event.keyCode)))"
                          style="width: 100px;" size="small">
                </el-input>
                <span style="margin: 0 10px; color: #A8B7D2">至</span>
                <el-input v-model.trim="port.portMax"
                          onkeypress="return( /[\d]/.test(String.fromCharCode(event.keyCode)))"
                          style="width: 100px;" size="small">
                </el-input>
                <el-button type="success" style="margin-left: 10px; height: 32px" @click="onSavePort" size="small">保存并重启</el-button>
              </div>
            </el-form-item>
            <span class="tips">注：udp端口主要用于控制页面的画面预览和数据传输，多人打开页面都会占用端口，端口总数配置建议在1000以上</span>
            <el-form-item label="外网映射IP：" label-width="120px" style="margin-top: 20px;">
              <el-input v-model="currentMappedIp" style="width: 236px;"></el-input>
              <el-button type="success" style="margin-left: 10px; height: 32px" @click="onSaveMappedIp" size="small">保存</el-button>
            </el-form-item>
            <span class="tips">注：内容为空则代表未开启外网IP映射，只支持内网IP访问</span>
          </el-form>
        </template>
      </div>
    </div>
  </el-dialog>
</template>

<script>
  import liveApi from 'api/live'
  import cloudApi from 'api/cloud'
  import {mapGetters, mapMutations} from 'vuex'
  import Clipboard from 'clipboard'

  export default {
    name: 'live-setting',
    data () {
      return {
        visibleInput1: false,
        visibleInput2: false,
        showDialog: true,
        tabList: [
          {id: 1, name: '口令设置', active: true},
          {id: 9, name: 'UDP端口设置', active: false},
          {id: 2, name: '关于', active: false}
        ],
        currentId: 1,
        oldPassword1: '',
        oldPassword2: '',
        password1: '',
        password2: '',
        logText: '',
        port: {
          portMin: 0,
          portMax: 0
        },
        currentMappedIp: ''
      }
    },
    watch: {
      currentId (val) {
        if (val === 9) {
          this.port.portMin = this.portRange.portMin >= 0 ? this.portRange.portMin : 30000
          this.port.portMax = this.portRange.portMax >= 0 ? this.portRange.portMax : 40000
          this.currentMappedIp = this.mappedIp
        }
      }
    },
    computed: {
      ...mapGetters([
        'roleType',
        'instanceId',
        'pwd',
        'versionInfo',
        'portRange',
        'mappedIp'
      ])
    },
    methods: {
      reStartPop () {
        const loading = this.$loading({
          lock: true,
          text: '正在重启中',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        })
        setInterval(() => {
          cloudApi.getMachineInfo({liveId: 0}, res => {
            location.reload(true)
          }, () => {})
        }, 2000)
      },
      doDownloadUpgrade () {
        cloudApi.downloadUpgrade({latestVersion: this.versionInfo.latestVersion}, () => {
          this.setVersionInfo({...this.versionInfo, upgradeDownloadStatus: 'downloading'})
          this.getMachineInfo()
        })
      },
      onSaveMappedIp() {
        cloudApi.setMappedIp({ip: this.currentMappedIp}, () => {
          this.setMappedIp(this.currentMappedIp)
          this.$message.success('保存成功')
        })
      },
      onSavePort () {
        if (!this.port.portMin || !this.port.portMax) {
          return this.$message.warning('请输入端口范围')
        }
        this.port.portMin = parseInt(this.port.portMin)
        this.port.portMax = parseInt(this.port.portMax)
        if (this.port.portMin < 30000 || this.port.portMin > 40000 || this.port.portMax < 30000 || this.port.portMax > 40000) {
          return this.$message.warning('请确保端口范围在30000到40000之间')
        }
        if (this.port.portMin >= this.port.portMax) {
          return this.$message.warning('请输入正确的端口范围')
        }
        let param = this.port
        this.$confirm('重启实例后生效，是否立即重启?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          cloudApi.saveInstancePort(param, () => {
            this.setPortRange(this.port)
            this.reStartPop()
          })
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消'
          })
        })
      },
      modifyPassword () {
        if (this.password1.length < 6 || this.password1.length > 255) {
          return this.$message.warning('管理员口令长度应在6-255字之间')
        }
        if (this.password2.length < 6 || this.password2.length > 255) {
          return this.$message.warning('巡查口令长度应在6-255字之间')
        }
        let param = {
          adminPwd: this.password1,
          inspectorPwd: this.password2
        }
        cloudApi.modifyAdminPassword(param, () => {
          localStorage.removeItem('live_pwd')
          this.$_deleteCookie('live_pwd_' + this.instanceId)
          if (this.roleType === 1) {
            this.setPwd(this.password1)
            this.oldPassword1 = param.adminPwd
          }
          if (this.roleType === 2) {
            this.setPwd(this.password2)
            this.oldPassword2 = param.inspectorPwd
          }
          this.$message.success('口令修改成功')
          this.$emit('close')
        })
      },
      getWord () {
        cloudApi.getAdminPassword({}, res => {
          this.password1 = res.adminPwd
          this.password2 = res.inspectorPwd
          this.oldPassword1 = res.adminPwd
          this.oldPassword2 = res.inspectorPwd
        })
      },
      changePass1 () {
        this.visibleInput1 = !this.visibleInput1
      },
      changePass2 () {
        this.visibleInput2 = !this.visibleInput2
      },
      cancel () {
        this.$emit('close')
      },
      checkVersion () {
        let result = this.$_compareVersion(this.versionInfo.latestVersion, this.versionInfo.currentVersion)
        if (result === 1) return true
        return false
      },
      upgrade () {
        this.$confirm('确定要升级最新版本吗?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          cloudApi.upgradeReq({systemVersion: this.versionInfo.latestVersion}, () => {
            const loading = this.$loading({
              lock: true,
              text: '正在升级中',
              spinner: 'el-icon-loading',
              background: 'rgba(0, 0, 0, 0.7)'
            })
            setInterval(() => {
              cloudApi.checkUpdate({}, res => {
                if (res.isUpgrade) {
                  location.reload(true)
                }
              }, () => {})
            }, 2000)
          })
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消'
          })
        })
      },
      clipboard () {
        let clipboard1 = new Clipboard('.copy-btn1')
        clipboard1.on('success', (e) => {
          this.$message.success('已复制')
        })
        let clipboard2 = new Clipboard('.copy-btn2')
        clipboard2.on('success', (e) => {
          this.$message.success('已复制')
        })
      },
      loginOut () {
        this.$confirm('确定退出吗?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          localStorage.removeItem('live_pwd')
          location.reload()
        })
      },
      reStart () {
        this.$confirm('确定重启吗?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          cloudApi.restartInstanceReq({}, () => {}, () => {})
          const loading = this.$loading({
            lock: true,
            text: '正在重启中',
            spinner: 'el-icon-loading',
            background: 'rgba(0, 0, 0, 0.7)'
          })
          setInterval(() => {
            liveApi.getConfig({}, res => {
              location.reload(true)
            }, () => {})
          }, 2000)
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消'
          })
        })
      },
      selectItem (item) {
        this.tabList.forEach(v => {
          v.active = false
        })
        item.active = true
        this.currentId = item.id
      },
      getMachineInfo () {
        cloudApi.getMachineInfo({liveId: 0}, res => {
          this.setVersionInfo({deviceCode: res.deviceCode, currentVersion: res.currentVersion, latestVersion: res.latestVersion, upgradeDownloadStatus: res.upgradeDownloadStatus})
          this.setMappedIp(res.accessIp)
          // 循环检测升级文件是否下载完成
          if (res.upgradeDownloadStatus === 'downloading') {
            setTimeout(() => {
              this.getMachineInfo()
            }, 5 * 60 * 1000)
          }
        })
      },
      ...mapMutations({
        setPwd: 'SET_PWD',
        setPortRange: 'SET_PPOR_INFO',
        setVersionInfo: 'SET_VERSION_INFO',
        setMappedIp: 'SET_MAPPED_IP'
      })
    },
    created () {
      this.clipboard()
      this.getWord()
      this.getMachineInfo()
    }
  }
</script>

<style scoped lang="scss">
  .live-setting {
    display: flex;
    justify-content: flex-start;
    .tab-item {
      padding: 14px 0 14px 12px;
      cursor: pointer;
      &:hover {
        background-color: #3E4553;
      }
      &.active {
        .tab-title {
          color: $color-theme;
        }
        background-color: #3E4553;
      }
    }
    .tab-title {
      font-size: 14px;
      color: #AEBBD9;
      line-height: 18px;
    }
    .left-tabs {
      padding-top: 29px;
      position: relative;
      flex: 0 0 120px;
      width: 120px;
      background-color: #373E4A!important;
      min-height: 356px;
      box-sizing: border-box;
      .footer {
        position: absolute;
        bottom: 20px;
        left: 50%;
        transform: translateX(-50%);
        z-index: 100;
      }
    }
    .right-content {
      flex: auto;
      box-sizing: border-box;

      .version-info {
        font-weight: 700;
        font-size: 20px;
        color: #AEBBD9;
        margin-right: 10px;
        margin-bottom: 20px;
      }
    }
  }
  ::v-deep .el-dialog__body {
    padding: 0;
    background-color: #3E4553!important;
  }
  .dialog-footer {
    @include flex-v-c();
    justify-content: flex-end;
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 65px;
    padding-right: 20px;
    box-sizing: border-box;
  }
  .pwd-set {
    padding: 20px 31px 0 20px;
    box-sizing: border-box;
    .icon-view {
      margin-top: 3px;
      display: inline-block;
      width: 20px;
      height: 20px;
      background: url('./visible.png') no-repeat;
      background-size: 20px;
      &:hover {
        background-image: url('./visible-hover.png');
      }
    }
    .icon-no-view {
      display: inline-block;
      width: 20px;
      height: 20px;
      background: url('./unvisible.png') no-repeat;
      background-size: 20px;
      &:hover {
        background-image: url('./unvisible-hover.png');
      }
    }
    ::v-deep .el-input__suffix {
      display: flex;
      align-items: center;
    }
    ::v-deep .el-input__inner {
      height: 34px;
    }
    .label {
      margin-bottom: 13px;
      font-size: 14px;
      color: #AEBBD9;
    }
    .tips {
      margin-bottom: 13px;
      font-size: 12px;
      font-family: SimSun;
      color: #79859F;
    }
  }
  .version-box {
    padding: 30px;
    box-sizing: border-box;
  }

  .upload-block {
    flex-direction: column;
    height: 200px;
    .tips-text {
      color: #AEBBD9;
      font-size: 20px;
      margin-bottom: 10px;
    }
  }
</style>
