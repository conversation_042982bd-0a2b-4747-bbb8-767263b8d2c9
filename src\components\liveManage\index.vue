<template>
  <div class="live-manage">
    <div class="page-top">
      <div class="btn-like">
        <i class="el-icon-plus add-icon"></i>
        新建直播
      </div>
      <div class="ipt-like">
        <el-input size="small" placeholder="输入直播名称" v-model.trim="keywords">
          <el-button slot="append" size="small" icon="el-icon-search">搜索</el-button>
        </el-input>
      </div>
    </div>
    <div class="page-body">
      <div class="table-column table-header">
        <div class="row-1">直播名称</div>
        <div class="row-2">类型</div>
        <div class="row-3">状态</div>
        <div class="row-4">创建时间</div>
        <div class="row-5">操作</div>
      </div>
      <div class="table-column" :key="item.id" v-for="(item, index) in liveList ">
        <div class="row-1">{{ item.name }}</div>
        <div class="row-2">{{ item.liveType === 0 ? '推流直播' : '拉流直播' }}</div>
        <div class="row-3">{{ liveStateMap[item.state] }}</div>
        <div class="row-4">{{ item.createTime }}</div>
        <div class="row-5">
          <div class="btn-like" @click="showAddrDialog(item)">直播地址</div>
          <div class="btn-like" @click="switchPullState(item)">开始拉流</div>
          <div class="btn-like" @click="viewItem(item)">预览</div>
          <div class="btn-like" @click="editItem(item)">编辑</div>
          <div class="btn-like" @click="banItem(item)">禁用</div>
          <div class="btn-like" @click="delItem(item, index)">删除</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import liveApi from 'api/live'

export default {
  components: {},
  props: {},
  computed: {},
  data() {
    this.liveStateMap = {
      0: '未直播',
      1: '直播中',
      2: '禁播',
      '-1': '删除'
    }
    return {
      keywords: '',
      liveList: []
    }
  },
  methods: {
    getLiveList() {
      liveApi.getLiveList({}, (res) => {
        console.log(12333)
        console.log(res)
        // this.liveList = res.data
      }, (code, msg) => {
        this.$message.error(msg)
      })
    },
    showAddrDialog(item) {
    },
    switchPullState(item) {
    },
    viewItem(item) {
    },
    editItem(item) {
    },
    banItem(item) {
    },
    delItem(item, index) {
    }
  },
  created() {
    // this.getLiveList()
  },
  mounted() {}
}
</script>

<style scoped lang="scss">
.live-manage {
  width: 100%;
  height: calc(100% - 13px);
  margin-top: 13px;
  overflow: hidden;

  .page-top {
    display: flex;
    align-items: center;
    justify-content: space-between;
    .btn-like {
      width: 92px;
      height: 32px;
      background-color: #e95c2b;
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 14px;
      color: #fff;
      cursor: pointer;
      border-radius: 4px;
      .add-icon {
        margin-right: 5px;
      }
    }
    .ipt-like {
      ::v-deep .el-input-group__append {
        background-color: #535d72;
        border-color: #535d72;
        color: #bec9e5;
      }
      ::v-deep .el-input__inner {
        border-color: #535d72 !important;
      }
      ::v-deep .el-input__inner::placeholder {
        color: #51586c;
      }
    }
  }
  .page-body {
    width: 100%;
    height: calc(100% - 48px);
    margin-top: 16px;
    overflow: auto;
    scrollbar-width: none; /* firefox */
    -ms-overflow-style: none; /* IE 10+ */
    &::-webkit-scrollbar {
      display: none; /* Chrome Safari */
    }
    .table-column {
      height: 46px;
      display: flex;
      font-size: 16px;
      line-height: 46px;
      color: #b4bfdb;
      &.table-header {
        color: #666d7e;
        background-color: #272c35;
      }
      .row-1 {
        flex: 0 0 92px;
        text-align: center;
      }
      .row-2 {
        flex: 1;
        padding: 0 30px;
        box-sizing: border-box;
        text-align: center;
      }
      .row-3 {
        flex: 0 0 132px;
        text-align: center;
      }
      .row-4 {
        flex: 0 0 157px;
        padding: 0 30px;
        box-sizing: border-box;
        .btn-like {
          font-size: 16px;
          color: #687ebd;
          cursor: pointer;
          &.mr-btn {
            margin-right: 33px;
          }
        }
      }
    }
  }
}
</style>
