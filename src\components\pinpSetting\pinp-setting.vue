<template>
  <el-dialog title="画中画设置" :visible.sync="showDialog" top="80px" :close-on-click-modal="false" @close="$emit('close')" :width="dialogWidth">
    <div class="pinp-setting">
      <div class="pinp-content-wrapper" style="position: relative" :style="getContentWH()">
        <img class="wh100" style="position: absolute;object-fit: cover" :src="this.supportHttps(this.$_getPicture(this.background))" alt="" v-show="settingForm.bgFlag && this.background">
        <vue-draggable-resizable :active="item.active" :parent="true" v-for="(item, index) in boxList" :z="item.zIndex" :x="item.left * screenScaleX"
                                 :y="item.top * screenScaleY" :w="item.width * screenScaleX" :h="item.height * screenScaleY" :handles="['tl', 'tr', 'br', 'bl']"
                                 @activated="pinpItemClick(index)" :snap="true" :snapTolerance="3" @dragstop="dragstop($event, index)"
                                 @resizestop="resizestop($event, index)" @refLineParams="getRefLineParams">
          <div class="pinp-item" :class="{isMaster: item.idx === 0}">
            <span class="title" v-show="item.idx === 0">主画面</span>
            <span class="title" v-show="item.idx !== 0">画面{{item.idx}}</span>
            <i class="el-icon-delete" v-show="item.idx !== 0" @click.stop="deletePinpItem(index)"></i>
          </div>
        </vue-draggable-resizable>
        <span class="ref-line v-line" v-for="item in vLine" :key="item.id" v-show="item.display"
              :style="{ left: item.position, top: item.origin, height: item.lineLength}" />
        <span class="ref-line h-line" v-for="item in hLine" :key="item.id" v-show="item.display"
              :style="{ top: item.position, left: item.origin, width: item.lineLength}" />
      </div>
      <div class="right-setting-box">
        <div class="form-item" style="margin-bottom: 20px">
          <div class="label-line">
            <span class="label">画中画功能：</span>
            <el-switch v-model="settingForm.pinpFlag" :width="35" active-text="" inactive-text="" active-color="#FF5B1F" inactive-color="#BCC9E8"></el-switch>
            <span class="tip2" style="margin-left: 10px">关闭后只能看到主画面</span>
          </div>
        </div>
        <div class="form-item">
          <div class="label-line"  v-if="false">
            <span class="label">背景图：</span>
            <el-switch v-model="settingForm.bgFlag" :width="35" active-text="" inactive-text="" active-color="#FF5B1F" inactive-color="#BCC9E8"></el-switch>
          </div>
        </div>
        <div class="form-item" style="margin-bottom: 6px">
          <div class="label-line">
            <span class="label">选择画面：</span>
            <el-select v-model="currentIdx" placeholder="请选择" size="small" @change="changeSelectIdx">
              <el-option v-for="item in boxList" :key="item.idx" :label="item.idx ? '画面' + item.idx : '主画面'" :value="item.idx"></el-option>
            </el-select>
          </div>
        </div>
        <div class="form-item" style="margin-bottom: 6px" v-show="currentIdx || currentIdx === 0">
          <div class="label-line">
            <span class="label">元素层级：</span>
            <span class="g-option-btn-2" style="width: 74px;height: 28px;line-height: 26px" @click="addIndex(currentIdx)">上移</span>
            <span class="g-option-btn-2" style="width: 74px;height: 28px;line-height: 26px" @click="reduceIndex(currentIdx)">下移</span>
          </div>
        </div>
        <div class="form-item" v-if="currentItem">
          <div class="label-line">
            <span class="label">基础属性：</span>
          </div>
          <div style="margin-bottom: 9px">
            <span style="margin-right: 12px;color: #8C97B1">X轴</span>
            <el-input-number style="width: 90px;margin-right: 9px" v-model="currentItem.left" controls-position="right" :min="0" size="small" @change="rightChange"></el-input-number>
            <span style="margin-right: 12px;color: #8C97B1">Y轴</span>
            <el-input-number style="width: 90px;" v-model="currentItem.top" controls-position="right" :min="0" size="small" @change="rightChange"></el-input-number>
          </div>
          <div>
            <span style="margin-right: 12px;margin-left: 10px;color: #8C97B1">宽</span>
            <el-input-number style="width: 90px;margin-right: 9px" v-model="currentItem.width" controls-position="right" :min="0" :max="liveWidth" size="small" @change="rightChange"></el-input-number>
            <span style="margin-right: 12px;margin-left: 10px;color: #8C97B1">高</span>
            <el-input-number style="width: 90px;" v-model="currentItem.height" controls-position="right" :min="0" :max="liveHeight" size="small" @change="rightChange"></el-input-number>
          </div>
        </div>
        <div v-for="item in moveEndList" v-show="item.idx !== 0 && item.active">
          <div class="form-item">
            <div class="label-line">
              <span class="label">输入源：</span>
            </div>
            <el-radio v-model="item.type" :label="'stream'">直播流</el-radio>
            <el-radio v-model="item.type" :label="'record'">点播流</el-radio>
            <el-radio v-model="item.type" :label="'pic'">图片</el-radio>
            <el-input size="small" v-model.trim="item.url" placeholder="请输入源地址" style="width: 350px;margin-top: 10px"></el-input>
            <p class="tip2">默认带出来所选画面链接，不能为空，其中主要源不能在这里删改</p>
          </div>
          <div class="form-item">
            <div class="label-line">
              <span class="label">音频调节：</span>
            </div>
          </div>
          <div style="display: flex;justify-content: space-between;align-items: center">
            <el-slider class="slider" :max="100" v-model="item.voiceNumber" style="width: 280px"></el-slider>
            <el-input-number :min="0" :max="100" v-model.trim="item.voiceNumber" :controls="false" style="width: 57px" size="small"></el-input-number>
          </div>
        </div>
      </div>
    </div>
    <div class="dialog-footer">
      <span>
        <el-button type="info" size="small" @click="addPinpItem">添加画面</el-button>
        <el-button type="info" size="small" @click="resetHandle">重置</el-button>
      </span>
      <el-button size="small" type="primary" @click="saveOption">保存并应用</el-button>
    </div>
  </el-dialog>
</template>

<script>
  import VueDraggableResizable from '@/plugin/vue-draggable-resizable/components/vue-draggable-resizable'
  import '@/plugin/vue-draggable-resizable/components/vue-draggable-resizable.css'
  import {mapActions, mapGetters} from 'vuex'
  import {initBoxList1, initBoxList2} from './initList'
  import cloudApi from 'api/cloud'

  export default {
    name: 'pinp-setting',
    components: {VueDraggableResizable},
    props: {
      programType: {
        type: String,
        default: ''
      },
      scheduleId: { // 节目编排Id
        type: Number,
        default: 0
      },
      manageId: { // 节目管理ID
        type: Number,
        default: 0
      },
      currentProgramItem: {
        type: Object,
        default: () => {
        }
      }
    },
    watch: {
    },
    computed: {
      currentItem () {
        return this.boxList.find(v => v.active)
      },
      getChannels () {
        let moveEndList = this.moveEndList
        moveEndList = moveEndList.sort((a, b) => {
          return a.zIndex - b.zIndex
        })
        let channels = moveEndList.map(item => {
          return {
            type: item.idx === 0 ? this.masterItem.type : item.type,
            url: item.idx === 0 ? this.masterItem.url : item.url,
            idx: item.idx,
            crop: item.crop || null,
            colorTone: item.colorTone || null,
            lut: item.lut || null,
            localFileName: item.idx === 0 ? this.masterItem.localFileName : '',
            display: {
              dx: parseInt(this.getMaxLeft(parseInt(item.width), item.left)),
              dy: parseInt(this.getMaxTop(parseInt(item.height), item.top)),
              width: Math.min(parseInt(item.width), this.liveWidth),
              height: Math.min(parseInt(item.height), this.liveHeight)
            },
            volume: item.voiceNumber
          }
        })
        return channels
      },
      screenScaleX () { // 屏幕缩放比率
        return this.optionAreaWidth / this.liveWidth
      },
      screenScaleY () { // 屏幕缩放比率
        return this.optionAreaHeight / this.liveHeight
      },
      dialogWidth () {
        return this.optionAreaWidth + 425 + 'px'
      },
      ...mapGetters([
        'liveRatio',
        'liveWidth',
        'liveHeight',
        'background'
      ])
    },
    data () {
      return {
        currentIdx: null,
        currentIndex: 0,
        masterType: '',
        masterUrl: '',
        vLine: [],
        hLine: [],
        maxIndex: 2,
        boxList: [],
        moveEndList: [],
        showDialog: true,
        optionAreaWidth: 0,
        optionAreaHeight: 0,
        settingForm: {
          bgFlag: false,
          pinpFlag: false
        }
      }
    },
    methods: {
      addIndex (idx) {
        let index = this.boxList.findIndex(v => v.idx === idx)
        if (this.boxList[index].zIndex === this.maxIndex) {
          return
        }
        this.boxList[index].zIndex += 1
        let i = this.boxList[index].zIndex
        this.boxList.find(v => {
          if (v.zIndex === i && v.idx !== idx) {
            v.zIndex -= 1
          }
        })
        this.moveEndList[index].zIndex += 1
        this.moveEndList.find(v => {
          if (v.zIndex === i && v.idx !== idx) {
            v.zIndex -= 1
          }
        })
      },
      reduceIndex (idx) {
        let index = this.boxList.findIndex(v => v.idx === idx)
        if (this.boxList[index].zIndex === 1) {
          return
        }
        this.boxList[index].zIndex -= 1
        let i = this.boxList[index].zIndex
        this.boxList.find(v => {
          if (v.zIndex === i && v.idx !== idx) {
            v.zIndex += 1
          }
        })
        this.moveEndList[index].zIndex -= 1
        this.moveEndList.find(v => {
          if (v.zIndex === i && v.idx !== idx) {
            v.zIndex += 1
          }
        })
      },
      // 辅助线回调事件
      getRefLineParams (params) {
        const {vLine, hLine} = params
        let id = 0
        this.vLine = vLine.map(item => {
          item['id'] = ++id
          return item
        })
        this.hLine = hLine.map(item => {
          item['id'] = ++id
          return item
        })
      },
      changeSelectIdx (idx) {
        let index = this.boxList.findIndex(v => v.idx === idx)
        this.pinpItemClick(index)
      },
      setPinpBg () {
        this.$bus.$emit('openLiveSetting', 4)
        this.$emit('close')
      },
      getMaxLeft (width, left) {
        return Math.min(this.liveWidth - width, left) > 0 ? Math.min(this.liveWidth - width, left) : 0
      },
      getMaxTop (height, top) {
        return Math.min(this.liveHeight - height, top) > 0 ? Math.min(this.liveHeight - height, top) : 0
      },
      saveOption () {
        if (this.currentProgramItem) {
          let editProgram = this.currentProgramItem
          // 检查是否有主画面
          if (!this.boxList.find(v => v.idx === 0)) {
            this.$message.error('无主画面无法保存')
            return
          }
          let channels = this.getChannels
          console.log(channels)
          editProgram.video = {
            useBackground: this.settingForm.bgFlag,
            usePip: this.settingForm.pinpFlag,
            channels: channels
          }
          if (this.manageId > 0) {
            let param = {
              id: this.manageId,
              program: JSON.stringify(editProgram)
            }
            cloudApi.setProgram(param, () => {
              this.$bus.$emit('refreshProgramManage')
              this.$emit('close')
            })
          } else if (this.scheduleId > 0) {
            let param = {
              id: this.scheduleId,
              program: JSON.stringify(editProgram)
            }
            cloudApi.uploadProgramArrangementById(param, () => {
              this.$bus.$emit('refreshProgramManage')
              this.$emit('close')
            })
          } else {
            let param = {
              programType: this.programType,
              program: editProgram
            }
            this.modProgram(param).then(() => {
              this.$emit('close')
            })
          }
        }
      },
      addPinpItem () {
        let newObj = {
          active: false,
          left: 0,
          top: 0,
          width: parseInt(this.liveWidth / 3),
          height: parseInt(this.liveHeight / 3),
          zIndex: this.maxIndex,
          idx: this.getNewIdx(),
          url: '',
          voiceNumber: 10,
          type: 'stream',
          localFileName: ''
        }
        this.boxList.push(newObj)
        this.moveEndList.push(this.$_deepCopy(newObj))
        ++this.maxIndex
      },
      getNewIdx () {
        let max = 0
        this.boxList.forEach(v => {
          max = Math.max(max, v.idx)
        })
        return Number(max) + 1
      },
      deletePinpItem (index) {
        this.$confirm('确定删除吗?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.boxList.splice(index, 1)
          this.moveEndList.splice(index, 1)
        }).catch(() => {
        })
      },
      pinpItemClick (index) {
        this.boxList.forEach(item => {
          item.active = false
        })
        // this.boxList[index].zIndex = this.maxIndex + 1
        // ++this.maxIndex
        this.boxList[index].active = true
        this.currentIdx = this.boxList[index].idx
        this.moveEndList.forEach(item => {
          item.active = false
        })
        this.moveEndList[index].active = true
      },
      rightChange () {
        Object.assign(this.moveEndList[this.currentIndex], {
          left: this.currentItem.left,
          top: this.currentItem.top,
          width: this.currentItem.width,
          height: this.currentItem.height
        })
      },
      resizestop (obj, index) {
        this.currentIndex = index
        Object.assign(this.moveEndList[index], {
          left: parseInt(obj.left / this.screenScaleX),
          top: parseInt(obj.top / this.screenScaleY),
          width: parseInt(this.liveWidth - obj.width / this.screenScaleX < 3 ? this.liveWidth : obj.width / this.screenScaleX),
          height: parseInt(this.liveHeight - obj.height / this.screenScaleY < 3 ? this.liveHeight : obj.height / this.screenScaleY)
        })
        Object.assign(this.boxList[index], {
          left: parseInt(obj.left / this.screenScaleX),
          top: parseInt(obj.top / this.screenScaleY),
          width: parseInt(this.liveWidth - obj.width / this.screenScaleX < 3 ? this.liveWidth : obj.width / this.screenScaleX),
          height: parseInt(this.liveHeight - obj.height / this.screenScaleY < 3 ? this.liveHeight : obj.height / this.screenScaleY)
        })
      },
      dragstop (obj, index) {
        this.currentIndex = index
        Object.assign(this.moveEndList[index], {
          left: parseInt(obj.left < 0 ? 0 : obj.left / this.screenScaleX),
          top: parseInt(obj.top < 0 ? 0 : obj.top / this.screenScaleY),
          width: parseInt(this.liveWidth - obj.width / this.screenScaleX < 3 ? this.liveWidth : obj.width / this.screenScaleX),
          height: parseInt(this.liveHeight - obj.height / this.screenScaleY < 3 ? this.liveHeight : obj.height / this.screenScaleY),
          zIndex: this.boxList[index].zIndex
        })
        Object.assign(this.boxList[index], {
          left: parseInt(obj.left < 0 ? 0 : obj.left / this.screenScaleX),
          top: parseInt(obj.top < 0 ? 0 : obj.top / this.screenScaleY),
          width: parseInt(this.liveWidth - obj.width / this.screenScaleX < 3 ? this.liveWidth : obj.width / this.screenScaleX),
          height: parseInt(this.liveHeight - obj.height / this.screenScaleY < 3 ? this.liveHeight : obj.height / this.screenScaleY),
          zIndex: this.boxList[index].zIndex
        })
      },
      getContentWH () {
        if (this.liveRatio > 1) {
          this.optionAreaHeight = window.innerHeight * 0.6
        } else {
          this.optionAreaHeight = window.innerHeight * 0.7
        }
        this.optionAreaWidth = this.optionAreaHeight * this.liveRatio
        return {
          width: this.optionAreaWidth + 'px',
          height: this.optionAreaHeight + 'px'
        }
      },
      getInitBoxList () {
        let list = []
        if (this.liveRatio === 16 / 9) {
          list = this.$_deepCopy(initBoxList1(this.liveWidth, this.liveHeight))
        } else if (this.liveRatio === 4 / 3) {
          list = this.$_deepCopy(initBoxList1(this.liveWidth, this.liveHeight))
        } else if (this.liveRatio === 9 / 16) {
          list = this.$_deepCopy(initBoxList2(this.liveWidth, this.liveHeight))
        } else if (this.liveRatio === 4 / 9) {
          list = this.$_deepCopy(initBoxList2(this.liveWidth, this.liveHeight))
        }
        list.find(item => item.idx === 0).crop = this.$_deepCopy(this.currentProgramItem.video.channels.find(item => item.idx === 0).crop)
        list.find(item => item.idx === 0).colorTone = this.$_deepCopy(this.currentProgramItem.video.channels.find(item => item.idx === 0).colorTone)
        list.find(item => item.idx === 0).lut = this.$_deepCopy(this.currentProgramItem.video.channels.find(item => item.idx === 0).lut)
        return list
      },
      resetHandle () {
        this.moveEndList = this.getInitBoxList()
        this.boxList = []
        setTimeout(() => {
          this.boxList = this.getInitBoxList()
        }, 100)
      },
      initPinp () {
        this.masterItem = this.currentProgramItem.video.channels.find(item => item.idx === 0)
        let checkFlag = true
        if (this.masterItem.display && this.masterItem.display.height !== 0) {
          let list = []
          let zIndex = 1
          this.currentProgramItem.video.channels.forEach(item => {
            list.push({
              active: false,
              left: item.display.dx,
              top: item.display.dy,
              width: item.display.width,
              height: item.display.height,
              zIndex: zIndex,
              idx: item.idx,
              url: item.url,
              voiceNumber: item.volume,
              type: item.type,
              localFileName: item.localFileName,
              crop: item.crop,
              colorTone: item.colorTone,
              lut: item.lut
            })
            ++zIndex
            this.maxIndex = zIndex
            if (Math.abs(item.display.dx) + item.display.width > this.liveWidth) {
              checkFlag = false
            }
            if (Math.abs(item.display.dy) + item.display.height > this.liveHeight) {
              checkFlag = false
            }
          })
          this.boxList = []
          this.$nextTick(() => {
            this.boxList = this.$_deepCopy(list)
            this.moveEndList = this.$_deepCopy(list)
          })
          if (!checkFlag) {
            this.resetTip()
          }
        } else {
          this.$nextTick(() => {
            this.boxList = this.getInitBoxList()
            this.moveEndList = this.getInitBoxList()
            console.log(this.moveEndList)
          })
        }
      },
      resetTip () {
        this.$confirm('画中画分辨率与输出分辨率不符，是否重置', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.resetHandle()
        }).catch(() => {})
      },
      setInit () {
        this.settingForm.bgFlag = this.currentProgramItem.video.useBackground
        this.settingForm.pinpFlag = this.currentProgramItem.video.usePip
      },
      ...mapActions(['modProgram'])
    },
    mounted () {
      this.setInit()
      this.initPinp()
    }
  }
</script>

<style scoped lang="scss">
  .pinp-setting {
    display: flex;
    justify-content: flex-start;

    .pinp-content-wrapper {
      background-color: #272C35;

      .pinp-item {
        width: 100%;
        height: 100%;
        border: 2px solid transparent;
        box-sizing: border-box;
        background: url("./pinp-bg.png") #3C4451 no-repeat center;
        background-size: 50%;
        border: 2px solid #BAC6DA;
        cursor: pointer;
        &:hover {
          border: 2px solid #BAC6DA;
        }

        .title {
          display: inline-block;
          margin-top: 2%;
          margin-left: 2%;
          font-size: 16px;
          font-family: Microsoft YaHei;
          color: #B8C2D6;
          line-height: 18px;
        }
      }

      .el-icon-delete {
        display: none;
        position: absolute;
        right: 2%;
        top: 2%;
        font-size: 18px;
        color: #C8D1DE;
        cursor: pointer;

        &:hover {
          color: $color-theme;
        }
      }
    }

    .right-setting-box {
      width: 356px;
      margin-left: 25px;
    }
  }

  .form-item {
    margin-bottom: 25px;

    .label-line {
      margin-bottom: 16px;

      .label {
        font-size: 14px;
        font-family: SimSun;
        color: #AEBBD9;
        line-height: 20px;
      }
    }

    .tip1 {
      font-size: 14px;
      font-family: SimSun;
      color: #4386EB;
      line-height: 20px;
      cursor: pointer;

      &:hover {
        color: $color-theme;
      }
    }

    .tip2 {
      font-size: 12px;
      font-family: SimSun;
      color: $color-tips;
      line-height: 20px;
    }
  }

  .dialog-footer {
    display: flex;
    justify-content: space-between;
    margin-top: 20px;
  }

  /deep/ .vdr,
  .vdr.active:before {
    border: none;
  }

  /deep/ .handle {
    opacity: 0;
  }

  .active.draggable {
    .pinp-item {
      border-color: $color-theme;
      &:hover {
        border-color: $color-theme;
      }
    }

    .el-icon-delete {
      display: block;
    }
  }

  .ref-line {
    background-color: $color-theme;
  }
  /deep/ .el-slider__runway {
    background-color: #8692A9;
  }
  /deep/ .el-input-number.is-controls-right .el-input__inner {
      padding-left: 0;
      padding-right: 30px;
    }
</style>
