<template>
  <el-pagination class="v-pagination" @current-change="clickPagination" :current-page.sync="page.pageIndex"
                 :page-size="page.pageSize" layout="total, prev, pager, next, jumper, slot" :total="page.amount" background>
    <slot>
      <div class="jumper">
        <div class="xgbtn" :class="{small: small}">跳转</div>
      </div>
    </slot>
  </el-pagination>
</template>

<script>
  export default {
    name: 'pagination',
    props: {
      small: {
        type: Boolean,
        default: false
      },
      page: {
        type: Object
      }
    },
    methods: {
      clickPagination (page) {
        this.$emit('clickPage', page)
      }
    }
  }
</script>

<style lang="scss" scoped>
  .v-pagination {
    display: flex;
    align-items: center;
    justify-content: center;
    .jumper {
      font-size: 13px;
    }
    .xgbtn {
      display: flex;
      justify-content: center;
      align-items: center;
      margin-left: 10px;
      min-width: 40px;
      cursor: pointer;
      width: 70px;
      height: 28px!important;
      background: #535D72;
      border-radius: 3px;
      &.small {
        height: 25px;
      }
    }
  }
</style>
