<template>
  <div class="instance-list-area wh100">
    <div class="instance-li" v-for="item in instanceList" :key="item.id">
      <div class="img-cover">
        <loading v-if="loading"></loading>
        <instance-video :instanceId="item.id" @instanceLoading="instanceLoading"></instance-video>
        <span class="live-tag" v-show="item.info && item.info.isLiving">直播中</span>
        <div class="mask">
          <span class="common-btn" @click="gotoInstance(item)">播控</span>
        </div>
      </div>
      <div class="bottom-info">
        <span>{{item.name}}</span>
      </div>
    </div>
    <div class="instance-empty" v-for="item in 4" :key="item + '_'"></div>
  </div>
</template>

<script>
import instanceVideo from '@/components/instanceList/instance-video'
import ChannelData from 'common/js/channel-data'
import webrtcMgr from 'common/js/webrtc-manager'
import cloudApi from 'api/cloud'
import loading from '@/components/base/loading'
import {mapGetters} from 'vuex'

export default {
  data () {
    return {
      loading: true,
      instanceList: []
    }
  },
  computed: {
    ...mapGetters([
      'pwd'
    ])
  },
  components: {instanceVideo, loading},

  methods: {
    instanceLoading (flag) {
      setTimeout(() => {
        this.loading = flag
      }, 500)
    },
    initData () {
      cloudApi.getInstanceList({}, res => {
        if (res && res.length) {
          this.instanceList = res.map(item => {
            return {
              id: item.id,
              name: item.name,
              info: null
            }
          })
        }
        this.initChannelDate()
      })
    },
    initChannelDate () {
      this.instanceList.forEach(item => {
        new ChannelData(item.id).bind((res) => {
          item.info = JSON.parse(res)
        })
      })
    },
    gotoInstance (item) {
      this.$router.push('/instance/' + item.id)
    }
  },

  created () {
    this.initData()
    window.onunload = function () {
      webrtcMgr.destroyAll()
      webrtcMgr.destroyAllVideo()
    }
  },
  beforeDestroy () {
    webrtcMgr.destroyAll()
    webrtcMgr.destroyAllVideo()
  }
}
</script>

<style scoped lang="scss">
  .instance-list-area {
    padding: 9px 15px 0 15px;
    display: flex;
    justify-content: space-around;
    align-content: flex-start;
    flex-wrap: wrap;
    background-color: $color-border2;
    box-sizing: border-box;
    overflow-x: hidden;
    overflow-y: auto;
    .instance-empty {
      width: 468px;
      height: 0;
    }
    .instance-li {
      padding: 10px 9px 0 9px;
      width: 468px;
      height: 314px;
      border-radius: 6px;
      box-sizing: border-box;
      &:hover {
        background: #3F4654;
        .mask {
          display: flex;
        }
      }
    }
    .img-cover {
      position: relative;
      height: 253px;
      border-radius: 6px;
      background-color: #373D4A;
      overflow: hidden;
    }
    .live-tag {
      position: absolute;
      left: 0;
      top: 0;
      display: inline-block;
      width: 56px;
      height: 24px;
      line-height: 24px;
      text-align: center;
      background: #E91010;
      border-radius: 0 0 6px 0;
      color: #fff;
    }
    .bottom-info {
      display: flex;
      align-items: center;
      padding-left: 5px;
      height: 48px;
      font-size: 18px;
      font-family: Microsoft YaHei;
      color: #B2BFDE;
      @include elliptical();
      box-sizing: border-box;
    }
    .mask {
      display: none;
      justify-content: center;
      align-items: center;
      position: absolute;
      left: 0;
      top: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(0, 0, 0, .6);
    }
    .common-btn {
      display: inline-block;
      width: 122px;
      height: 40px;
      background: #FC4F08;
      border-radius: 20px;
      font-size: 18px;
      font-family: Microsoft YaHei;
      color: #FFFFFF;
      text-align: center;
      line-height: 40px;
      user-select: none;
      cursor: pointer;
      &:hover {
        opacity: .8;
      }
    }
  }
</style>
