<template>
  <div class="live-stream-renderer">
    <div v-if="component.previewImg" class="stream-preview" :style="streamBackgroundStyle">
      <!-- 这里可以集成实际的直播流预览 -->
    </div>
    <div v-else class="preview-placeholder">
      <i class="el-icon-video-camera"></i>
      <span>直播源</span>
    </div>
  </div>
</template>

<script>
export default {
  name: 'LiveStreamRenderer',
  props: {
    component: {
      type: Object,
      required: true
    },
    isSelected: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    streamBackgroundStyle() {
      // 将 objectFit 值转换为对应的 backgroundSize 值
      const getBackgroundSize = (objectFit) => {
        switch (objectFit) {
          case 'contain':
            return 'contain'
          case 'cover':
            return 'cover'
          case 'fill':
            return '100% 100%'
          default:
            return 'contain'
        }
      }

      return {
        width: '100%',
        height: '100%',
        backgroundImage: `url(${this.component.previewImg})`,
        backgroundSize: getBackgroundSize(this.component.objectFit),
        backgroundPosition: 'center',
        backgroundRepeat: 'no-repeat'
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.live-stream-renderer {
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.preview-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: rgba(255, 107, 53, 0.1);
  border: 1px dashed rgba(255, 107, 53, 0.3);
  color: #ff6b35;
  font-size: 12px;

  i {
    font-size: 24px;
    margin-bottom: 4px;
  }
}

.stream-preview {
  width: 100%;
  height: 100%;
  background: #000000;
  display: flex;
  align-items: center;
  justify-content: center;
  img {
    width: 100%;
    height: 100%;
  }
  .stream-info {
    text-align: center;
    color: #ff6b35;

    i {
      font-size: 32px;
      margin-bottom: 8px;
      display: block;
    }

    span {
      display: block;
      font-size: 14px;
      margin-bottom: 4px;
    }

    .stream-url {
      font-size: 10px;
      opacity: 0.8;
      max-width: 200px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
}
</style>
