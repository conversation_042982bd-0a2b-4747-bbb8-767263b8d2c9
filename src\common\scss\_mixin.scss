@mixin bottom-1px($color) {
  position: relative;
  &:after {
    display: block;
    position: absolute;
    left: 0;
    bottom: 0;
    width: 100%;
    border-top: 1px solid $color;
    content: ' ';
  }
}

@mixin border-none() {
  &:after {
    display: none;
  }
}

// 超出一行省略号
@mixin elliptical() {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
// 超出多行（$num）省略号
@mixin ellipticals($num) {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: $num;
  overflow: hidden;
}

@mixin flex-c-c() {
  display: flex;
  align-items: center;
  justify-content: center;
}

@mixin flex-v-c() {
  display: flex;
  align-items: center;
}

@mixin flex-h-c() {
  display: flex;
  align-items: center;
}

@mixin cut-line {
  &:before {
    content: "";
    font-size: 0;
    padding: 12px 4px 2px;
    margin-left: 6px;
    border-left: 1px solid $color-border;
  }
}

@mixin hide-scroll {
  position: absolute;
  left: 0;
  top: 0;
  right: -17px;
  bottom: 0;
  overflow-x: hidden;
  overflow-y: scroll;
}

