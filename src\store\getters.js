// 获取state有时需要对state做一些映射，例如对state的数据进行筛选、查找等

export const instanceId = state => state.instanceId
export const pwd = state => state.pwd
export const liveId = state => state.liveId
export const timeDifference = state => state.timeDifference
export const secretKey = state => state.secretKey
export const httpEndPoint = state => state.httpEndPoint
export const liveWidth = state => state.liveWidth
export const liveHeight = state => state.liveHeight
export const Emergency = state => state.Emergency
export const warnSetting = state => state.warnSetting
export const audio = state => state.audio
export const liveRatio = (state, getters) => {
  let ratio = getters.liveWidth / getters.liveHeight
  return ratio
}
export const cgs = state => state.cgs
export const output = state => state.output
export const programs = state => state.programs || []
export const todayPrograms = state => state.todayPrograms || []
export const loopPrograms = state => state.loopPrograms || []
export const loopStatus = state => state.loopStatus
export const defaultPic = state => state.defaultPic
export const versionInfo = state => state.versionInfo
export const localTimeDifference = state => state.localTimeDifference
export const programsSn = state => state.programsSn
export const background = state => state.background
export const roleType = state => state.roleType
export const machineInfo = state => state.machineInfo
export const localStorageInfo = state => state.localStorageInfo
export const portRange = state => state.portRange
export const schedules = state => state.schedules || []
export const currentScheduleId = state => state.currentScheduleId
export const cardNumber = state => state.cardNumber
export const mappedIp = state => state.mappedIp
export const hasParentLimit = state => state.hasParentLimit
export const layouts = state => state.layouts || []
export const currentLayoutId = state => state.currentLayoutId
