import Server from './request'

let getContentType = function (fileType) {
  let contentType = ''
  switch (fileType.toLowerCase()) {
    case 'dae':
      contentType = 'model/vnd.collada+xml'
      break
    case 'obj':
      contentType = 'application/x-tgif'
      break
    case 'mtl':
      contentType = 'application/octet-stream'
      break
    case 'dds':
      contentType = 'application/octet-stream'
      break
    case 'jpg':
      contentType = 'image/jpeg'
      break
    case 'jpeg':
      contentType = 'image/jpeg'
      break
    case 'png':
      contentType = 'image/png'
      break
    case 'bmp':
      contentType = 'image/x-bmp'
      break
    case 'mp4':
      contentType = 'video/mp4'
      break
    case 'h264':
      contentType = 'video/h264'
      break
    case 'm4a':
      contentType = 'audio/mp4a-latm'
      break
    case 'avi':
      contentType = 'application/octet-stream'
      break
    case 'wmv':
      contentType = 'application/octet-stream'
      break
    case '3gp':
      contentType = 'application/octet-stream'
      break
    case 'mkv':
      contentType = 'application/octet-stream'
      break
    case 'flv':
      contentType = 'application/octet-stream'
      break
    case 'mov':
      contentType = 'application/octet-stream'
      break
    case 'mpg':
      contentType = 'application/octet-stream'
      break
    case 'ts':
      contentType = 'application/octet-stream'
      break
    case 'mts':
      contentType = 'application/octet-stream'
      break
    case 'asf':
      contentType = 'application/octet-stream'
      break
    case 'mp3':
      contentType = 'audio/mpeg'
      break
    case 'wma':
      contentType = 'audio/x-ms-wma'
      break
    case 'wav':
      contentType = 'audio/wav'
      break
    case 'ac3':
      contentType = 'audio/ac3'
      break
    case 'amr':
      contentType = 'audio/amr'
      break
    case 'ogg':
      contentType = 'application/ogg'
      break
    case 'mid':
      contentType = 'audio/mid'
      break
    case 'zip':
      contentType = 'application/zip'
      break
    case 'rar':
      contentType = 'application/x-rar-compressed'
      break
    case 'doc':
      contentType = 'application/msword'
      break
    case 'docx':
      contentType = 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
      break
    case 'xls':
      contentType = 'application/vnd.ms-excel'
      break
    case 'xlsx':
      contentType = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      break
    case 'pdf':
      contentType = 'application/pdf'
      break
    case 'txt':
      contentType = 'text/plain'
      break
    case 'ppt':
    case 'pptx':
      contentType = 'application/vnd.ms-powerpoint'
      break
    case 'html':
    case 'htm':
      contentType = 'text/html'
      break
    case 'p12':
      contentType = 'application/x-pkcs12'
      break
    default :
      break
  }
  return contentType
}
let getUploadType = function (fileType) {
  switch (fileType.toLowerCase()) {
    case 'gif':
      return 'gif'
    case 'jpg':
      return 'picture'
    case 'jpeg':
      return 'picture'
    case 'png':
      return 'picture'
    case 'bmp':
      return 'picture'
    case 'mp4':
      return 'video'
    case 'avi':
      return 'video'
    case 'wmv':
      return 'video'
    case '3gp':
      return 'video'
    case 'mkv':
      return 'video'
    case 'flv':
      return 'video'
    case 'mov':
      return 'video'
    case 'mpg':
      return 'video'
    case 'ts':
      return 'video'
    case 'asf':
      return 'video'
    case 'h264':
      return 'video'
    case 'mts':
      return 'video'
    case 'mp3':
    case 'wma':
    case 'wav':
    case 'ogg':
    case 'mid':
    case 'm4a':
    case 'amr':
    case 'ac3':
      return 'audio'
    case 'zip':
      return 'doc'
    case 'rar':
      return 'doc'
    case 'doc':
      return 'doc'
    case 'docx':
      return 'doc'
    case 'xls':
      return 'doc'
    case 'xlsx':
      return 'doc'
    case 'pdf':
      return 'doc'
    case 'txt':
    case 'ppt':
    case 'pptx':
    case 'html':
    case 'htm':
    case 'p12':
      return 'doc'
    default :
      break
  }
  return 'undefined'
}
function verificationPicFile (file) {
  return new Promise(resolve => {
      if (file) {
        var f = file
        var reader = new FileReader()
        reader.onload = function (e) {
            var data = e.target.result
            var image = new Image()
            image.onload = function() {
                var width = image.width
                var height = image.height
                if (width <= 3840 && height <= 3840) {
                  resolve('')
                } else {
                  return resolve('暂不支持超过4k分辨率的图片')
                }
            }
            image.src = data
        }
        reader.readAsDataURL(f)
    } else {
      return resolve('图片读取失败')
    }
  })
}
function checkFileSize (uploadType, fileSize) {
  if (uploadType === 'picture' && fileSize > (20 * 1024 * 1024)) {
    return '图片上传支持最大20M'
  }
  if (uploadType === 'audio' && fileSize > (100 * 1024 * 1024)) {
    return '音频上传支持最大100M'
  }
  if (uploadType === 'video' && fileSize > (4 * 1024 * 1024 * 1024)) {
    return '视频上传支持最大4G'
  }
  if (uploadType === 'doc' && fileSize > (500 * 1024 * 1024)) {
    return '文件上传支持最大500M'
  }
  return ''
}
function uploadFile(value, file, uploadSuccess, uploadError, setProgress) {
  console.log(1111111111111)
  console.log(value)
  let fileType = file.name.substring(file.name.lastIndexOf('.') + 1, file.name.length)
  let contentType = getContentType(fileType)
  let fd = new FormData()
  let key = value.path
  fd.append('key', key)
  if (value.type === 'oss') {
    fd.append('Content-Type', contentType)
    fd.append('OSSAccessKeyId', value.id)
    fd.append('policy', value.policy)
    fd.append('signature', value.signature)
  } else {
    fd.append('Signature', value.signature)
    fd.append('success_action_status', '200')
  }
  fd.append('file', file)
  file.path = key
  let xhr = new XMLHttpRequest()
  file.xhr = xhr
  if (xhr.upload) {
    xhr.upload.addEventListener('progress', function () {
      setProgress && setProgress(event, file)
    }, false)
    xhr.upload.addEventListener('load', function (e) {
      uploadSuccess && uploadSuccess(file, file.path)
    }, false)
    xhr.upload.addEventListener('error', function (e) {
      uploadError && uploadError(file)
    }, false)
  }
  xhr.open('post', value.url, true)
  xhr.send(fd)
}
export default {
  // 不需要登陆
  getImgPromise: function (uploadInfo, file, uploadMultiFile, customPathPrefix) {
    return new Promise((resolve, reject) => {
      // type: 文件类型：'':默认空字段显示图片（与picture不同），video:视频, audio: 音频, doc:文件，picture:素材图片, 3d:3d文件 （注，与默认的图片不同的是，存储的路径不同，这里主要给素材图片用）
      Server.request('/cloud/upload/getImgKeyset', {name: file.name}, function (value) {
        let fileType = file.name.substring(file.name.lastIndexOf('.') + 1, file.name.length)
        let contentType = getContentType(fileType)
        if (contentType === '') {
          reject(new Error(false))
        }
        let fileName = value.fileName
        if (uploadInfo.uploadType.startsWith('3d')) {
          fileName = file.name.substring(0, file.name.lastIndexOf('.'))
        }
        if (uploadMultiFile) {
          uploadInfo.AuthData.multiFile[file.uid] = value.pathPrefix + fileName + '.' + fileType.toLowerCase()
        }
        if (!customPathPrefix) {
          uploadInfo.AuthData['key'] = value.pathPrefix + fileName + '.' + fileType.toLowerCase()
        } else {
          uploadInfo.AuthData['key'] = customPathPrefix + fileName + '.' + fileType.toLowerCase()
        }
        uploadInfo.uploadUrl = value.url
        uploadInfo.visitUrl = value.visitUrl.replace('http://', 'https://')
        if (value.type === 'oss') {
          uploadInfo.AuthData['Content-Type'] = contentType
          uploadInfo.AuthData['OSSAccessKeyId'] = value.id
          uploadInfo.AuthData['policy'] = value.policy
          uploadInfo.AuthData['signature'] = value.signature
        } else {
          uploadInfo.AuthData['success_action_status'] = '200'
          uploadInfo.AuthData['Signature'] = value.signature
        }
        resolve(true)
      }, function (code, value) {
        alert(value)
        reject(new Error(false))
      })
    })
  },
  getContentType: getContentType,
  getUploadType: getUploadType,
  checkFileSize: checkFileSize,
  verificationPicFile,
  uploadFile (uploadUrl, file, uploadSuccess, uploadError, setProgress) {
    let fd = new FormData()
    fd.append('file', file)
    let xhr = new XMLHttpRequest()
    file.xhr = xhr
    if (xhr.upload) {
      xhr.upload.addEventListener('progress', function () {
        setProgress && setProgress(event, file)
      }, false)
      xhr.onload = () => {
        let res = JSON.parse(xhr.responseText)
        uploadSuccess && uploadSuccess(file, res.value.visitUrl)
      }
      xhr.upload.addEventListener('error', function (e) {
        uploadError && uploadError(file)
      }, false)
    }
    xhr.open('post', uploadUrl, true)
    xhr.send(fd)
  }
}
