<template>
  <div class="display-mode-properties">
    <div class="property-section">
      <div class="section-title">画面显示</div>
      <div class="property-group">
        <div class="display-mode-buttons">
          <el-tooltip
            v-for="mode in displayModes"
            :key="mode.value"
            :content="mode.tooltip"
            placement="top"
            effect="dark"
          >
            <button
              class="mode-btn"
              :class="{ active: component.objectFit === mode.value }"
              @click="handleDisplayModeChange(mode.value)"
            >
              {{ mode.label }}
            </button>
          </el-tooltip>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'DisplayModeProperties',
  props: {
    component: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      displayModes: [
        {
          label: '适应',
          value: 'contain',
          tooltip: '保持图片原始宽高比例，让图片完整显示在区域内，不裁剪、不变形'
        },
        {
          label: '拉伸',
          value: 'fill',
          tooltip: '强制拉伸图片至填满区域，会变形'
        },
        {
          label: '裁剪',
          value: 'cover',
          tooltip: '保持图片比例，缩放图片至完全覆盖区域，超出部分被裁剪（只显示中间符合比例的部分）'
        }
      ]
    }
  },
  methods: {
    handleDisplayModeChange(mode) {
      this.$emit('update', { objectFit: mode })
    }
  }
}
</script>

<style lang="scss" scoped>
.display-mode-properties {
  .property-section {
    margin-bottom: 20px;

    .section-title {
      font-size: 14px;
      font-weight: 500;
      margin-bottom: 12px;
      padding: 0 16px;
    }
  }

  .property-group {
    margin-bottom: 16px;
    padding: 0 16px;
  }

  .display-mode-buttons {
    display: flex;
    gap: 4px;
    background-color: #3F4552;
    border-radius: 6px;
    .mode-btn {
      flex: 1;
      height: 30px;
      background: transparent;
      border: none;
      color: #B3BACD;
      border-radius: 6px;
      cursor: pointer;
      font-size: 12px;
      transition: all 0.2s ease;

      &:hover {
        background: #555D70;
        color: #fff;
      }

      &.active {
        background: #555D70;
        color: #fff;
      }
    }
  }
}
</style>
