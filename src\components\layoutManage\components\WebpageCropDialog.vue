<template>
  <el-dialog
    class="webpage-crop-dialog"
    title="网页画面裁剪"
    :visible.sync="dialogVisible"
    width="800px"
    top="60px"
    :modal="false"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @close="handleClose"
  >
    <div
      class="clipping-content-wrapper"
      :style="{width: contentWidth + 'px'}"
      v-loading="screenshotLoading"
      element-loading-background="rgba(0, 0, 0, 0)"
      element-loading-text="获取网页截图中...."
    >
      <div class="clipping-content" v-if="showClipArea" ref="clippingContent">
        <img class="img-cover" ref="clippingImg" :src="screenshotUrl" alt="网页截图">
      </div>
    </div>

    <div class="footer-option">
      <div class="left-btn">
        <el-button type="info" size="small" icon="el-icon-refresh-left" style="margin-right: 20px;" @click="resetClip">
          重置
        </el-button>
        <el-button style="margin-right: 20px;" type="info" size="small" icon="el-icon-refresh" @click="refreshScreenshot">
          重新截图
        </el-button>
        <el-radio-group v-model="radio" @change="switchRadio">
          <el-radio :label="1">
            <span class="label" style="margin-right: 10px;">常用比例</span>
            <el-select style="width: 90px;" v-model="activeProportion" placeholder="请选择" size="mini" @change="switchAspectRatio">
              <el-option v-for="item in proportion" :key="item.value" :label="item.value" :value="item.scale"></el-option>
            </el-select>
          </el-radio>
          <el-radio :label="2">
            <span class="label">自由比例</span>
          </el-radio>
        </el-radio-group>
      </div>
      <el-button type="primary" icon="el-icon-crop" size="small" @click="saveClip">
        裁剪并应用
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import Cropper from 'cropperjs'
import 'cropperjs/src/css/cropper.scss'

const CROPBOXWIDTH = 640
const CROPBOXHEIGHT = 360

export default {
  name: 'WebpageCropDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    webpageUrl: {
      type: String,
      default: ''
    },
    component: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      dialogVisible: false,
      screenshotLoading: false,
      screenshotUrl: '',
      contentWidth: 640,
      showClipArea: false,
      cropper: null,
      imgWidth: 0,
      imgHeight: 0,
      proportion: [
        {value: '16:9', scale: 16 / 9},
        {value: '9:16', scale: 9 / 16},
        {value: '1:1', scale: 1},
        {value: '4:3', scale: 4 / 3},
        {value: '4:9', scale: 4 / 9}
      ],
      activeProportion: 16 / 9,
      currentProportion: 0,
      radio: 2,
      cropperFace: {
        width: 0,
        height: 0,
        dx: 0,
        dy: 0
      }
    }
  },
  watch: {
    visible(val) {
      this.dialogVisible = val
      if (val) {
        this.initDialog()
      }
    }
  },
  methods: {
    async initDialog() {
      if (!this.webpageUrl) {
        this.$message.error('请先输入网页地址')
        this.handleClose()
        return
      }

      await this.getWebpageScreenshot()
    },

    async getWebpageScreenshot() {
      this.screenshotLoading = true
      this.showClipArea = false

      try {
        // 调用获取网页截图的API
        const response = await this.fetchWebpageScreenshot(this.webpageUrl)

        if (response && response.screenshotUrl) {
          this.screenshotUrl = response.screenshotUrl
          this.imgWidth = response.width || 1920
          this.imgHeight = response.height || 1080

          // 等待图片加载完成
          await this.waitForImageLoad()

          this.showClipArea = true
          this.$nextTick(() => {
            this.initCropper()
          })
        } else {
          throw new Error('获取截图失败')
        }
      } catch (error) {
        console.error('获取网页截图失败:', error)
        this.$message.error('获取网页截图失败，请检查网页地址是否正确')
        // this.handleClose()
      } finally {
        this.screenshotLoading = false
      }
    },

        async fetchWebpageScreenshot(url) {
      // 实际的API调用示例
      return new Promise((resolve, reject) => {
        // 如果有真实的API，可以这样调用：
        // import cloudApi from 'api/cloud'
        //
        // const param = {
        //   url: url,
        //   width: 1920,
        //   height: 1080,
        //   timeout: 30000 // 30秒超时
        // }
        //
        // cloudApi.getWebpageScreenshot(param, (response) => {
        //   if (response && response.success) {
        //     resolve({
        //       screenshotUrl: response.data.screenshotUrl,
        //       width: response.data.width || 1920,
        //       height: response.data.height || 1080
        //     })
        //   } else {
        //     reject(new Error(response.message || '获取网页截图失败'))
        //   }
        // }, (error) => {
        //   reject(new Error('网络请求失败: ' + error.message))
        // })

        // 临时模拟API调用（开发阶段使用）
        setTimeout(() => {
          // 检查URL是否有效
          if (!url || !url.startsWith('http')) {
            reject(new Error('请输入有效的网页地址（必须以http://或https://开头）'))
            return
          }

          // 模拟成功响应
          resolve({
            screenshotUrl: `data:image/svg+xml;base64,${btoa(`
              <svg xmlns="http://www.w3.org/2000/svg" width="1920" height="1080">
                <defs>
                  <pattern id="grid" width="100" height="100" patternUnits="userSpaceOnUse">
                    <path d="M 100 0 L 0 0 0 100" fill="none" stroke="#e0e0e0" stroke-width="1"/>
                  </pattern>
                </defs>
                <rect width="100%" height="100%" fill="white"/>
                <rect width="100%" height="100%" fill="url(#grid)" opacity="0.3"/>
                <rect x="50" y="50" width="1820" height="980" fill="#f8f9fa" stroke="#dee2e6" stroke-width="2"/>
                <rect x="50" y="50" width="1820" height="120" fill="#007bff"/>
                <text x="960" y="120" text-anchor="middle" dy=".3em" font-size="32" fill="white" font-family="Arial, sans-serif">
                  网页截图 - ${url.length > 50 ? url.substring(0, 50) + '...' : url}
                </text>
                <circle cx="200" cy="300" r="50" fill="#28a745"/>
                <rect x="300" y="250" width="200" height="100" fill="#ffc107"/>
                <text x="960" y="500" text-anchor="middle" dy=".3em" font-size="24" fill="#6c757d">
                  这是一个模拟的网页截图预览
                </text>
                <text x="960" y="550" text-anchor="middle" dy=".3em" font-size="18" fill="#6c757d">
                  实际使用时将显示真实的网页内容
                </text>
                <text x="960" y="800" text-anchor="middle" dy=".3em" font-size="16" fill="#adb5bd">
                  尺寸: 1920 × 1080 像素
                </text>
              </svg>
            `)}`,
            width: 1920,
            height: 1080
          })
        }, 2000)
      })
    },

    waitForImageLoad() {
      return new Promise((resolve) => {
        const img = new Image()
        img.onload = () => resolve()
        img.onerror = () => resolve() // 即使加载失败也继续
        img.src = this.screenshotUrl
      })
    },

    initCropper() {
      const img = this.$refs.clippingImg
      if (!img) return

      this.cropper = new Cropper(img, {
        viewMode: 1,
        dragMode: 'move',
        movable: false,
        guides: true,
        scalable: false,
        zoomable: false,
        crop: (event) => {
          this.cropperFace.width = Number((event.detail.width / this.imgWidth).toFixed(4))
          this.cropperFace.height = Number((event.detail.height / this.imgHeight).toFixed(4))
          this.cropperFace.dx = Number((event.detail.x / this.imgWidth).toFixed(4))
          this.cropperFace.dy = Number((event.detail.y / this.imgHeight).toFixed(4))
        }
      })

      // 设置初始裁剪区域
      setTimeout(() => {
        this.setInitialCropArea()
      }, 100)
    },

    setInitialCropArea() {
      const scale = Math.min(CROPBOXWIDTH / this.imgWidth, CROPBOXHEIGHT / this.imgHeight)
      let data

      // 如果组件已有裁剪信息，使用现有的
      if (this.component.crop && this.component.crop.width > 0) {
        data = {
          left: parseInt(this.component.crop.dx * this.imgWidth * scale),
          top: parseInt(this.component.crop.dy * this.imgHeight * scale),
          width: parseInt(this.component.crop.width * this.imgWidth * scale),
          height: parseInt(this.component.crop.height * this.imgHeight * scale)
        }
      } else {
        // 默认全屏裁剪
        data = {
          left: 0,
          top: 0,
          width: this.imgWidth * scale,
          height: this.imgHeight * scale
        }
      }

      this.cropper.setCropBoxData(data)
    },

    resetClip() {
      if (!this.cropper) return

      const scale = Math.min(CROPBOXWIDTH / this.imgWidth, CROPBOXHEIGHT / this.imgHeight)
      const data = {
        left: 0,
        top: 0,
        width: this.imgWidth * scale,
        height: this.imgHeight * scale
      }
      this.cropper.setCropBoxData(data)
    },

    switchRadio(value) {
      if (!this.cropper) return

      if (value === 1) {
        this.currentProportion = this.activeProportion
      } else {
        this.currentProportion = NaN
      }
      this.cropper.setAspectRatio(this.currentProportion)
    },

    switchAspectRatio(value) {
      if (this.radio === 2 || !this.cropper) {
        return
      }
      this.activeProportion = value
      this.cropper.setAspectRatio(value)
    },

    async refreshScreenshot() {
      if (this.cropper) {
        this.cropper.destroy()
        this.cropper = null
      }
      await this.getWebpageScreenshot()
    },

    saveClip() {
      if (!this.cropper) {
        this.$message.error('裁剪区域未初始化')
        return
      }

      const cropData = {
        dx: this.cropperFace.dx,
        dy: this.cropperFace.dy,
        width: this.cropperFace.width,
        height: this.cropperFace.height
      }

      // 发送裁剪数据给父组件
      this.$emit('crop-applied', {
        crop: cropData,
        screenshotUrl: this.screenshotUrl,
        originalSize: {
          width: this.imgWidth,
          height: this.imgHeight
        }
      })

      this.$message.success('裁剪设置已应用')
      this.handleClose()
    },

    handleClose() {
      this.dialogVisible = false
      this.$emit('update:visible', false)

      // 清理资源
      if (this.cropper) {
        this.cropper.destroy()
        this.cropper = null
      }

      // 重置状态
      this.screenshotUrl = ''
      this.showClipArea = false
      this.screenshotLoading = false
    }
  },

  beforeDestroy() {
    if (this.cropper && this.cropper.destroy) {
      this.cropper.destroy()
    }
  }
}
</script>

<style scoped lang="scss">
.webpage-crop-dialog {
  .clipping-content-wrapper {
    position: relative;
    height: 360px;
    margin: 0 auto;

    .clipping-content {
      width: 100%;
      height: 100%;
      background-color: #22252D;
      overflow: hidden;

      .img-cover {
        overflow: hidden;
        max-width: 100%;
        max-height: 100%;
      }
    }
  }

  .footer-option {
    display: flex;
    justify-content: space-between;
    margin-top: 30px;

    .label {
      color: #606266;
    }

    .left-btn {
      display: flex;
      align-items: center;
      gap: 20px;
    }
  }
}

:deep(.el-loading-mask) {
  display: flex;
  justify-content: center;
}

:deep(.el-loading-mask) {
  background-color: rgba(0, 0, 0, 0.8);
}

:deep(.el-loading-text) {
  color: white;
}

:deep(.el-radio-group) {
  .el-radio {
    margin-right: 15px;

    .el-radio__label {
      color: #B0BBD6;
    }
  }
}

:deep(.el-select) {
  .el-input__inner {
    background: #4a4a4a;
    border-color: #606060;
    color: #ffffff;
  }
}

:deep(.el-button) {
  &.el-button--info {
    background: #4a4a4a;
    border-color: #606060;
    color: #ffffff;

    &:hover {
      background: #5a5a5a;
      border-color: #ff6b35;
    }
  }

  &.el-button--primary {
    background: #ff6b35;
    border-color: #ff6b35;

    &:hover {
      background: #ff7a47;
      border-color: #ff7a47;
    }
  }
}
</style>
