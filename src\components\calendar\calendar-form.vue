<template>
  <div class="calendar-form" ref="tableWrapper" v-loading="loading">
    <span class="top-icon" title="回到顶部" @click.stop="toTop" v-show="showToTop"></span>
    <div class="scroll-box" ref="scrollBox">
      <div class="form-top">
        <div class="top-left">
          <el-popover
            popper-class="time-slider dark-slider"
            placement="bottom"
            width="56"
            trigger="click">
            <div class="time-slider-box">
              <i class="add" @click="addSlider"></i>
              <el-slider
                v-model="sliderValue"
                vertical
                :min="0"
                :max="5"
                :step="1"
                @change="heightChange"
                height="140px">
              </el-slider>
              <i class="reduce" @click="reduceSlider"></i>
            </div>
            <span class="time-btn" slot="reference">
              <i class="el-icon-zoom-in" style="margin: 0 5px"></i>
              <span>时间轴调节</span>
            </span>
          </el-popover>
        </div>
        <div class="right-li" :style="{width: formWidth + 'px'}" v-for="(item, index) in dateList" :key="item.date">
          <p class="date1">{{getWeekText(item.week)}}</p>
          <p class="date2">{{getDay(item.date)}}</p>
          <el-popover
            :placement="index === 6 ? 'bottom-end' : 'bottom'"
            width="300"
            trigger="click"
            class="view-fragment"
            :ref="'listPop' + item.date"
            :popper-options="{boundariesElement: 'body'}"
            popper-class="view-pop">
            <el-table :data="item.programMap" height="450" @row-click="onJumpProgram">
              <el-table-column label="当日节目列表" header-align="center" align="left" style="cursor: pointer">
                <template slot-scope="scope">
                  <div class="pop-li">
                    <span>{{scope.row.time}}</span>
                    <span>{{scope.row.name}}</span>
                  </div>
                </template>
              </el-table-column>
            </el-table>
            <i class="el-icon-date list-icon" slot="reference" v-show="item.programMap && item.programMap.length"></i>
          </el-popover>
        </div>
      </div>
      <div class="form-area">
        <div class="left-content">
          <div class="time-li" :style="{height: itemHeight + 'px'}" v-for="item in 24" :key="item">
            <span class="time-text">{{item > 9 ? item + ':00' : '0' + item + ':00'}}</span>
          </div>
        </div>
        <div class="right-column" v-for="v in dateList" :key="v.date" :style="{width: formWidth + 'px'}" @click="addProgramHandle($event, v)">
          <div class="empty-li" :data-id="'1_' + item.top + '_' + v.date" :title="'点击新增节目'" v-for="item in v.programList" :style="{top: item.top + 'px', height: item.height + 'px'}"  v-show="canEdit && !item.state">
            <i class="el-icon-plus"></i>
            <span>新增节目</span>
          </div>
          <div v-for="item in v.programList" v-show="item.state">
            <el-popover
              placement="left"
              width="330"
              :ref="item.editId + item.date"
              trigger="click">
              <div class="view-pop">
                <p class="title">{{item.name}}</p>
                <p class="info">
                  <span class="label">播出时间:</span>
                  <span class="text">{{item.seriesDay}}</span>
                  <span class="text">{{item.seriesTime}}</span>
                </p>
                <p class="info">
                  <span class="label">节目时长:</span>
                  <span class="text" v-if="item.seriesDuration">{{getDurationText(item.seriesDuration)}}</span>
                </p>
                <p class="info">
                  <span class="label">节目类型:</span>
                  <span class="text" v-show="item.type === 1">日播节目</span>
                  <span class="text" v-show="item.type === 2">周播节目</span>
                  <span class="text" v-show="item.type === 3">指定时间节目</span>
                </p>
                <p class="info">
                  <span class="label">节目源:</span>
                  <span class="text">{{item.sourceUrl}}</span>
                </p>
                <p class="info">
                  <span class="label">背景音乐:</span>
                  <span class="text">{{!item.backgroundAudi ? '开启' : '关闭'}}</span>
                </p>
                <span class="g-option-btn-1" style="width: 100%" @click="toEdit(item)" v-show="canEdit">编  辑</span>
              </div>
              <div slot="reference" class="program-li" :style="{top: item.top + 'px', height: item.height + 'px'}">
                <span class="info-name" :id="item.seriesDay + ' ' + item.seriesTime" :style="getLineClamp(item.seriesDay + ' ' + item.seriesTime)">{{item.name}}</span>
                <span class="tag" v-show="item.type === 1" style="backgroundColor: #FF7E01">日播</span>
                <span class="tag" v-show="item.type === 2" style="backgroundColor: #32B818">周播</span>
              </div>
            </el-popover>
          </div>
          <div class="right-line" :style="{height: itemHeight + 'px'}" v-for="item in 24" :key="'3_' + item"></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import {mapGetters} from 'vuex'
import cloudApi from 'api/cloud'
import moment from 'moment'
const lineHeight = 140
export default {
  props: {
    canEdit: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
      showToTop: false,
      currentItem: null,
      startTime: null,
      loading: true,
      sliderValue: 2,
      formWidth: 100,
      itemHeight: lineHeight * 2,
      dateList: [],
      weekObj: {
        'Monday': '周一',
        'Tuesday': '周二',
        'Wednesday': '周三',
        'Thursday': '周四',
        'Friday': '周五',
        'Saturday': '周六',
        'Sunday': '周日'
      }
    }
  },
  computed: {
    ...mapGetters([
      'localTimeDifference'
    ])
  },

  methods: {
    toTop () {
      this.$refs.tableWrapper.scrollTo({top: 0, behavior: 'smooth'})
    },
    onJumpProgram (item) {
      this.$refs['listPop' + item.date][0].doClose()
      let dom = this.$refs.tableWrapper
      if (dom) {
        dom.scrollTop = item.top
      }
      this.$refs[item.editId + item.date][0].doShow()
    },
    getLineClamp (id) {
      let getDom = document.getElementById(id)
      if (getDom) {
        let maxLine = Math.floor((getDom.parentNode.offsetHeight - 12) / 19) > 1 ? Math.floor((getDom.parentNode.offsetHeight - 12) / 19) : 1
        return {'-webkit-line-clamp': maxLine}
      } else {
        return {}
      }
    },
    toEdit (item) {
      if (item.editId > 1000000000000000) {
        this.$message.warning('请到今日节目单编辑该节目')
        return
      }
      this.$emit('close')
      this.$bus.$emit('programFromCalendar', item)
    },
    reduceSlider () {
      if (this.sliderValue === 0) {
        return
      }
      --this.sliderValue
      this.heightChange(this.sliderValue)
    },
    addSlider () {
      if (this.sliderValue === 5) {
        return
      }
      ++this.sliderValue
      this.heightChange(this.sliderValue)
    },
    heightChange (val) {
      if (val) {
        this.itemHeight = lineHeight * val
      } else {
        this.itemHeight = lineHeight / 2
      }
      this.getData(this.startTime)
    },
    getDay (date) {
      return date.split('-')[1] + '-' + date.split('-')[2]
    },
    getWeekText (week) {
      return this.weekObj[week] || ''
    },
    addProgramHandle (e, v) {
      let currentDom = e.target
      if (currentDom.parentNode.title === '点击新增节目') {
        currentDom = currentDom.parentNode
      }
      if (currentDom.title === '点击新增节目') {
        let id = currentDom.dataset.id
        let addItem = v.programList.find(item => item.top == id.split('_')[1])
        this.$emit('close')
        this.$bus.$emit('programFromCalendar', addItem)
      }
    },
    getDurationText (duration) {
      let hour
      let minute
      let second
      let durationNum = duration
      if (duration > 3600) {
        hour = Math.floor(duration / 3600)
        durationNum = durationNum - hour * 3600
      } else {
        hour = 0
      }
      if (durationNum >= 60) {
        minute = Math.floor(durationNum / 60)
        durationNum = durationNum - minute * 60
      } else {
        minute = 0
      }
      if (durationNum > 0) {
        second = durationNum
      } else {
        second = 0
      }
      return hour + '时' + minute + '分' + second + '秒'
    },
    getTime (duration) {
      let durationNum = Number(duration)
      if (!durationNum || durationNum <= 0) {
        return '00:00:00'
      }
      durationNum = Math.floor(durationNum / 1000)
      let hour
      let minute
      let second
      if (durationNum >= 3600) {
        hour = Math.floor(durationNum / 3600)
        durationNum = durationNum - hour * 3600
        if (hour >= 10) {
          hour = hour + ''
        } else {
          hour = '0' + hour
        }
      } else {
        hour = '00'
      }
      if (durationNum >= 60) {
        minute = Math.floor(durationNum / 60)
        durationNum = durationNum - minute * 60
        if (minute >= 10) {
          minute = minute + ''
        } else {
          minute = '0' + minute
        }
      } else {
        minute = '00'
      }
      if (durationNum > 0) {
        second = durationNum
        if (second >= 10) {
          second = second + ''
        } else {
          second = '0' + second
        }
      } else {
        second = '00'
      }
      return hour + ':' + minute + ':' + second
    },
    getTimeValue (timeText) {
      if (!timeText.split(':')[0] || !timeText.split(':')[1] || !timeText.split(':')[2]) {
        return 0
      }
      return Number(timeText.split(':')[0] * 3600) + Number(timeText.split(':')[1] * 60) + Number(timeText.split(':')[2])
    },
    setAddArea (index) {
      if (!this.dateList[index].programList || !this.dateList[index].programList.length) {
        this.dateList[index].programList = [
          {top: 0, height: 24 * this.itemHeight, state: 0, date: this.dateList[index].date, time: '00:00:00', duration: 24 * 60 * 60, id: this.$_createRandomId()}
        ]
      }
      let programList = this.$_deepCopy(this.dateList[index].programList)
      for (let i = programList.length - 1; i >= 0; i--) {
        let top = programList[i].top + programList[i].height
        let time = this.getTimeValue(programList[i].time) + programList[i].duration
        if (i === programList.length - 1 && time < 24 * 60 * 60) {
          this.dateList[index].programList.push({top: top, height: 24 * this.itemHeight - top, state: 0, date: this.dateList[index].date, time: this.getTime(time * 1000), duration: 24 * 60 * 60 - time, id: this.$_createRandomId()})
        } else if (programList[i + 1] && time < this.getTimeValue(programList[i + 1].time)) {
          this.dateList[index].programList.splice(i, 0, {top: top, height: programList[i + 1].top - top, state: 0, date: this.dateList[index].date, time: this.getTime(time * 1000), duration: this.getTimeValue(programList[i + 1].time) - time, id: this.$_createRandomId()})
        }
        if (i === 0 && this.getTimeValue(programList[i].time) > 0) {
          this.dateList[index].programList.unshift({top: 0, height: programList[i].top, state: 0, date: this.dateList[index].date, time: '00:00:00', duration: this.getTimeValue(programList[i].time), id: this.$_createRandomId()})
        }
      }
      this.dateList[index].programMap = this.dateList[index].programList.filter(v => v.state)
    },
    setProgramList (res) {
      this.dateList = []
      res.forEach((dateItem, index) => {
        let dataObj = {}
        dataObj.programList = []
        if (index) {
          dataObj.week = moment(this.startTime).add(index, 'days').format('dddd') // 星期一
          dataObj.date = moment(this.startTime).add(index, 'days').format('YYYY-MM-DD')
        } else {
          dataObj.date = moment(this.startTime).format('YYYY-MM-DD')
          dataObj.week = moment(this.startTime).format('dddd')
        }
        if (dateItem && dateItem.length) {
          dataObj.programList = dateItem.map(item => {
            let programObj = {
              id: this.$_createRandomId(),
              date: dataObj.date,
              name: item.name,
              type: item.type,
              time: item.time,
              duration: item.duration,
              sourceUrl: item.sourceUrl,
              backgroundAudio: item.backgroundAudio,
              state: 1,
              seriesDay: item.seriesDay,
              seriesTime: item.seriesTime,
              seriesDuration: item.seriesDuration,
              editId: item.id
            }
            programObj.top = parseInt(this.getTimeValue(item.time) / (60 * 60) * this.itemHeight)
            programObj.height = parseInt(item.duration / (60 * 60) * (this.itemHeight))
            return programObj
          })
        }
        this.dateList.push(dataObj)
      })
      this.dateList.forEach((item, index) => {
        this.setAddArea(index)
      })
    },
    getData (time) {
      this.startTime = time
      let param = {
        startTime: moment(time).format('YYYY-MM-DD')
      }
      cloudApi.getCalendarData(param, res => {
        if (res && res.length) {
          this.setProgramList(res)
        }
        setTimeout(() => {
          this.loading = false
          this.$refs.scrollBox.style.opacity = 1
        }, 500)
      })
    },
    scrollChange (e) {
      if (e.target.scrollTop >= 300) {
        this.showToTop = true
      } else {
        this.showToTop = false
      }
    },
    resizeChange () {
      if (this.$refs.tableWrapper) {
        this.formWidth = Math.max(Math.floor((this.$refs.tableWrapper.offsetWidth - 100) / 7), 160)
      }
    }
  },

  mounted () {
    this.loading = true
    this.$bus.$on('windowResize', this.resizeChange)
    this.$refs.tableWrapper.addEventListener('scroll', this.scrollChange)
    this.$nextTick(() => {
      this.formWidth = Math.max(Math.floor((this.$refs.tableWrapper.offsetWidth - 100) / 7), 160)
    })
  },
  beforeDestroy () {
    this.$refs.tableWrapper.removeEventListener('scroll', this.scrollChange)
  }
}
</script>

<style scoped lang="scss">
  .calendar-form {
    position: relative;
    overflow: auto;
    font-family: Microsoft YaHei;
  }
  .scroll-box {
    position: relative;
    min-width: 1220px;
    opacity: 0;
    overflow: hidden;
    padding-top: 100px;
    box-sizing: border-box;
  }
  .form-top {
    position: fixed;
    top: 117px;
    display: flex;
    z-index: 9999;

    .top-left {
      display: flex;
      align-items: center;
      height: 100px;
      width: 100px;
      border-right: 1px solid #040507;
      border-bottom: 1px solid #040507;
      box-sizing: border-box;
      background-color: #292E37;
      .time-btn {
        cursor: pointer;
        &:hover {
          color: #FC4F08;
        }
      }
    }
    .right-li {
      position: relative;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      height: 100px;
      line-height: 26px;
      border-right: 1px solid #040507;
      border-bottom: 1px solid #040507;
      box-sizing: border-box;
      background-color: #292E37;
      .list-icon {
        position: absolute;
        right: 10px;
        bottom: 10px;
        font-size: 16px;
      }
      .date1 {
        font-size: 20px;
        color: #B2BFDE;
      }
      .date2 {
        font-size: 22px;
        color: #85A0E3;
      }
      .date3 {
        font-size: 14px;
        color: #636D85;
      }
    }
  }
  .form-area {
    display: flex;
    justify-content: flex-start;
    .left-content {
      width: 100px;
      border-right: 1px solid #040507;
      border-bottom: 1px solid #040507;
      box-sizing: border-box;
      background-color: #292E37;
      .time-li {
        position: relative;
        width: 100%;
        color: #B2BFDE;
        font-size: 20px;
        .time-text {
          position: absolute;
          bottom: -13px;
          left: 50%;
          transform: translateX(-50%);
        }
        &:last-child .time-text {
          bottom: 0;
        }
      }
    }
    .right-column {
      position: relative;
      .right-line {
        box-sizing: border-box;
        background-color: #1F232A;
        border-right: 1px solid #040507;
        border-bottom: 1px solid #040507;
      }
      .empty-li {
        position: absolute;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100%;
        background: transparent;
        border-radius: 4px;
        box-sizing: border-box;
        font-size: 14px;
        color: #B2BFDE;
        user-select: none;
        cursor: pointer;
        font-size: 14px;
        color: #FFFFFF;
        opacity: 0;
        &:hover {
          opacity: 1;
          background-color: #4869B6;
          border-right: 1px solid #040507;
          border-bottom: 1px solid #040507;
        }
      }
      .program-li {
        position: absolute;
        display: flex;
        align-items: center;
        padding-left: 18px;
        padding-right: 18px;
        width: 100%;
        background: #35415D;
        border-radius: 4px;
        border-right: 1px solid #040507;
        border-bottom: 1px solid #040507;
        border-top: 1px solid #040507;
        box-sizing: border-box;
        font-size: 14px;
        color: #B2BFDE;
        user-select: none;
        cursor: pointer;
        overflow: hidden;
        &:hover {
          background-color: #445274;
        }
        .info-name {
          display: -webkit-box;
          -webkit-box-orient: vertical;
          overflow: hidden;
        }
      }
      .tag {
        position: absolute;
        top: 0;
        left: 0;
        padding: 2px 4px;
        border-radius: 4px 0px 4px 0px;
        color: #FFFFFF;
        font-size: 12px;
        font-family: SimSun;
      }
    }
  }
  .view-pop {
    padding: 37px 24px 20px 24px;
    .title {
      margin-bottom: 27px;
      font-size: 18px;
      font-weight: bold;
      color: #222222;
    }
    .label {
      white-space: nowrap;
      font-size: 14px;
      color: #979797;
    }
    .text {
      margin-left: 14px;
      font-size: 14px;
      color: #222222;
    }
    .info {
      display: flex;
      justify-content: flex-start;
      margin-bottom: 17px;
    }
  }
  .time-slider-box {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 56px;
    .reduce {
      display: inline-block;
      width: 18px;
      height: 18px;
      background: url('./reduce.png') no-repeat;
      background-size: 18px;
      margin-top: 4px;
    }
    .add {
      display: inline-block;
      width: 18px;
      height: 18px;
      background: url('./add.png') no-repeat;
      background-size: 18px;
      margin-bottom: 4px;
    }
  }
  .pop-li {
    display: flex;
    justify-content: space-between;
    & span:last-child {
      width: 150px;
    }
  }
  .top-icon {
    position: fixed;
    right: 30px;
    bottom: 100px;
    z-index: 9999;
    background: url('./back-top.png') no-repeat;
    width: 64px;
    height: 64px;
    background-size: 64px;
    cursor: pointer;
    &:hover {
      background-image: url('./back-top-hover.png');
    }
  }
  ::v-deep .el-loading-mask {
    background-color: #1F232A;
  }
</style>
