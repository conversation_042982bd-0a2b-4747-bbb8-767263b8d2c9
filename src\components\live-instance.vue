<template>
  <div class="live-instance wh100" ref="studioInstance" v-if="info">
    <header-area :info="info" class="header-area"></header-area>
    <main class="main-area">
      <live-tip v-if="showLiveTip" @close="showLiveTip = false"></live-tip>
      <calendar v-if="showCalendar" :canEdit="canEdit" @close="showCalendar = false"></calendar>
      <left-area class="left-area" :currentProgramId="currentProgramId" :nextProgramId="nextProgramId"></left-area>
      <div class="right-area">
        <live-video class="live-video" :info="info" :currentProgramId="currentProgramId" :nextProgramId="nextProgramId"></live-video>
        <bottom-tabs class="bottom-tabs" :info="info"></bottom-tabs>
      </div>
    </main>
  </div>
</template>

<script>
  import headerArea from './headerArea/header-area'
  import liveVideo from './liveVideo/live-video'
  import ChannelData from 'common/js/channel-data'
  import webrtcMgr from 'common/js/webrtc-manager'
  import bottomTabs from './bottomTabs/bottom-tabs'
  import leftArea from './leftArea/left-area'
  import {mapMutations, mapGetters, mapActions} from 'vuex'
  import calendar from './calendar'
  import liveTip from '@/components/liveTip'
import moment from 'moment'

  export default {
    name: 'live-instance',
    components: {headerArea, liveVideo, bottomTabs, leftArea, calendar, liveTip},
    computed: {
      ...mapGetters([
        'localTimeDifference',
        'instanceId',
        'programsSn'
      ])
    },
    data () {
      return {
        firstFlag: false,
        showLiveTip: false,
        showCalendar: false,
        currentProgramId: '',
        info: null,
        canEdit: false,
        lastEndOf: 0
      }
    },
    methods: {
      initData () {
        this.lastEndOf = moment().endOf('day').valueOf()
        window.channelData = new ChannelData(this.instanceId).bind((res) => {
          if (window.WebRTCLogOpen) {
            console.log(JSON.parse(res))
          }
          this.info = JSON.parse(res)
          this.checkUpdate()
          if (this.info.isLiving && !this.firstFlag) {
            this.firstFlag = true
            this.showLiveTip = true
          }
          this.setProgramsSn(this.info.programsSn)
          this.setCurrentScheduleId(this.info.currentScheduleId)
          this.setCardNumber(this.info.cardNumber)
          this.currentProgramId = this.info.currentProgramId
          this.nextProgramId = this.info.nextProgramId
          if (this.info.invalid) {
            location.reload(true)
          }
        })
      },
      checkUpdate () {
        if (new Date().getTime() < this.lastEndOf + 2000) {
          return
        }
        if (new Date().getTime() > this.lastEndOf + 2000) {
          this.$bus.$emit('refreshProgramManage')
          this.getPrograms()
          this.lastEndOf = moment().endOf('day').valueOf()
        }
      },
      windowResizeChange () {
        this.$bus.$emit('windowResize')
      },
      ...mapMutations({
        setCurrentScheduleId: 'SET_CURRENT_SCHEDULE_ID',
        setCardNumber: 'SET_CARD_NUMBER',
        setProgramsSn: 'SET_PROGRAMS_SN'
      }),
      ...mapActions(['getPrograms'])
    },
    mounted () {
      this.$bus.$on('showCalendarView', (canEdit) => {
        this.showCalendar = true
        this.canEdit = canEdit
      })
      window.addEventListener('resize', this.windowResizeChange)
    },
    created () {
      this.initData()
      window.onunload = function () {
        webrtcMgr.destroyAll()
        webrtcMgr.destroyAllVideo()
      }
    },
    beforeDestroy () {
      webrtcMgr.destroyAll()
      webrtcMgr.destroyAllVideo()
      window.removeEventListener('resize', this.windowResizeChange)
    }
  }
</script>

<style scoped lang="scss">
  .live-instance {
    background-color: $color-content-bg;
    overflow: hidden;
    .header-area {
      height: 60px;
      border-bottom: 1px solid $color-border2;
      box-sizing: border-box;
    }
    .main-area {
      position: relative;
      width: 100%;
      box-sizing: border-box;
      height: calc(100% - 60px);
      display: flex;
      justify-content: space-between;
      .left-area {
        flex: 0 0 64%;
        overflow: hidden;
        border-right: 1px solid $color-border2;
        box-sizing: border-box;
        background-color: #21252D;
      }
      .right-area {
        flex: 0 0 36%;
        width: 0;
        display: flex;
        flex-direction: column;
        justify-content: flex-start;
        box-sizing: border-box;
        .live-video {
          height: 50%;
          border-bottom: 1px solid $color-border2;
        }
        .bottom-tabs {
          height: 50%;
        }
      }
    }
  }
</style>
