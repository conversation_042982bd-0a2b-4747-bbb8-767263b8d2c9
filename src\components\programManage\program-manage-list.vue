<template>
  <div class="program-list">
    <div class="top-area">
      <el-input placeholder="请输入名称" class="top-search-input" style="width: 340px" v-model.trim="search.name">
        <el-button class="search-button" slot="append" icon="search" @click="getPageInfo(1)">
          <i class="el-icon-search"></i>
          搜索</el-button>
      </el-input>
      <div v-show="roleType !== 2">
        <span class="download-model">
          <a class="el-icon-document" :href="getDownUrl" download="节目管理模板.xlsx" style="color: #B5BBCA">
            下载节目模板
          </a>
          <el-tooltip effect="light" content="节目单最多支持1000条节目" placement="top">
            <span class="el-icon-warning" style="margin-left: 13px"></span>
          </el-tooltip>
        </span>
        <span>
          <label for="importFile">
            <span class="g-option-btn-2" style="width: 110px">
              <input id="importFile" type="file" style="opacity: 0;width: 0" @change="importExcel"/>
              <i class="icon el-icon-document-add"></i>
              <span>导入节目</span>
            </span>
          </label>
        </span>
        <span class="g-option-btn-1" @click="addProgramDialog" style="width: 84px;">
          <i class="icon el-icon-plus"></i>
          <span>新增</span>
        </span>
      </div>
    </div>
    <div class="program-wrapper">
      <div class="list-header" ref="header">
        <span class="program-checked" v-if="roleType !== 2">
          <el-checkbox :indeterminate="isIndeterminate" v-model="checkAll" @change="handleCheckAllChange"></el-checkbox>
        </span>
        <span class="program-name">节目名称</span>
        <span class="program-duration">时长</span>
        <span class="cg-name">CG名称</span>
        <span class="audio">音频文件名</span>
        <span class="program-number">排列序号
          <el-tooltip effect="light" content="节目按序号从小到大排列，仅在节目管理中有效" placement="top">
            <span class="el-icon-warning" style="margin-left: 5px"></span>
          </el-tooltip>
        </span>
      </div>
      <div class="program-ul" id="program-ul">
        <el-checkbox-group v-model="checkedIds" style="font-size: 16px" @change="handleChecked">
        <div class="program-li program-drag"
             :key="item.id" v-for="(item) in pageList.data" @mouseenter="programOptionShow(item.id)" @mouseleave="programLeave(item.id)">
          <span class="program-checked" v-if="roleType !== 2">
            <el-checkbox :label="item.id"><br></el-checkbox>
          </span>
          <div class="program-name">
            <div class="name" :title="item.program.name">{{item.program.name}}</div>
            <div class="local-tag" v-show="hasLocalTag(item.program)">本地</div>
          </div>
          <div class="program-duration">
            <div class="time">{{item.program.duration / 1000 | formatDuration}}</div>
          </div>
          <div class="cg-name">
            <ul class="g-hide-scroll cg-name-ul" v-if="item.program.cgIds && item.program.cgIds.length">
              <li class="cg-name-li" v-for="(v, i) in item.program.cgIds" :title="getCgName(v)">{{i + 1}}、{{getCgName(v)}}</li>
            </ul>
            <span v-else>-</span>
          </div>
          <div class="audio">
            <ul class="g-hide-scroll audio-ul" v-if="item.program.audios && item.program.audios.length">
              <li class="audio-li" v-for="(v, i) in item.program.audios" :class="{lineThrough: v.isDeleted}" :title="v.audioName">{{i + 1}}、{{v.audioName}}</li>
            </ul>
            <span v-else>-</span>
          </div>
          <div class="program-number">{{item.orderNum === 2147483647 ? '' : item.orderNum}}</div>
          <div class="program-option" :class="{'show-option': showOptionId === item.id}">
            <div class="program-option-inner">
              <template v-if="roleType !== 2">
                <el-button type="danger" class="my-operate-btn" style="width: 136px" @click="pushProgramList(item)">加入循环节目单</el-button>
                <el-button type="danger" class="my-operate-btn" @click="showPreview(item)">预览</el-button>
                <el-button type="danger" class="my-operate-btn" @click="relateSubtitleShow(item)">关联字幕</el-button>
                <el-button type="danger" class="my-operate-btn" @click="showRelateAudioDialog(item)">关联音频</el-button>
                <el-dropdown @visible-change="programOptionShow1($event)">
                  <span class="my-operate-btn my-operate-btn-span">更多</span>
                  <el-dropdown-menu slot="dropdown">
                    <el-dropdown-item @click.native="showFramesClipDialog(item)">画面裁剪</el-dropdown-item>
                   <el-dropdown-item @click.native="showPaletteDialog(item)">调色</el-dropdown-item>
                    <el-dropdown-item @click.native="showLayoutSelectDialogForProgram(item)">关联布局</el-dropdown-item>
                    <span class="dropdown-line"></span>
                    <el-dropdown-item @click.native="editProgramsDialog(item)">编辑</el-dropdown-item>
                    <el-dropdown-item @click.native="copyProgram(item)">复制</el-dropdown-item>
                    <el-dropdown-item @click.native="deleteProgram(item)">删除</el-dropdown-item>
                  </el-dropdown-menu>
                </el-dropdown>
              </template>
              <template v-else>
                <el-button type="danger" class="my-operate-btn" @click="showPreview(item)">预览</el-button>
              </template>
            </div>
          </div>
        </div>
        </el-checkbox-group>
        <div class="program-empty wh100" v-if="!pageList.data.length">
          <no-data tips="暂无节目"></no-data>
        </div>
      </div>
    </div>
    <div class="program-bottom">
      <div>
        <template v-if="roleType !== 2">
          <el-checkbox :indeterminate="isIndeterminate" v-model="checkAll" @change="handleCheckAllChange" style="margin-left: 28px"></el-checkbox>
          <el-button type="text" @click="allChange" class="text-btn" style="margin-left: 11px">全选</el-button>
          <!-- <el-button type="text" @click="allPushProgramList" class="text-btn" style="margin-left: 40px">加入循环节目单</el-button> -->
          <el-button type="text" @click="allDelete" class="text-btn" style="margin-left: 25px">删除</el-button>
        </template>
        <span style="margin-left: 30px" class="hide-1440">存储量：</span>
        <span class="hide-1440" style="color: #FF5712">{{$_getSizeFormat(Number(localStorageInfo.currentVolume))}}</span>
        <span class="hide-1440">{{'/' + Number(localStorageInfo.maxVolume) / (1024 * 1024 * 1024)}}GB</span>
      </div>
      <footer-pagination small :page="pageList.page" @getPageInfo="getPageInfo"></footer-pagination>
    </div>
    <el-dialog class="relate-audio-first" title="节目关联音乐" width="700px" top="70px" :visible.sync="programAudioDialog" :close-on-click-modal="false" v-if="programAudioDialog">
      <el-table :data="relateItem.audios" style="width: 100%" stripe height="500" element-loading-spinner="el-icon-loading"
                element-loading-background="rgba(0, 0, 0, 0.8)" border row-key="id" size="small">
        <el-table-column label="音频文件名" prop="audioName"></el-table-column>
        <el-table-column label="类型">
          <template slot-scope="scope">
            <span>{{scope.row.audioType ? '音频流' : '点播'}}</span>
          </template>
        </el-table-column>
        <el-table-column label="音频时长">
          <template slot-scope="scope">
            <span>{{scope.row.duration | formatDurationMs}}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作">
          <template slot-scope="scope">
            <el-button type="info" @click="cancelRelate(scope.row)" size="mini" style="font-size: 12px">取消关联</el-button>
          </template>
        </el-table-column>
        <div class="empty-box wh100" v-if="relateItem.audios && !relateItem.audios.length" slot="empty">
          <no-data tips="暂无音频"></no-data>
        </div>
      </el-table>
      <div  slot="footer" class="dialog-footer">
        <el-button size="small" type="primary" @click="relateAudio" style="font-size: 12px">关联新文件</el-button>
      </div>
      <div class="tips">
        <p>注：</p>
        <p>1、关联多个点播文件时，单个文件播完会自动播放下一个</p>
        <p>2、关联音频直播流时，会一直播放该直播流；需要手动取消关联</p>
      </div>
    </el-dialog>
    <relate-subtitle v-if="relateSubtitleDialog" @close="relateSubtitleDialog=false" :currentProgram="currentProgram" :manageId="manageId" :currentProgramItem="currentProgramItem"></relate-subtitle>
    <relate-audio @close="showRelateAudio=false" v-if="showRelateAudio" :relateItem="relateItem" @callBack="relateCallBack"
                  :selects="relateItem.audios" :manageId="manageId"></relate-audio>
    <frames-clip @close="showFramesClip = false" v-if="showFramesClip" :currentProgramItem="currentProgramItem" :manageId="manageId"></frames-clip>
    <palette @close="showPalette = false" v-if="showPalette" :currentProgramItem="currentProgramItem" :manageId="manageId"></palette>
    <pinp-setting @close="showPinpSetting = false" v-if="showPinpSetting" :currentProgramItem="currentProgramItem" :manageId="manageId"></pinp-setting>
    <preview-dialog v-if="previewDialog" @close="previewDialog = false" :currentProgramItem="currentProgramItem"></preview-dialog>
    <add-program v-if="showProgramDialog" @close="showProgramDialog = false" :currentItem="currentItem" :addType="4" @refresh="addCallback"></add-program>

    <!-- 布局选择弹窗 -->
    <LayoutSelectDialog
      :visible.sync="showLayoutSelectDialog"
      :target-ratio="currentLiveRatioString"
      @layout-selected="handleLayoutSelected"
      @close="handleLayoutSelectDialogClose"
    />
  </div>
</template>

<script>
  import {mapGetters, mapActions, mapMutations} from 'vuex'
  import XLSX from 'xlsx'
  import relateSubtitle from '@/components/cgTemplate/relate-subtitle'
  import relateAudio from '@/components/base/relateAudio/relate-audio'
  import framesClip from '@/components/framesClip/frames-clip'
  import palette from '@/components/palette'
  import pinpSetting from '@/components/pinpSetting/pinp-setting'
  import previewDialog from '../programList/previewDialog'
  import cloudApi from 'api/cloud'
  import footerPagination from '@/components/pagination/footer-pagination'
  import addProgram from '../addProgram'
  import layoutSelectMixin from '@/common/js/layoutSelectMixin'

  export default {
    name: 'program-list',
    components: {relateSubtitle, relateAudio, framesClip, pinpSetting, palette, previewDialog, footerPagination, addProgram},
    mixins: [layoutSelectMixin],
    computed: {
      getDownUrl () {
        if (process.env.NODE_ENV === 'production') return process.env.VUE_APP_BASE_URL + 'manage-template.xlsx'
        return 'manage-template.xlsx'
      },
      ...mapGetters([
        'localStorageInfo',
        'roleType',
        'programs',
        'cgs',
        'programsSn',
        'audio'
      ])
    },
    data () {
      return {
        currentItem: null,
        search: {
          name: ''
        },
        btnTop: 0,
        btnLeft: 0,
        moreBtnArr: [
          {id: 1, text: '画中画'},
          {id: 2, text: '画面裁剪'},
          {id: 3, text: '编辑'},
          {id: 4, text: '复制'},
          {id: 5, text: '删除'}
        ],
        programAudioDialog: false,
        scale: 1,
        showRelateAudio: false,
        pageList: {
          data: [],
          page: this.Page()
        },
        isSlotProgram: false,
        slotIndex: -1,
        showProgramDialog: false,
        relateSubtitleDialog: false,
        adding: false,
        programForm: {
          id: 0,
          name: '',
          startTime: '', // 播出时间，本地时间，格式为"2006-01-02 15:04:05"，仅节目表第一个节目有效
          hour: 0, // 节目小时
          minute: 0, // 节目分钟
          second: 0, // 节目秒
          type: 'stream', // "stream"：直播；"record"：录播
          url: '',
          cgIds: [],
          audios: [],
          orderNum: undefined // 节目管理列表排序
        },
        programRules: {
          name: [
            {required: true, message: '请输入节目名称', trigger: 'blur'}
          ],
          url: [
            {required: true, message: '请输入地址', trigger: 'blur'}
          ]
        },
        importData: [],
        currentProgram: '',
        manageId: 0,
        relateItem: {},
        currentProgramItem: {},
        showFramesClip: false,
        showPalette: false,
        showPinpSetting: false,
        previewDialog: false,
        checkedIds: [],
        checkAll: false,
        isIndeterminate: false,
        allCheckedIds: [],
        manageItem: null,
        showOptionId: ''
      }
    },
    methods: {
      addCallback () {
        this.getFileStorage()
        this.getPageInfo()
      },
      hasLocalTag (program) {
        let masterChannel = program.video.channels.find(v => v.idx === 0)
        if (!masterChannel) {
          return false
        }
        return masterChannel.localFileName && masterChannel.type === 'local'
      },
      programLeave () {
        this.timer = setTimeout(() => {
          if (this.showMore) {
            return
          }
          this.showOptionId = ''
        }, 150)
      },
      programOptionShow (id) {
        clearTimeout(this.timer)
        this.showOptionId = id
      },
      programOptionShow1 (flag) {
        this.showMore = flag
      },
      handleChecked (value) {
        let checkedCount = value.length
        this.checkAll = checkedCount === this.allCheckedIds.length
        this.isIndeterminate = checkedCount > 0 && checkedCount < this.allCheckedIds.length
      },
      allChange () {
        this.checkedIds = this.allCheckedIds
        this.isIndeterminate = false
        this.checkAll = true
      },
      handleCheckAllChange (val) {
        this.checkedIds = val ? this.allCheckedIds : []
        this.isIndeterminate = false
      },
      getPageInfo (page) {
        let param = {}
        param.page = page || this.pageList.page.pageIndex
        if (this.search.name) {
          param.name = this.search.name
        }
        this.pageList.loading = true
        cloudApi.getProgramManageList(param, res => {
          if (res && res.data) {
            this.allCheckedIds = []
            this.pageList.data = res.data.filter(item => item.program)
            this.pageList.data.forEach(v => {
              this.allCheckedIds.push(v.id)
              v.program = JSON.parse(v.program)
            })
            this.GetPage(this.pageList, res)
          }
        }, (code, msg) => {
          this.$message.error(msg)
        })
      },
      allPushProgramList () {
        if (this.checkedIds.length === 0) {
          return this.$message.warning('请先选择节目')
        }
        this.$confirm('确定添加选中节目进入循环节目单吗?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          let programsParam = []
          this.pageList.data.forEach(item => {
            if (this.checkedIds.includes(item.id)) {
              let newProgram = this.$_deepCopy(item.program)
              newProgram.id = this.$_createRandomId()
              newProgram.duration = newProgram.duration / 1000
              programsParam.push(newProgram)
            }
          })
          let param = {
            programType: 'loop',
            programs: programsParam
          }
          if (this.adding) {
            return this.$message.warning('正在添加中')
          }
          this.adding = true
          this.addProgram(param).then(() => {
            this.$message.success('添加成功')
            this.adding = false
            this.clearAllSelect()
          }, (code, msg) => {
            this.adding = false
          })
        }).catch(() => {
        })
      },
      pushProgramList (item) {
        this.$confirm('确定添加节目进入循环节目单吗?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          let newProgram = this.$_deepCopy(item.program)
          newProgram.id = this.$_createRandomId()
          newProgram.duration = newProgram.duration / 1000 // 节目管理添加到节目单时 duration / 1000 后单位为秒
          let param = {
            programType: 'loop',
            programs: [newProgram]
          }
          if (this.adding) {
            return this.$message.warning('正在添加中')
          }
          this.adding = true
          this.addProgram(param).then(() => {
            this.$message.success('添加成功')
            this.adding = false
          }, (code, msg) => {
            this.adding = false
          })
        }).catch(() => {
        })
      },
      showPreview (item) {
        this.currentProgramItem = item.program
        this.previewDialog = true
      },
      showPinpDialog (item) {
        this.manageId = item.id
        this.currentProgramItem = item.program
        this.showPinpSetting = true
      },
      showFramesClipDialog (item) {
        this.manageId = item.id
        this.currentProgramItem = item.program
        this.showFramesClip = true
      },
      showPaletteDialog (item) {
        this.manageId = item.id
        this.currentProgramItem = item.program
        this.showPalette = true
      },
      getCgName (id) {
        let index = this.cgs.findIndex(item => {
          return item.id === id
        })
        if (index >= 0) {
          return this.cgs[index].name
        } else {
          return '该CG已删除'
        }
      },
      relateCallBack (editProgram) {
        this.relateItem = editProgram
        this.getPageInfo()
      },
      async cancelRelate (item) {
        let editProgram = this.relateItem
        editProgram.audios.forEach((v, i) => {
          if (v.audioId === item.audioId) {
            editProgram.audios.splice(i, 1)
          }
        })
        await this.editApiConfirm(editProgram).then(() => {
          this.getPageInfo()
          this.relateItem = editProgram
          this.$message.success('取消关联成功')
        })
      },
      showRelateAudioDialog (item) {
        this.manageId = item.id
        this.programAudioDialog = true
        this.relateItem = item.program
      },
      relateAudio () {
        this.showRelateAudio = true
      },
      relateSubtitleShow (item) {
        this.manageId = item.id
        this.currentProgram = item.program.id
        this.currentProgramItem = item.program
        this.relateSubtitleDialog = true
      },
      importExcel (file) {
        // 获取上传的文件对象
        const { files } = file.target
        // 通过FileReader对象读取文件
        const fileReader = new FileReader()
        if (files[0].name.indexOf('.xlsx') > 0 || files[0].name.indexOf('.xls') > 0) {
          fileReader.onload = event => {
            const { result } = event.target
            // 以二进制流方式读取得到整份excel表格对象
            const workbook = XLSX.read(result, { type: 'binary' })
            let data = [] // 存储获取到的数据
            // 遍历每张工作表进行读取（这里默认只读取第一张表）
            for (const sheet in workbook.Sheets) {
              if (workbook.Sheets.hasOwnProperty(sheet)) {
                // 利用 sheet_to_json 方法将 excel 转成 json 数据
                data = data.concat(XLSX.utils.sheet_to_json(workbook.Sheets[sheet]))
                break // 如果只取第一张表，就取消注释这行
              }
            }
            this.importData = this.$_deepCopy(data)
            this.addMorePrograms(this.importData)
          }
        } else {
          // 抛出文件类型错误不正确的相关提示
          this.$message.error('导入文件类型不正确')
          return
        }

        // 以二进制方式打开文件
        fileReader.readAsBinaryString(files[0])
        event.target.value = ''
      },
      splicePrograms (data) {
        let content = this
        let splicePrograms = []
        let topName = ['排列序号', '节目名称', '媒体机构', '节目时长/秒', '直播方式', '流地址', '备注']
        for (let i = 0; i < data.length; i++) {
          for (const dataKey in data[i]) {
            if (topName.indexOf(dataKey) < 0) {
              return content.$message.error('请使用模板文件规范填写资料')
            }
          }
          splicePrograms.push({
            id: '',
            name: '',
            startTime: '',
            duration: 0,
            type: 'stream',
            url: '',
            orderNum: ''
          })
          splicePrograms[i].id = content.$_createRandomId()
          splicePrograms[i].name = data[i]['节目名称'] ? data[i]['节目名称'] + '' : ''
          splicePrograms[i].duration = data[i]['节目时长/秒'] * 1000 // 就节目管理时间 * 1000
          splicePrograms[i].type = data[i]['直播方式'] === '拉流' ? 'stream' : data[i]['直播方式'] === '点播' ? 'record' : 'pic'
          splicePrograms[i].url = data[i]['流地址']
          splicePrograms[i].orderNum = data[i]['排列序号']
        }
        return splicePrograms
      },
      addMorePrograms (data) {
        let programsParam = []
        let newProgram = this.splicePrograms(data)
        if (!this.checkForm(newProgram)) {
          return
        }
        programsParam = []
        newProgram.forEach(item => {
          let itemJson = JSON.stringify({
            id: item.id,
            name: item.name,
            startTime: item.startTime,
            duration: item.duration,
            cgIds: [],
            audios: [],
            video: {
              useBackground: false,
              channels: [
                {
                  type: item.type,
                  url: item.url,
                  idx: 0,
                  crop: null,
                  display: null
                }
              ]
            }
          })
          let obj = {
            program: itemJson
          }
          if (item.orderNum) {
            obj.orderNum = item.orderNum
          }
          programsParam.push(obj)
        })
        this.addApiConfirm(programsParam)
      },
      checkForm (data) {
        let content = this
        let flag = true
        for (let i = 0; i < data.length; i++) {
          if (!data[i].name) {
            content.$message.warning(`第${i + 2}行中的节目名称不能为空`)
            flag = false
            break
          } else if (!data[i].duration) {
            flag = false
            content.$message.warning(`第${i + 2}行中的节目时长不能为空`)
            break
          } else if (!data[i].type) {
            flag = false
            content.$message.warning(`第${i + 2}行中的直播方式不能为空`)
            break
          } else if (!data[i].url) {
            flag = false
            content.$message.warning(`第${i + 2}行中的流地址不能为空`)
            break
          } else if (data[i].orderNum && typeof data[i].orderNum !== 'number') {
            flag = false
            content.$message.warning(`第${i + 2}行中的排列序号不是数字`)
            break
          } else if (data[i].orderNum && typeof data[i].orderNum === 'number' && (data[i].orderNum > 200 || data[i].orderNum < 1)) {
            flag = false
            content.$message.warning(`第${i + 2}行中的排列序号需要在1-200之间`)
            break
          }
        }
        return flag
      },
      editProgramsDialog (item) {
        this.currentItem = item
        this.showProgramDialog = true
      },
      clearAllSelect () {
        this.checkedIds = []
        this.isIndeterminate = false
        this.checkAll = false
      },
      allDelete () {
        if (this.checkedIds.length === 0) {
          return this.$message.warning('请先选择节目')
        }
        let tip = '确定删除选中节目吗?'
        this.pageList.data.find(item => {
          if (this.checkedIds.includes(item.id) && this.hasLocalTag(item.program)) {
            tip = '删除节目可能会影响已编排节目的正常播出，请确认是否删除?'
          }
        })
        this.$confirm(tip, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          cloudApi.deleteProgram({ids: this.checkedIds}, () => {
            if (this.pageList.data.length === this.checkedIds.length && this.pageList.page.pageIndex > 1) {
              this.getPageInfo(this.pageList.page.pageIndex - 1)
            } else {
              this.getPageInfo()
            }
            this.clearAllSelect()
            this.getFileStorage()
            this.$message.success('删除成功')
          })
        }).catch(() => {
        })
      },
      deleteProgram (item) {
        let tip = '确定删除该节目吗?'
        if (this.hasLocalTag(item.program)) {
          tip = '删除节目可能会影响已编排节目的正常播出，请确认是否删除?'
        }
        this.$confirm(tip, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          cloudApi.deleteProgram({ids: [item.id]}, () => {
            if (this.pageList.data.length === 1 && this.pageList.page.pageIndex > 1) {
              this.getPageInfo(this.pageList.page.pageIndex - 1)
            } else {
              this.getPageInfo()
            }
            this.clearAllSelect()
            this.getFileStorage()
            this.$message.success('删除成功')
          })
        }).catch(() => {
        })
      },
      checkAudio (newProgram, item) {
        let audioIds = newProgram.audios.map(item => {
          return item.audioId
        })
        cloudApi.checkAudio({ids: audioIds}, res => {
          newProgram.audios.forEach(item => {
            if (res.includes(item.audioId)) {
              item.isDeleted = true
            }
          })
          let masterChannel = item.program.video.channels.find(v => v.idx === 0)
          this.copyApiConfirm({copyFromId: item.id, programSourceType: masterChannel.type, program: JSON.stringify(newProgram), programSourceUrl: masterChannel.url})
        })
      },
      copyProgram (item) {
        this.$confirm('确定复制节目吗?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          let newProgram = this.$_deepCopy(item.program)
          newProgram.id = this.$_createRandomId()
          if (newProgram.cgIds && newProgram.cgIds.length) {
            newProgram.cgIds = newProgram.cgIds.filter(item => {
              return this.cgs && !!this.cgs.find(v => v.id === item)
            })
          }

          if (newProgram.audios && newProgram.audios.length) {
            this.checkAudio(newProgram, item)
          } else {
            let masterChannel = item.program.video.channels.find(v => v.idx === 0)
            this.copyApiConfirm({copyFromId: item.id, programSourceType: masterChannel.type, program: JSON.stringify(newProgram), programSourceUrl: masterChannel.url})
          }
        }).catch(() => {
        })
      },
      copyApiConfirm (param) {
        cloudApi.copyProgram(param, () => {
          this.getPageInfo()
          this.showProgramDialog = false
          this.getFileStorage()
        }, (code, msg) => {
          this.$message.error(msg)
        })
      },
      addApiConfirm (programs) {
        if (this.adding) {
          return this.$message.warning('正在添加中')
        }
        this.adding = true
        cloudApi.addProgram({programs: programs}, () => {
          this.$message.success('导入成功')
          this.getPageInfo()
          this.adding = false
          this.showProgramDialog = false
        }, (code, msg) => {
          this.adding = false
          this.$message.error(msg)
        })
      },
      editApiConfirm (editProgram) {
        return new Promise((resolve, reject) => {
          let param = {
            id: this.manageId,
            program: JSON.stringify(editProgram)
          }
          if (this.programForm.orderNum) {
            param.orderNum = this.programForm.orderNum
          }
          cloudApi.setProgram(param, () => {
            resolve()
          }, () => {
            reject(new Error(''))
          })
        })
      },
      addProgramDialog () {
        this.currentItem = null
        this.showProgramDialog = true
      },
      cutPlay (item) {
        this.$confirm('是否切换播出该节目?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.cutProgram(item.id)
        }).catch(() => {
        })
      },
      ...mapMutations({
        setLocalStorageInfo: 'SET_LOCAL_STORAGE_INFO',
        setProgramsData: 'SET_PROGRAMS',
        setProgramsSn: 'SET_PROGRAMS_SN'
      }),
      ...mapActions(['getPrograms', 'addProgram', `delProgram`, 'setRepeat', 'modProgram', 'cutProgram', 'getFileStorage'])
    },
    mounted () {
      this.$bus.$on('refreshProgramManage', () => {
        this.getPageInfo()
      })
    },
    created () {
      this.getPageInfo(1)
    }
  }
</script>

<style scoped lang="scss">
  .program-list {
    height: 100%;
    position: relative;
    font-size: 16px;
    .more-dialog {
      position: absolute;
      width: 100px;
      background: #FFFFFF;
      box-shadow: 0px 3px 8px 0px rgba(0, 0, 0, 0.28);
      border-radius: 4px;
      overflow: hidden;

      .more-li {
        width: 100%;
        height: 32px;
        line-height: 32px;
        padding-left: 19px;
        box-sizing: border-box;
        color: #222222;
        cursor: pointer;
        &:hover {
          color: $color-theme;
          background: #FFEBE3;
        }
      }
    }
    .top-area {
      display: flex;
      justify-content: space-between;
      align-items: center;
      height: 50px;
      padding: 7px 15px;
      .download-model {
        margin-right: 31px;
        .el-icon-document:hover {
          color: #fc4f08!important;
        }
      }
      .top-search-input {
        width: 340px;
        height: 40px;
        border-radius: 4px;
        background: #1B1E24;
        border: 1px solid #535D72;
        box-sizing: border-box;
      }
      .search-button {
        background-color: #535D72;
        color: #BCC9E8;
        &:hover {
          background-color: #5e6678;
        }
      }
    }
    .program-wrapper {
      height: calc(100% - 114px);
      box-sizing: border-box;
    }
    .program-bottom {
      position: absolute;
      bottom: 0;
      left: 0;
      display: flex;
      justify-content: space-between;
      align-items: center;
      height: 50px;
      background-color: $color-content-bg;
      width: 100%;
      .loop-btn {
        margin-left: 50px;
      }
    }
    .program-ul {
      height: calc(100% - 46px);
      overflow-y: auto;
      overflow-x: hidden;
      .program-li {
        position: relative;
        display: flex;
        justify-content: flex-start;
        margin: 2px 0;
        padding: 6px 0;
        background: #292E37;
        box-sizing: border-box;
        height: 90px;
        &.active-living {
          background-color: #D01616;
          color: #ffffff;
          .program-option .my-operate-btn {
            background: rgba(255, 255, 255, .5);
            border: none;
            color: #ffffff;
            &:hover {
              background: $color-theme;
            }
            &:active {
              opacity: .8;
            }
          }
          &:hover {
            background-color: #D01616;
          }
        }
        &.active-next {
          background-color: #17A30C;
          color: #ffffff;
          .program-option .my-operate-btn {
            background: rgba(255, 255, 255, .5);
            border: none;
            color: #ffffff;
            &:hover {
              background: $color-theme;
            }
            &:active {
              opacity: .8;
            }
          }
          &:hover {
            background-color: #17A30C;
          }
        }
        &:hover {
          background: #3E4252;
        }
        .status-flag {
          position: absolute;
          left: 0;
          top: 0;
          width: 88px;
          height: 28px;
          line-height: 28px;
          color: #ffffff;
          background: linear-gradient(-45deg,transparent 28px,rgba(255, 255, 255, .3) 0);
        }
        .el-icon-video-camera-solid {
          position: absolute;
          display: none;
          padding: 0 10px;
          cursor: pointer;
          left: 0;
          top: calc(50% - 3px);
          font-size: 22px;
          margin: 0 8px;
          color: $color-theme;
        }
      }
    }
    .list-header {
      display: flex;
      justify-content: flex-start;
      align-items: center;
      color: #646D80;
      font-size: 16px;
      box-sizing: border-box;
      height: 46px;
    }
    .program-option {
      display: none;
      align-items: center;
      justify-content: center;
      flex: 0 0 20%;
      width: 20%;
      &.show-option {
        display: flex;
      }
      .program-option-inner {
        display: flex;
        align-content: space-around;
        flex: 0 0 220px;
        height: 100%;
        flex-wrap: wrap;
      }
      .my-operate-btn {
        display: inline-block;
        margin-right: 5px;
        width: 66px;
      }
      .empty-btn {
        margin-right: 6px;
        width: 66px;
      }
      .my-operate-btn-span {
        background: #4f5867;
        border-color: #4f5867;
        color: #C2D1EE;
        cursor: pointer;
        text-align: center;
        line-height: 28px;
        &:hover {
          background: $color-theme;
          border-color: $color-theme;
          color: #ffffff;
        }
      }
    }
    .audio {
      display: flex;
      align-items: center;
      flex: 0 0 15%;
      width: 15%;
      padding-right: 20px;
      box-sizing: border-box;
      .audio-ul {
        width: 100%;
        max-height: 78px;
        display: flex;
        flex-direction: column;
        overflow-y: auto;
      }
      .audio-li {
        flex-shrink: 0;
        display: inline-block;
        width: 100%;
        @include elliptical();
      }
    }
    .cg-name {
      display: flex;
      align-items: center;
      flex: 0 0 15%;
      width: 15%;
      padding-right: 20px;
      box-sizing: border-box;
      .cg-name-ul {
        width: 100%;
        max-height: 78px;
        display: flex;
        flex-direction: column;
        overflow-y: auto;
      }
      .cg-name-li {
        display: inline-block;
        width: 100%;
        flex-shrink: 0;
        @include elliptical();
      }
    }
    .program-checked {
      display: flex;
      align-items: center;
      justify-content: center;
      flex: 0 0 3%;
      width: 3%;
    }
    .program-number {
      display: flex;
      align-items: center;
      justify-content: center;
      flex: 0 0 13%;
      width: 13%;
      white-space: nowrap;
    }
    .program-name {
      display: flex;
      align-items: center;
      flex: 0 0 20.5%;
      width: 20.5%;
      padding-right: 40px;
      box-sizing: border-box;
      text-indent: 40px;
      .name {
        display: inline-block;
        max-width: 100%;
        @include elliptical();
      }
      .local-tag {
        margin-left: 5px;
        display: inline-block;
        background: #5771AB;
        padding: 2px 6px;
        border-radius: 2px;
        color: #D1DFFF;
        font-size: 12px;
        white-space: nowrap;
        text-indent: 0;
      }
    }
    .program-duration {
      display: flex;
      align-items: center;
      flex: 0 0 10%;
      width: 10%;
    }
    .start-stop-time,
    .loop-time{
      display: flex;
      align-items: center;
      flex: 0 0 16.5%;
      width: 16.5%;
      padding-right: 40px;
      box-sizing: border-box;
      .time {
        display: flex;
        align-items: center;
        line-height: 16px;
        .icon {
          width: 4px;
          height: 4px;
          border-radius: 50%;
          margin-right: 6px;
        }
      }
    }
    .loop-time {
      flex: 0 0 8%;
      width: 8%;
    }
  }
  .box-content {
    max-height: 600px;
    overflow-y: auto;

    .bgm-info-box {
      display: flex;
      justify-content: flex-start;
      align-items: center;
      color: #B5BBCA;
      margin-bottom: 10px;
    }
    .bgm-info-item {
      font-size: 22px;
      display: inline-block;
      margin-right: 50px;
      width: 300px;
      @include elliptical();
    }
  }
  .program-dialog {
    .live-type-tips {
      color: $color-tips;
    }
  }
  .text-btn {
    color: #627FC2;
  }
  ::v-deep .el-checkbox__label {
    padding-left: 0!important;
  }
  ::v-deep .el-input-group__append {
    background-color: #535D72;
    padding: 0 18px;
  }
  ::v-deep .el-checkbox__input {
    transform: scale(1.3);
  }
  ::v-deep .el-table td.el-table__cell {
    border-bottom: 1px solid #434A58;
  }
  .menu-enter-active{
    transition: opacity .5s;
  }
  .menu-enter{
    opacity: 0;
  }
  .el-icon-warning {
    color: #B2BFDE;
  }
  .tips {
    position: absolute;
    bottom: 25px;
    color: #8892A7;
  }
  ::v-deep .el-button--text:focus {
    color: #627FC2;
  }
  ::v-deep .el-button--text:hover {
    color: #FF5B0C;
  }
  ::v-deep .orderInput.el-input-number .el-input__inner {
    text-align: left;
  }
  ::v-deep .el-button {
    font-size: 16px;
  }
  .program-option {
    ::v-deep .el-button {
      font-size: 14px;
    }
  }
  .dropdown-line {
    display: inline-block;
    height: 9px;
    width: 100%;
    border-top: 1px solid #E6E6E6;
  }
</style>
