import * as PIXI from 'pixi.js'
let shaderFrag = `
    varying vec2 vTextureCoord;
    uniform sampler2D uSampler;
    uniform vec4 screenArea;
    uniform vec4 area;
    uniform float angle;
    uniform vec2 flip;

    float cosAngle = cos(radians(-angle));
    float sinAngle = sin(radians(-angle));

    vec2 getPointByRotate(vec2 uv) {
      vec2 c = vec2(area.z + area.x/2.0, area.w + area.y/2.0);
      vec2 p1a = uv * screenArea.xy - c.xy;
      return (vec2(p1a.x * cosAngle - p1a.y * sinAngle,  p1a.x * sinAngle + p1a.y * cosAngle) + c.xy) /screenArea.xy;
    }

    bool isInArea(vec2 uv) {
      float x1 = area.z/screenArea.x;
      float x2 = (area.z + area.x)/screenArea.x;
      float y1 = area.w/screenArea.y;
      float y2 = (area.w + area.y)/screenArea.y;
      if (uv.x > x1 && uv.x < x2 && uv.y > y1 && uv.y < y2) {
        return bool(true);
      }
      return bool(false);
    }
    
    vec2 parse2Screen(vec2 uv) {
      float x1 = area.z/screenArea.x;
      float y1 = area.w/screenArea.y;
      return vec2((uv.x -x1) * screenArea.x/area.x, (uv.y - y1) * screenArea.y/area.y);
    }

    void main()
    {
      vec2 uv = getPointByRotate(vTextureCoord);
      if (isInArea(uv)) {
        vec2 uv2 = parse2Screen(uv);
        if(flip.x == 1.0 && flip.y == 0.0){
          gl_FragColor = texture2D(uSampler, vec2((1.0-uv2.x), uv2.y));
         }else if(flip.x == 1.0 && flip.y == 1.0){
          gl_FragColor = texture2D(uSampler, vec2((1.0-uv2.x),  (1.0-uv2.y)));
         }else if(flip.x == 0.0 && flip.y == 1.0){
          gl_FragColor = texture2D(uSampler, vec2(uv2.x, (1.0-uv2.y))); 
         }else{
          gl_FragColor = texture2D(uSampler, vec2(uv2.x, uv2.y));
         }
      } else {
        gl_FragColor = vec4(0,0,0,0);
      }
    }
  `
export default () => {
  return new PIXI.Filter(null, shaderFrag)
}
