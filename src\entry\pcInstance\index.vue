<template>
  <div id="app">
    <live-instance v-if="canGetLiveConfig"></live-instance>
    <div class="live-init-page" :class="{'browser-tips': showBrowserTips}" v-else>
      <div class="tips-box">
        <div class="browser-tips" v-if="showBrowserTips">
          <img src="../../common/img/browser-tips.png" alt="">
          <h3 style="margin: 8px;">全时播控仅支持 <a style="color:#f6c20c;" href="https://www.google.cn/chrome/">谷歌浏览器</a> 使用</h3>
          <p style="color:#fff;">请下载谷歌浏览器后使用</p>
        </div>
        <div class="password-form" v-else-if="showPwdModal">
          <div class="input-tips">输入口令进入控制台</div>
          <div class="input-box">
            <input class="input-password" v-model.trim="livePassword" placeholder="请输入正确口令" @keyup.enter="confirmPassword">
            <div class="input-btn" @click.stop="confirmPassword">
              <img class="img" src="../../common/img/input-btn.png" alt="">
            </div>
          </div>
        </div>
        <div class="password-form" v-else-if="showSerialModal">
          <div class="input-tips">全时播控激活</div>
          <div class="input-box">
            <input class="input-password" v-model.trim="serial" placeholder="请输入序列号" @keyup.enter="confirmSerial">
            <div class="input-btn" @click.stop="confirmSerial">
              <img class="img" src="../../common/img/input-btn.png" alt="">
            </div>
          </div>
        </div>
        <div class="init-live-progress" v-else-if="showEnableProgress">
          <div>
            <el-progress type="circle" :width="150" :stroke-width="8" color="#FF5B0C" :percentage="percentage"></el-progress>
          </div>
          <p class="tips" v-if="percentage<100">全时播控正在激活中</p>
          <p class="tips" v-else>激活完成</p>
        </div>
        <div class="init-live-progress" v-else-if="showInitLiveProgress">
          <div>
            <el-progress type="circle" :width="150" :stroke-width="8" color="#FF5B0C" :percentage="percentage"></el-progress>
          </div>
          <p class="tips" v-if="percentage<100">全时播控实例正在创建中</p>
          <p class="tips" v-else>创建完成</p>
        </div>
      </div>
    </div>
    <vVideoPlayer></vVideoPlayer>
  </div>
</template>

<script>
  import {mapGetters, mapMutations, mapActions} from 'vuex'
  import cloudApi from 'api/cloud'
  import liveApi from 'api/live'
  import liveInstance from 'components/live-instance'
  import vVideoPlayer from '@/components/base/player/v-video-player.vue'
  import moment from 'moment'
  import store from 'store'
  import * as types from 'store/mutation-types'

  const STUDIO_PWD_TIME_LIMIT = 3 * 24 * 60 * 60 * 1000
  export default {
    name: 'instancePage',
    components: {liveInstance, vVideoPlayer},
    computed: {
      ...mapGetters([
        'machineInfo',
        'instanceId',
        'pwd'
      ])
    },
    watch: {
    $route: {
      handler: function(val, oldVal) {
        if (val.params.id !== oldVal.params.id) {
          location.reload()
        }
      },
      deep: true
    }
    },
    data () {
      return {
        showEnableProgress: false,
        showSerialModal: false,
        canGetLiveConfig: false,
        showInitLiveProgress: false,
        showPwdModal: false,
        showBrowserTips: false,
        percentage: 0,
        livePassword: '',
        serial: '',
        livePwdArr: [],
        freshStateTimer: null
      }
    },
    methods: {
      showLoading () {
        this.showEnableProgress = true
        this.freshStateIsLocal()
        this.freshStateTimer = setInterval(() => {
          this.freshStateIsLocal()
        }, 1000)
      },
      freshStateIsLocal () {
        this.percentage = Number(this.percentage + 25)
        if (this.percentage >= 100) {
          this.showEnableProgress = false
          this.showPwdModal = true
          clearInterval(this.freshStateTimer)
        }
      },
      confirmSerial () {
        if (!this.serial) {
          return this.$message.warning('请先输入序列号')
        }
        cloudApi.setSerialNumberReq({serialNumber: this.serial}, res => {
          this.showSerialModal = false
          this.setMachineInfo({authorized: true})
          this.showLoading()
        }, (code, msg) => {
          this.$message.error(msg)
        })
      },
      checkoutSerial () {
        if (!this.machineInfo.authorized) { // 机器未激活
          this.showSerialModal = true
          return
        }
        this.livePwdArr = this.getLivePwdArr()
        if (this.pwd || this.getLivePwd()) {
          this.getLiveInfo('existPwd')
        } else {
          this.showPwdModal = true
        }
      },
      confirmPassword () {
        if (!this.livePassword) {
          this.$message.warning('请先输入密码')
        }
        this.setPwd(this.livePassword)
        this.getLiveInfo()
      },
      getLivePwdArr () {
        let livePwdArr = JSON.parse(window.localStorage.getItem('live_pwd')) || []
        if (livePwdArr.length) {
          let newTime = new Date().getTime()
          livePwdArr = livePwdArr.filter(item => newTime - item.time <= STUDIO_PWD_TIME_LIMIT)
          localStorage.setItem('live_pwd', JSON.stringify(livePwdArr))
        }
        return livePwdArr
      },
      getLivePwd () {
        let livePwd = this.livePwdArr.find(item => {
          return item.id === this.instanceId
        })
        if (livePwd) {
          let pwd = livePwd ? livePwd.password : ''
          this.setPwd(pwd)
          return pwd
        } else {
          return ''
        }
      },
      localSavePwd () {
        let findIndex = this.livePwdArr.findIndex(item => item.id === this.instanceId)
        if (findIndex === -1) {
          this.livePwdArr.push({id: this.instanceId, password: this.pwd, time: new Date().getTime()})
          localStorage.setItem('live_pwd', JSON.stringify(this.livePwdArr))
        } else {
          this.livePwdArr[findIndex] = {id: this.instanceId, password: this.pwd, time: new Date().getTime()}
          localStorage.setItem('live_pwd', JSON.stringify(this.livePwdArr))
        }
      },
      showInitLivePage () {
        this.showInitLiveProgress = true
        this.freshState()
        this.freshStateTimer = setInterval(() => {
          this.freshState()
        }, 4000)
      },
      freshState () {
        let param = {instanceId: this.instanceId, pwd: this.pwd}
        cloudApi.freshState(param, res => {
          this.percentage = Number(res ? res.creatingPercentage : 0)
          if (this.percentage >= 100) {
            this.getLiveInfo()
            clearInterval(this.freshStateTimer)
          }
        }, (code, msg) => {
          this.$message.error(msg)
        })
      },
      showLivePage (res) {
        this.getLiveConfig()
      },
      getLiveConfig () {
        liveApi.getConfig({}, res => {
          let timeDifference = moment(res.localTime).unix() * 1000 - new Date().getTime()
          this.setTimeDifference(timeDifference)
          let configObj = res
          configObj.cgs = configObj.cgs ? configObj.cgs.reverse() : []
          this.setLiveConfig(configObj)
          this.canGetLiveConfig = true
          this.showInitLiveProgress = false
        }, (code, msg) => {
          this.$message.error(msg)
        })
      },
      getLiveInfo (type) {
        let param = {pwd: this.pwd, instanceId: this.instanceId}
        cloudApi.instanceInfo(param, res => {
          this.setRoleType(res.roleType)
          this.showPwdModal = false
          this.localSavePwd()
          this.showLivePage(res)
        }, (code, msg) => {
          if (type === 'existPwd') {
            this.showPwdModal = true
            localStorage.removeItem('live_pwd')
            this.$_deleteCookie('live_pwd_' + this.instanceId)
          } else {
            if (code === 20000) {
              this.showPwdModal = true
              this.$message.warning('口令错误，请输入正确口令！')
            } else {
              this.$message.error(msg)
            }
          }
        })
      },
      ...mapMutations({
        setRoleType: 'SET_ROLETYPE',
        setInstanceId: 'SET_INSTANCE_ID',
        setPwd: 'SET_PWD',
        setTimeDifference: 'SET_TIME_DIFFERENCE',
        setLocalTimeDifference: 'SET_LOCAL_TIME_DIFFERENCE',
        setMachineInfo: 'SET_MACHINE_INFO'
      }),
      ...mapActions([
        'setLiveInfo',
        'setLiveConfig'
      ])
    },
    created () {
      console.log('当前版本号: 2.0.0')
      if (this.machineInfo.instanceCount === 1) {
        history.replaceState({}, '', location.origin)
      }
      if (this.$route.params.id && this.machineInfo.instanceCount !== 1) {
        this.setInstanceId(this.$route.params.id)
      }
      cloudApi.getServerTime({}, res => {
        this.setLocalTimeDifference(res.timestamp - new Date().getTime())
        this.checkoutSerial()
      })
    }
  }
</script>

<style scoped lang="scss">
  #app {
    display: flex;
    justify-content: center;
    align-items: center;
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background: url("../../common/img/init-bg.png") no-repeat center/cover;
    &.browser-tips {
      background: #eeeff6;
    }
    .tips-box {
      position: relative;
      top: -16%;
      overflow: hidden;
      color: rgba(255, 255, 255, 0.9);
    }
    .browser-tips {
      text-align: center;
      color: #fff;
    }
    .password-form {
      text-align: center;
      .input-tips {
        margin-bottom: 10px;
        font-size: 30px;
        color: rgba(255, 255, 255, 0.9);
      }
      .input-box {
        position: relative;
        width: 300px;
        .input-password {
          width: 100%;
          height: 44px;
          border-radius: 44px;
          outline: none;
          border: none;
          text-indent: 14px;
          background-color: rgba(255, 255, 255, 0.7);
          font-size: 16px;
          color: $color-border2;
        }
        .input-btn {
          position: absolute;
          top: 3px;
          right: 4px;
          @include flex-c-c();
          z-index: 10;
          width: 40px;
          height: 40px;
          border-radius: 50%;
          background-color: $color-theme;
          cursor: pointer;
          .img {
            width: 80%;
            height: 80%;
          }
        }
      }
    }
    .init-live-progress {
      text-align: center;
      padding: 30px 0;
      width: 400px;
      .tips {
        font-size: 16px;
        margin-top: 10px;
      }
      /deep/ .el-progress__text {
        color: rgba(255, 255, 255, .9);
      }
    }
  }
</style>
