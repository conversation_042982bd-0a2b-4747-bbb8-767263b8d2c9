import * as types from './mutation-types'

const mutations = {
  [types.SET_INSTANCE_ID] (state, id) {
    state.instanceId = id
  },
  [types.SET_PWD] (state, pwd) {
    state.pwd = pwd
  },
  [types.SET_LIVE_ID] (state, liveId) {
    state.liveId = liveId
  },
  [types.SET_LIVE_SECRET_KEY] (state, secretKey) {
    state.secretKey = secretKey
  },
  [types.SET_TIME_DIFFERENCE] (state, timeDifference) {
    state.timeDifference = timeDifference
  },
  [types.SET_HTTP_END_POINT] (state, httpEndPoint) {
    state.httpEndPoint = httpEndPoint
  },
  [types.SET_LIVE_WIDTH] (state, liveWidth) {
    state.liveWidth = liveWidth
  },
  [types.SET_LIVE_HEIGHT] (state, liveHeight) {
    state.liveHeight = liveHeight
  },
  [types.SET_WARN_SETTING] (state, warnSetting) {
    if (warnSetting && warnSetting.mailList) {
      state.warnSetting.mailList = JSON.parse(JSON.stringify(warnSetting.mailList))
    }
    if (warnSetting && warnSetting.phoneList) {
      state.warnSetting.phoneList = JSON.parse(JSON.stringify(warnSetting.phoneList))
    }
  },
  [types.INT_CGS] (state, cgs) {
    state.cgs = cgs || []
  },
  [types.SET_CGS] (state, cgsObj) {
    if (cgsObj.id) {
      let cgIndex = state.cgs.findIndex(item => {
        return item.id === cgsObj.id
      })
      cgsObj.index = cgIndex
    }
    console.log(cgsObj)
    if (cgsObj.cgItem) {
      state.cgs.splice(cgsObj.index, cgsObj.deleteNum, cgsObj.cgItem)
    } else {
      state.cgs.splice(cgsObj.index, cgsObj.deleteNum)
    }
    console.log(state.cgs)
  },
  [types.CLEAR_PGM_CGS] (state) {
    state.cgs.forEach(item => {
      item.selected = false
    })
  },
  [types.SET_SOUND_MIXER] (state, audio) {
    Object.assign(state.audio, audio)
  },
  [types.SET_EMERGENCY] (state, Emergency) {
    Object.assign(state.Emergency, Emergency)
  },
  [types.SET_OUTPUT] (state, output) {
    Object.assign(state.output, output)
  },
  [types.SET_PROGRAMS] (state, programs) {
    state.programs = programs || []
  },
  [types.SET_TODAY_PROGRAMS] (state, programs) {
    state.todayPrograms = programs || []
  },
  [types.SET_LOOP_PROGRAMS] (state, programs) {
    state.loopPrograms = programs || []
  },
  [types.SET_AUDIO] (state, audio) {
    state.audio = audio
  },
  [types.SET_LOOPSTATUS] (state, loopStatus) {
    state.loopStatus = loopStatus
  },
  [types.SET_DEFAULTPIC] (state, defaultPic) {
    state.defaultPic = defaultPic
  },
  [types.SET_VERSION_INFO] (state, versionInfo) {
    state.versionInfo = versionInfo
  },
  [types.SET_LOCAL_TIME_DIFFERENCE] (state, localTimeDifference) {
    state.localTimeDifference = localTimeDifference
  },
  [types.SET_PROGRAMS_SN] (state, programsSn) {
    state.programsSn = programsSn
  },
  [types.SET_BACKGROUND] (state, background) {
    state.background = background
  },
  [types.SET_MACHINE_INFO] (state, machineInfo) {
    state.machineInfo = Object.assign({}, state.machineInfo, machineInfo)
  },
  [types.SET_ROLETYPE] (state, roleType) {
    state.roleType = roleType
  },
  [types.SET_LOCAL_STORAGE_INFO] (state, localStorageInfo) {
    state.localStorageInfo = Object.assign({}, state.localStorageInfo, localStorageInfo)
  },
  [types.SET_PPOR_INFO] (state, portRange) {
    state.portRange = Object.assign({}, state.portRange, portRange)
  },
  [types.SET_SCHEDULES] (state, schedules) {
    state.schedules = schedules || []
  },
  [types.SET_CURRENT_SCHEDULE_ID] (state, currentScheduleId) {
    state.currentScheduleId = currentScheduleId
  },
  [types.SET_CARD_NUMBER] (state, cardNumber) {
    state.cardNumber = cardNumber
  },
  [types.SET_MAPPED_IP] (state, mappedIp) {
    state.mappedIp = mappedIp
  },
  [types.SET_HAS_PARENT_LIMIT] (state, hasParentLimit) {
    state.hasParentLimit = hasParentLimit
  },
  [types.SET_LAYOUTS] (state, layouts) {
    state.layouts = layouts || []
  },
  [types.ADD_LAYOUT] (state, layout) {
    if (layout) {
      state.layouts.unshift(layout)
    }
  },
  [types.UPDATE_LAYOUT] (state, layout) {
    if (layout && layout.id) {
      const index = state.layouts.findIndex(item => item.id === layout.id)
      if (index !== -1) {
        state.layouts.splice(index, 1, layout)
      }
    }
  },
  [types.DELETE_LAYOUT] (state, layoutId) {
    if (layoutId) {
      state.layouts = state.layouts.filter(layout => layout.id !== layoutId)
    }
  },
  [types.SET_CURRENT_LAYOUT_ID] (state, layoutId) {
    state.currentLayoutId = layoutId
  }
}
export default mutations
