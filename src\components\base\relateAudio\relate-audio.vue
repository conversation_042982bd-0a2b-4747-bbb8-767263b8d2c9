<template>
  <el-dialog class="relate-audio" :title="relateItem ? '关联音频' : '关联背景音乐'" width="700px" top="70px" :visible.sync="showDialog" :close-on-click-modal="false"
             @close="$emit('close')">
    <el-table :data="packageInfo.data" style="width: 100%" stripe height="500" v-loading="packageInfo.loading" element-loading-text="拼命加载中"
              element-loading-spinner="el-icon-loading" element-loading-background="rgba(0, 0, 0, 0.8)" border row-key="id" size="small"
              @selection-change="handleSelectionChange" id="multipleTable">
      <el-table-column type="selection" align="center" width="55" :selectable="selectable"></el-table-column>
      <el-table-column label="音频文件名" prop="fileName"></el-table-column>
      <el-table-column label="类型">
        <template slot-scope="scope">
          <span>{{scope.row.type ? '音频流' : '点播'}}</span>
        </template>
      </el-table-column>
      <el-table-column label="音频时长">
        <template slot-scope="scope">
          <span>{{scope.row.duration | formatDurationMs}}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作">
        <template slot-scope="scope">
          <el-button type="info" @click="watchAudio(scope.row)" size="mini" style="font-size: 12px">观看</el-button>
        </template>
      </el-table-column>
      <div class="empty-box wh100" v-if="packageInfo.data && !packageInfo.data.length" slot="empty">
        <no-data tips="暂无音频"></no-data>
      </div>
    </el-table>
    <footer-pagination style="margin-top: 10px;" small :page="packageInfo.page" @getPageInfo="getPackageInfo"></footer-pagination>
    <span slot="footer" class="dialog-footer">
      <el-button type="info" plain size="small" @click="$emit('close')">取 消</el-button>
      <el-button type="primary" size="small" @click="confirmMultiple()">确 定</el-button>
    </span>
    <div class="tips">
      <p>注：</p>
      <p>1、关联多个点播文件时，单个文件播完会自动播放下一个</p>
      <p>2、关联音频直播流时，会一直播放该直播流；需要手动取消关联</p>
    </div>
  </el-dialog>
</template>

<script>
  import footerPagination from '@/components/pagination/footer-pagination'
  import liveApi from 'api/live'
  import cloudApi from 'api/cloud'
  import {mapMutations, mapActions, mapGetters} from 'vuex'
  import noData from 'components/base/no-data'

  export default {
    name: 'relate-audio',
    components: {footerPagination, noData},
    props: {
      programType: {
        type: String,
        default: ''
      },
      scheduleId: { // 节目编排Id
        type: Number,
        default: 0
      },
      manageId: { // 节目管理Id
        type: Number,
        default: 0
      },
      relateItem: { // 关联节目项
        type: Object,
        default: () => {}
      },
      selects: { // 已关联音频
        type: Array,
        default: () => []
      }
    },
    computed: {
      ...mapGetters([
        'audio'
      ])
    },
    data () {
      return {
        packageInfo: {
          data: [],
          page: this.Page(),
          loading: false
        },
        showDialog: true,
        selectedId: 0,
        selectedIds: []
      }
    },
    methods: {
      selectable (row) {
        let ids = this.selects.map(item => item.audioId)
        if (ids.includes(row.id)) {
          return false
        } else {
          return true
        }
      },
      handleSelectionChange (val) {
        this.selectedIds = val
      },
      watchAudio (item) {
        this.$bus.$emit('getVideoUrl', {
          videoUrl: item.visitUrl,
          name: item.fileName,
          type: 'audio'
        })
      },
      getPackageInfo (page) {
        this.packageInfo.data = []
        let param = {
          name: '',
          page: page || this.packageInfo.page.pageIndex,
          pageSize: 20
        }
        this.packageInfo.loading = true
        cloudApi.getAudioList(param, res => {
          if (res) {
            this.packageInfo.data = res.data
            this.packageInfo.loading = false
          }
          this.GetPage(this.packageInfo, res)
          this.$nextTick(() => {
            this.initCheck()
          })
        }, (code, msg) => {
        })
      },
      confirmMultiple () {
        if (!this.selectedIds.length) return this.$message.warning('请先选择音频')
        if (this.relateItem) { // 节目关联音频
          let editProgram = this.relateItem
          if (!editProgram.audios) {
            editProgram.audios = []
          }
          this.selectedIds.forEach(item => {
            editProgram.audios.push({
              audioUrl: item.path,
              audioName: item.fileName,
              audioId: item.id,
              duration: item.duration,
              audioType: item.type
            })
          })
          if (this.manageId > 0) {
            let param = {
              id: this.manageId,
              program: JSON.stringify(editProgram)
            }
            cloudApi.setProgram(param, () => {
              this.$emit('callBack', editProgram)
              this.$emit('close')
            })
          } else if (this.scheduleId > 0) {
            let param = {
              id: this.scheduleId,
              program: JSON.stringify(editProgram)
            }
            cloudApi.uploadProgramArrangementById(param, () => {
              this.$emit('callBack', editProgram)
              this.$emit('close')
            })
          } else {
            let param = {
              programType: this.programType,
              program: editProgram
            }
            this.modProgram(param).then(() => {
              this.$emit('callBack', editProgram)
              this.$emit('close')
            })
          }
        } else { // 背景音乐设置
          let currentAudios = this.audio.audios ? this.$_deepCopy(this.audio.audios) || [] : []
          this.selectedIds.forEach(item => {
            currentAudios.push({
              audioId: item.id,
              audioName: item.fileName,
              audioUrl: item.path,
              duration: item.duration,
              audioType: item.type
            })
          })
          let param = {
            audios: currentAudios
          }
          liveApi.setBgm(param, () => {
            this.$message.success('设置成功')
            this.setSoundMixerData({audios: param.audios})
            this.$emit('close')
          })
        }
      },
      getInit () {
        this.getPackageInfo()
      },
      initCheck () {
        let els = document.getElementById('multipleTable').getElementsByClassName('el-checkbox__input is-disabled')
        Array.from(els).forEach(e => {
          e.classList.add('is-checked')
        })
      },
      ...mapMutations({
        setSoundMixerData: 'SET_SOUND_MIXER'
      }),
      ...mapActions(['modProgram'])
    },
    created () {
      this.getInit()
    }
  }
</script>

<style scoped lang="scss">
  ::v-deep .el-table td.el-table__cell {
    border-bottom: 1px solid #434A58;
  }
  .tips {
    position: absolute;
    bottom: 25px;
    color: #8892A7;
  }
</style>
