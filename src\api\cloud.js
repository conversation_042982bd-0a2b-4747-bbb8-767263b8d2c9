import Server from 'common/js/request'
import store from 'store/index'
const CryptoJS = require('crypto-js')
let VERSION = '1.1'

let localConfig = {
  isLocal: true,
  secretKey: '',
  liveId: '8008208820',
  instanceIp: '************'
}
const getAuthToken = function (timestamp) {
  let time = store.getters.localTimeDifference + new Date().getTime()
  let content = time + '_' + localConfig.liveId
  return time + '@' + CryptoJS.HmacSHA1(content, '').toString(CryptoJS.enc.Base64)
}
export default {
  getMachineInfo: function (param, successCall, failCall) {
    param.pwd = store.getters.pwd
    Server.requestGet('/cloud/fullTimeLive/machine/info?instanceId=' + store.getters.instanceId, param, successCall, failCall)
  },
  instanceInfo: function (param, successCall, failCall) {
    param.pwd = store.getters.pwd
    Server.request('/cloud/fullTimeLive/instance/info?instanceId=' + store.getters.instanceId, param, successCall, failCall)
  },
  freshState: function (param, successCall, failCall) {
    param.pwd = store.getters.pwd
    Server.request('/cloud/fullTimeLive/instance/freshState?instanceId=' + store.getters.instanceId, param, successCall, failCall)
  },
  templateList: function (param, successCall, failCall) {
    param.pwd = store.getters.pwd
    Server.request('/cloud/studio/console/subtitle/template/list?instanceId=' + store.getters.instanceId, param, successCall, failCall)
  },
  settingList: function (param, successCall, failCall) {
    param.token = getAuthToken()
    param.version = VERSION
    param.pwd = store.getters.pwd
    Server.requestJson('/cloud/fullTimeLive/machine/ctrl/setAlertInfo?instanceId=' + store.getters.instanceId, param, successCall, failCall)
  },
  getSettingList: function (param, successCall, failCall) {
    param.token = getAuthToken()
    param.version = VERSION
    param.pwd = store.getters.pwd
    Server.requestJson('/cloud/fullTimeLive/machine/ctrl/getAlertInfo?instanceId=' + store.getters.instanceId, param, successCall, failCall)
  },
  checkUpdate: function (param, successCall, failCall) {
    param.pwd = store.getters.pwd
    param.token = getAuthToken()
    param.version = VERSION
    Server.requestJson('/cloud/fullTimeLive/machine/ctrl/checkUpgradeComplete?instanceId=' + store.getters.instanceId, param, successCall, failCall)
  },
  checkUpdate: function (param, successCall, failCall) {
    param.pwd = store.getters.pwd
    param.token = getAuthToken()
    param.version = VERSION
    Server.requestJson('/cloud/fullTimeLive/machine/ctrl/checkUpgradeComplete?instanceId=' + store.getters.instanceId, param, successCall, failCall)
  },
  addAudio: function (param, successCall, failCall) {
    param.pwd = store.getters.pwd
    Server.requestJson('/cloud/fullTimeLive/audio/add?instanceId=' + store.getters.instanceId, param, successCall, failCall)
  },
  checkAudio: function (param, successCall, failCall) {
    param.pwd = store.getters.pwd
    Server.requestJson('/cloud/fullTimeLive/audio/exists?instanceId=' + store.getters.instanceId, param, successCall, failCall)
  },
  addAudioBatch: function (param, successCall, failCall) {
    param.pwd = store.getters.pwd
    Server.requestJson('/cloud/fullTimeLive/audio/batch/add?instanceId=' + store.getters.instanceId, param, successCall, failCall)
  },
  getAudioList: function (param, successCall, failCall) {
    param.pwd = store.getters.pwd
    Server.requestJson('/cloud/fullTimeLive/audio/page?instanceId=' + store.getters.instanceId, param, successCall, failCall)
  },
  getAudioListNoPage: function (param, successCall, failCall) {
    param.pwd = store.getters.pwd
    Server.requestJson('/cloud/fullTimeLive/audio/list?instanceId=' + store.getters.instanceId, param, successCall, failCall)
  },
  // 音频添加到播单（复制）
  copyAudioToPlay: function (param, successCall, failCall) {
    Server.requestJson('/cloud/fullTimeLive/audio/addToPlaylist?instanceId=' + store.getters.instanceId, param, successCall, failCall)
  },
  // 移动音频到播单
  moveAudioToPlay: function (param, successCall, failCall) {
    Server.requestJson('/cloud/fullTimeLive/audio/moveToPlaylist?instanceId=' + store.getters.instanceId, param, successCall, failCall)
  },
  modifyAdminPassword: function (param, successCall, failCall) {
    param.pwd = store.getters.pwd
    Server.requestJson('/cloud/fullTimeLive/pwd/updateAdmin?instanceId=' + store.getters.instanceId, param, successCall, failCall)
  },
  getAdminPassword: function (param, successCall, failCall) {
    param.pwd = store.getters.pwd
    Server.request('/cloud/fullTimeLive/pwd/getAdmin?instanceId=' + store.getters.instanceId, param, successCall, failCall)
  },
  deleteAudio: function (param, successCall, failCall) {
    param.pwd = store.getters.pwd
    Server.requestJson('/cloud/fullTimeLive/audio/delete?instanceId=' + store.getters.instanceId, param, successCall, failCall)
  },
  getServerTime: function (param, successCall, failCall) {
    param.pwd = store.getters.pwd
    Server.requestJson('/cloud/fullTimeLive/instance/server/time?instanceId=' + store.getters.instanceId, param, successCall, failCall)
  },
  deleteFile: function (param, successCall, failCall) {
    param.pwd = store.getters.pwd
    Server.requestJson('/cloud/fullTimeLive/file/delete?instanceId=' + store.getters.instanceId, param, successCall, failCall)
  },
  addProgram: function (param, successCall, failCall) {
    param.pwd = store.getters.pwd
    Server.requestJson('/cloud/fullTimeLive/programConfig/add?instanceId=' + store.getters.instanceId, param, successCall, failCall)
  },
  copyProgram: function (param, successCall, failCall) {
    param.pwd = store.getters.pwd
    Server.requestJson('/cloud/fullTimeLive/programConfig/copy?instanceId=' + store.getters.instanceId, param, successCall, failCall)
  },
  editProgram: function (param, successCall, failCall) {
    param.pwd = store.getters.pwd
    Server.requestJson('/cloud/fullTimeLive/programConfig/update?instanceId=' + store.getters.instanceId, param, successCall, failCall)
  },
  setProgram: function (param, successCall, failCall) {
    param.pwd = store.getters.pwd
    Server.requestJson('/cloud/fullTimeLive/programConfig/setProgram?instanceId=' + store.getters.instanceId, param, successCall, failCall)
  },
  deleteProgram: function (param, successCall, failCall) {
    param.pwd = store.getters.pwd
    Server.requestJson('/cloud/fullTimeLive/programConfig/delete?instanceId=' + store.getters.instanceId, param, successCall, failCall)
  },
  getProgramManageList: function (param, successCall, failCall) {
    param.pwd = store.getters.pwd
    Server.requestJson('/cloud/fullTimeLive/programConfig/page?instanceId=' + store.getters.instanceId, param, successCall, failCall)
  },
  restartInstanceReq: function (param, successCall, failCall) {
    param.token = getAuthToken()
    param.version = VERSION
    param.pwd = store.getters.pwd
    Server.requestJson('/cloud/fullTimeLive/machine/ctrl/restart?instanceId=' + store.getters.instanceId, param, successCall, failCall)
  },
  downloadUpgrade: function (param, successCall, failCall) {
    param.token = getAuthToken()
    param.version = VERSION
    Server.requestJson('/ctrl/downloadUpgradeFile?instanceId=' + store.getters.instanceId, param, successCall, failCall)
  },
  upgradeReq: function (param, successCall, failCall) {
    param.pwd = store.getters.pwd
    param.token = getAuthToken()
    param.version = VERSION
    Server.requestJson('/cloud/fullTimeLive/machine/ctrl/upgrade?instanceId=' + store.getters.instanceId, param, successCall, failCall)
  },
  setSerialNumberReq: function (param, successCall, failCall) {
    param.pwd = store.getters.pwd
    param.token = getAuthToken()
    param.version = VERSION
    Server.requestJson('/cloud/fullTimeLive/machine/ctrl/setSerialNumber?instanceId=' + store.getters.instanceId, param, successCall, failCall)
  },
  getPreviewVideo: function (param, sdp, successCall, failCall) {
    let newParam = {token: getAuthToken(), version: VERSION, sdp: btoa(JSON.stringify(sdp)), program: param.program, audio: param.audio, cgs: param.cgs, defaultPic: param.defaultPic}
    Server.requestJson('/cloud/fullTimeLive/machine/ctrl/preview?instanceId=' + store.getters.instanceId, newParam, successCall, failCall)
  },
  getProgramArrangement: function (param, successCall, failCall) {
    param.pwd = store.getters.pwd
    Server.requestGet('/cloud/fullTimeLive/programArrangement/list?instanceId=' + store.getters.instanceId, param, successCall, failCall)
  },
  addProgramArrangement: function (param, successCall, failCall) {
    param.pwd = store.getters.pwd
    Server.requestJson('/cloud/fullTimeLive/programArrangement/add?instanceId=' + store.getters.instanceId, param, successCall, failCall)
  },
  uploadProgramArrangement: function (param, successCall, failCall) {
    param.pwd = store.getters.pwd
    Server.requestJson('/cloud/fullTimeLive/programArrangement/update?instanceId=' + store.getters.instanceId, param, successCall, failCall)
  },
  uploadProgramArrangementById: function (param, successCall, failCall) {
    param.pwd = store.getters.pwd
    Server.requestJson('/cloud/fullTimeLive/programArrangement/setProgram?instanceId=' + store.getters.instanceId, param, successCall, failCall)
  },
  deleteProgramArrangement: function (param, successCall, failCall) {
    param.pwd = store.getters.pwd
    Server.request('/cloud/fullTimeLive/programArrangement/delete?instanceId=' + store.getters.instanceId, param, successCall, failCall)
  },
  getCalendarData: function (param, successCall, failCall) {
    param.pwd = store.getters.pwd
    Server.requestJson('/cloud/fullTimeLive/programArrangement/schedule?instanceId=' + store.getters.instanceId, param, successCall, failCall)
  },
  getInstanceList: function (param, successCall, failCall) {
    param.pwd = store.getters.pwd
    Server.requestGet('/cloud/fullTimeLive/instance/list?instanceId=' + store.getters.instanceId, param, successCall, failCall)
  },
  getInstanceInfo: function (param, successCall, failCall) {
    param.pwd = store.getters.pwd
    Server.requestGet('/cloud/fullTimeLive/instance?instanceId=' + store.getters.instanceId, param, successCall, failCall)
  },
  setInstanceInfo: function (param, successCall, failCall) {
    param.pwd = store.getters.pwd
    Server.requestJson('/cloud/fullTimeLive/instance?instanceId=' + store.getters.instanceId, param, successCall, failCall)
  },
  modifyPassword: function (param, successCall, failCall) {
    Server.requestJson('/cloud/fullTimeLive/pwd/update?instanceId=' + store.getters.instanceId, param, successCall, failCall)
  },
  getInstancePass: function (param, successCall, failCall) {
    Server.requestJson('/cloud/fullTimeLive/pwd/getOperator?instanceId=' + store.getters.instanceId, param, successCall, failCall)
  },
  saveInstancePort: function (param, successCall, failCall) {
    param.token = getAuthToken()
    param.version = VERSION
    Server.requestJson('/ctrl/setUdpPortRange', param, successCall, failCall)
  },
  uploadLut: function (param, successCall, failCall) {
    param.pwd = store.getters.pwd
    Server.requestJson('/cloud/fullTimeLive/lut/add?instanceId=' + store.getters.instanceId, param, successCall, failCall)
  },
  getLutList: function (param, successCall, failCall) {
    param.pwd = store.getters.pwd
    Server.requestJson('/cloud/fullTimeLive/lut/page?instanceId=' + store.getters.instanceId, param, successCall, failCall)
  },
  setPreviewExitt: function (param, successCall, failCall) {
    Server.request('/cloud/fullTimeLive/machine/ctrl/closePreview', param, successCall, failCall)
  },
  setMappedIp: function (param, successCall, failCall) {
    param.token = getAuthToken()
    param.version = VERSION
    Server.requestJson('/ctrl/setSdpIp', param, successCall, failCall)
  }
}
