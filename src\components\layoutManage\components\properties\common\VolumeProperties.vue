<template>
  <div class="volume-properties">
    <div class="property-section">
      <div class="section-title">音频调节</div>
      <div class="property-group">
        <div class="volume-control">
          <el-slider
            v-model="localVolume"
            :min="0"
            :max="100"
            :show-tooltip="false"
            @change="handleVolumeChange"
          />
          <div class="volume-input">
            <el-input-number
              v-model="localVolume"
              :min="0"
              :max="100"
              size="small"
              controls-position="right"
              @change="handleVolumeChange"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'VolumeProperties',
  props: {
    component: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      localVolume: 100
    }
  },
  watch: {
    component: {
      handler() {
        this.syncLocalValues()
      },
      immediate: true
    }
  },
  methods: {
    syncLocalValues() {
      this.localVolume = this.component.volume || 100
    },
    handleVolumeChange() {
      this.$emit('update', { volume: this.localVolume })
    }
  }
}
</script>

<style lang="scss" scoped>
.volume-properties {
  .property-section {
    margin-bottom: 20px;

    .section-title {
      font-size: 14px;
      font-weight: 500;
      margin-bottom: 12px;
      padding: 0 16px;
    }
  }

  .property-group {
    margin-bottom: 16px;
    padding: 0 16px;
  }

  .volume-control {
    display: flex;
    align-items: center;
    gap: 12px;

    :deep(.el-slider) {
      flex: 1;

      .el-slider__runway {
        background: #4a4a4a;
      }

      .el-slider__bar {
        background: #ff6b35;
      }

      .el-slider__button {
        border-color: #ff6b35;
      }
    }

    .volume-input {
      width: 80px;

      :deep(.el-input-number) {
        width: 100%;

        .el-input__inner {
          background: #4a4a4a;
          border-color: #606060;
          color: #ffffff;
          height: 28px;
          line-height: 28px;
        }
      }
    }
  }
}
</style>
