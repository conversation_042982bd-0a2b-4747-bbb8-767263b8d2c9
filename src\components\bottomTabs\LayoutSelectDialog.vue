<template>
  <el-dialog
    title="选择布局"
    :visible.sync="dialogVisible"
    width="900px"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @close="handleClose"
  >
    <div class="layout-select-dialog">
      <!-- 搜索栏 -->
      <div class="search-bar">
        <el-input
          v-model="searchKeyword"
          placeholder="搜索布局名称"
          size="small"
          clearable
          @input="handleSearch"
        >
          <template #append>
            <el-button icon="el-icon-search" @click="handleSearch"></el-button>
          </template>
        </el-input>
      </div>

      <!-- 布局网格 -->
      <div class="layout-grid" v-loading="loading">
        <div
          v-for="layout in paginatedLayouts"
          :key="layout.id"
          class="layout-card"
          :class="{ selected: selectedLayoutId === layout.id }"
          @click="handleSelectLayout(layout)"
        >
          <!-- 图片预览区域 -->
          <div class="image-preview">
            <!-- 比例显示区域 -->
            <div class="ratio-display">
              {{ layout.ratio || '16:9' }}
            </div>
            <div
              v-if="layout.previewImage"
              class="preview-background"
              :style="{ backgroundImage: `url(${layout.previewImage})` }"
            ></div>
            <div v-else class="preview-placeholder">
              <i class="el-icon-picture-outline"></i>
            </div>
          </div>

          <!-- 底部信息区域 -->
          <div class="bottom-info">
            <div class="layout-name" :title="layout.name">{{ layout.name }}</div>
          </div>
        </div>

        <!-- 空状态 -->
        <div v-if="!loading && filteredLayouts.length === 0" class="empty-state">
          <i class="el-icon-folder-opened"></i>
          <p>暂无符合条件的布局</p>
        </div>
      </div>

      <!-- 分页 -->
      <div class="pagination-wrapper" v-if="filteredLayouts.length > 0">
        <el-pagination
          @current-change="handlePageChange"
          :current-page="currentPage"
          :page-size="pageSize"
          :total="filteredLayouts.length"
          layout="prev, pager, next"
          small
        ></el-pagination>
      </div>
    </div>

    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose" type="info" size="small">取消</el-button>
      <el-button
        type="primary"
        @click="handleConfirm"
        :disabled="!selectedLayoutId"
        size="small"
      >
        确定
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import { mapGetters } from 'vuex'

export default {
  name: 'LayoutSelectDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    targetRatio: {
      type: String,
      default: '16:9'
    }
  },
  data() {
    return {
      dialogVisible: this.visible,
      loading: false,
      searchKeyword: '',
      selectedLayoutId: null,
      currentPage: 1,
      pageSize: 12 // 每页显示12个布局
    }
  },
  computed: {
    ...mapGetters(['layouts']),

    // 过滤符合当前直播比例的布局
    filteredLayouts() {
      let filtered = this.layouts.filter(layout => {
        // 过滤条件：aspectRatio符合目标比例
        const ratioMatch = layout.ratio === this.targetRatio

        // 搜索关键词过滤
        const searchMatch = !this.searchKeyword.trim() ||
          layout.name.toLowerCase().includes(this.searchKeyword.toLowerCase())

        return ratioMatch && searchMatch
      })

      return filtered
    },

    // 分页后的布局
    paginatedLayouts() {
      const start = (this.currentPage - 1) * this.pageSize
      const end = start + this.pageSize
      return this.filteredLayouts.slice(start, end)
    }
  },
  watch: {
    visible(val) {
      this.dialogVisible = val
      if (val) {
        this.resetDialog()
      }
    },

    dialogVisible(val) {
      this.$emit('update:visible', val)
    }
  },
  methods: {
    resetDialog() {
      this.searchKeyword = ''
      this.selectedLayoutId = null
      this.currentPage = 1
    },

    handleSearch() {
      this.currentPage = 1 // 搜索时重置到第一页
    },

    handleSelectLayout(layout) {
      this.selectedLayoutId = layout.id
    },

    handlePageChange(page) {
      this.currentPage = page
    },

    handleConfirm() {
      if (!this.selectedLayoutId) return

      const selectedLayout = this.layouts.find(layout => layout.id === this.selectedLayoutId)
      if (selectedLayout) {
        this.$emit('layout-selected', selectedLayout)
        this.handleClose()
      }
    },

    handleClose() {
      this.dialogVisible = false
      this.$emit('close')
    }
  }
}
</script>

<style lang="scss" scoped>
.layout-select-dialog {
  .search-bar {
    margin-bottom: 20px;

    .el-input {
      width: 300px;

      :deep(.el-input__inner) {
        background: #555D70;
        border-color: #606060;
        color: #ffffff;

        &::placeholder {
          color: #999999;
        }
      }

      :deep(.el-input-group__append) {
        background: #555D70;
        border-color: #606060;

        .el-button {
          background: transparent;
          color: #ffffff;
          border: none;

          &:hover {
            color: #ff6b35;
          }
        }
      }
    }
  }

  .layout-grid {
    display: flex;
    flex-wrap: wrap;
    gap: 16px;
    min-height: 400px;
    margin-bottom: 20px;

    .layout-card {
      width: 200px;
      height: 160px;
      background: #1a1a1a;
      border-radius: 8px;
      overflow: hidden;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
      transition: transform 0.2s ease, box-shadow 0.2s ease, border 0.2s ease;
      display: flex;
      flex-direction: column;
      cursor: pointer;
      border: 2px solid transparent;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4);
        border-color: #ff6b35;
      }

      &.selected {
        border-color: #ff6b35;
        box-shadow: 0 0 0 2px rgba(255, 107, 53, 0.3);

        .bottom-info {
          background: #ff6b35;
        }

        .layout-name {
          color: #ffffff;
        }
      }
    }

    .image-preview {
      width: 100%;
      height: 120px;
      overflow: hidden;
      position: relative;
      flex-shrink: 0;
      img {
        object-fit: cover;
      }

      .preview-background {
        width: 100%;
        height: 100%;
        background-size: cover;
        background-position: center;
        background-repeat: no-repeat;
        transition: transform 0.3s ease;
      }

      &:hover .preview-background {
        transform: scale(1.05);
      }

      .preview-placeholder {
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        background: #555555;
        color: #999999;
        font-size: 32px;
      }
    }

    .ratio-display {
      position: absolute;
      top: 8px;
      left: 8px;
      color: white;
      font-size: 12px;
      font-weight: 500;
      z-index: 2;
      background: rgba(0, 0, 0, 0.6);
      padding: 2px 6px;
      border-radius: 3px;
      backdrop-filter: blur(3px);
    }

    .bottom-info {
      height: 40px;
      background: #2E333D;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 0 12px;
      flex-shrink: 0;
      transition: background 0.2s ease;
    }

    .layout-name {
      color: white;
      font-size: 12px;
      font-weight: 500;
      text-align: center;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      width: 100%;
    }

    .empty-state {
      width: 100%;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 200px;
      color: #999999;

      i {
        font-size: 48px;
        margin-bottom: 16px;
        color: #666666;
      }

      p {
        margin: 0;
        font-size: 14px;
      }
    }
  }

  .pagination-wrapper {
    display: flex;
    justify-content: center;

    :deep(.el-pagination) {
      .el-pager li {
        background: #383838;
        color: #cccccc;
        border: 1px solid #4a4a4a;

        &:hover {
          color: #ff6b35;
        }

        &.active {
          background: #ff6b35;
          color: #ffffff;
          border-color: #ff6b35;
        }
      }

      .btn-prev,
      .btn-next {
        background: #383838;
        color: #cccccc;
        border: 1px solid #4a4a4a;

        &:hover {
          color: #ff6b35;
        }
      }
    }
  }
}

:deep(.el-dialog) {
  background: #2c2c2c;

  .el-dialog__header {
    background: #2c2c2c;
    border-bottom: 1px solid #4a4a4a;

    .el-dialog__title {
      color: #ffffff;
    }

    .el-dialog__close {
      color: #cccccc;

      &:hover {
        color: #ffffff;
      }
    }
  }

  .el-dialog__body {
    background: #2c2c2c;
    padding: 20px;
  }

  .el-dialog__footer {
    background: #2c2c2c;
    border-top: 1px solid #4a4a4a;

    .el-button {
      &--primary {
        background: #ff6b35;
        border-color: #ff6b35;

        &:hover {
          background: #ff5722;
          border-color: #ff5722;
        }

        &:disabled {
          background: #666666;
          border-color: #666666;
        }
      }
    }
  }
}
</style>
