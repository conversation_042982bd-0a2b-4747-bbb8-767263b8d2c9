<template>
  <el-dialog class="subtitle-cg-dialog" title="海康" :visible.sync="showDialog" top="50px" :width="activeName === 'setting' ? '660px' : '810px'" :close-on-click-modal="false" @close="$emit('close')" append-to-body>
    <el-tabs v-model="activeName">
      <el-tab-pane label="参数配置" name="setting"></el-tab-pane>
      <el-tab-pane label="设备列表" name="list"></el-tab-pane>
    </el-tabs>
    <div v-if="activeName === 'setting'" class="setting-area">
      <el-form>
        <el-form-item label="ip：" label-width="120px">
          <el-input style="width: 340px;" size="small" v-model.trim="formInfo.ip" placeholder="请输入ip"></el-input>
        </el-form-item>
        <el-form-item label="端口号：" label-width="120px">
          <el-input style="width: 340px;" size="small" v-model.trim="formInfo.port" placeholder="请输入端口号"></el-input>
        </el-form-item>
        <el-form-item label="appKey：" label-width="120px">
          <el-input style="width: 340px;" size="small" v-model.trim="formInfo.appKey" placeholder="请输入appKey"></el-input>
        </el-form-item>
        <el-form-item label="appSecret：" label-width="120px">
          <el-input style="width: 340px;" size="small" v-model.trim="formInfo.appSecret" placeholder="请输入appSecret"></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="confirm" size="small">确 定</el-button>
      </span>
    </div>
    <div v-if="activeName === 'list'">
      <haikang-list></haikang-list>
    </div>
  </el-dialog>
</template>

<script>
  import liveApi from 'api/live'
  import haikangList from './haikang-list'

  export default {
    name: 'haikang-setting',
    components: {haikangList},
    props: {
      haikangSettingInfo: {
        type: Object,
        default: () => {}
      }
    },
    data () {
      return {
        activeName: 'setting',
        showDialog: true,
        formInfo: {
          ip: '',
          port: '',
          appKey: '',
          appSecret: ''
        }
      }
    },
    methods: {
      confirm () {
        if (!this.formInfo.ip) {
          return this.$message.warning('请输入ip')
        }
        if (!this.formInfo.port) {
          return this.$message.warning('请输入端口号')
        }
        if (!this.formInfo.appKey) {
          return this.$message.warning('请输入appKey')
        }
        if (!this.formInfo.appSecret) {
          return this.$message.warning('请输入appSecret')
        }
        let param = this.formInfo
        liveApi.setHaikangSetting(param, () => {
          this.$message.success('配置成功')
          this.activeName = 'list'
        }, (code, msg) => {
          this.$message.error(msg)
        })
      },
      getInit () {
        if (this.haikangSettingInfo && this.haikangSettingInfo.ip) {
          this.activeName = 'list'
        }
        this.formInfo = this.haikangSettingInfo
      }
    },
    created () {
      this.getInit()
    }
  }
</script>

<style scoped lang="scss">
  .setting-area {
    padding-top: 20px;
    padding-left: 70px;
    font-weight: 700;
    font-size: 20px;
    color: #AEBBD9;
    .dialog-footer {
      padding: 20px;
      display: flex;
      justify-content: right;
    }
  }
  ::v-deep .el-dialog__body {
    padding-bottom: 5px;
  }
  ::v-deep .el-tabs__nav-wrap::after {
    background-color: #3E4553;
  }
  ::v-deep .el-tabs__nav-scroll {
    margin-left: 20px;
  }
  ::v-deep .el-tabs__header {
    margin-bottom: 0;
  }
</style>
