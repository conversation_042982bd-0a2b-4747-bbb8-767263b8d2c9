<template>
  <div class="calendar-box wh100">
    <div class="calendar-header">
      <div class="flex-c-c">
        <h1 class="title">节目日历</h1>
        <span style="margin-left: 80px;margin-right: 10px">选择日期</span>
        <el-date-picker :picker-options="pickerOptions" v-model="startTime" class="width130" type="date" placeholder="开始时间" size="small" style="width: 149px" :clearable="false" @change="timeUpdate"/>
      </div>
      <div class="right-option">
        <i class="el-icon-close" @click="$emit('close')"></i>
      </div>
    </div>
    <calendar-form class="bottom-form" :canEdit="canEdit" ref="calendarForm" @close="$emit('close')"> </calendar-form>
  </div>
</template>

<script>
import calendarForm from './calendar-form.vue'
import moment from 'moment'
import {mapGetters} from 'vuex'

export default {
  props: {
    canEdit: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    ...mapGetters([
      'localTimeDifference'
    ])
  },
  data () {
    return {
      startTime: '',
      pickerOptions: null
    }
  },

  components: {calendarForm},

  methods: {
    timeUpdate (val) {
      this.$refs.calendarForm.getData(val)
    }
  },
  mounted () {
    this.$refs.calendarForm.getData(this.startTime)
  },
  created () {
    let that = this
    this.startTime = moment(new Date().getTime() + this.localTimeDifference).add(1, 'days').valueOf()
    this.pickerOptions = {
      disabledDate (time) {
        return time.getTime() < moment(moment(new Date().getTime()).valueOf() + that.localTimeDifference).endOf('day').valueOf()
      }
    }
  }
}
</script>

<style scoped lang="scss">
  .calendar-box {
    position: absolute;
    z-index: 2001;
    background-color: #040507;
  }
  .bottom-form {
    height: calc(100% - 57px);
  }
  .calendar-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 20px;
    box-sizing: border-box;
    height: 57px;
    background-color: #373D4A;
    .title {
      font-size: 22px;
      font-family: Microsoft YaHei;
      color: #B2BFDE;
      font-weight: 400;
    }
    .right-option {
      display: flex;
      align-items: center;
    }
    .el-icon-close {
      font-size: 28px;
      cursor: pointer;
      &:hover {
        color: #FC4F08;
      }
    }
  }
</style>
