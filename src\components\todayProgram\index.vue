<template>
<div class="program-list">
  <div class="top-header">
    <span class="title">今日节目单
      <el-tooltip effect="light" content="节目在指定时间段内播出" placement="top">
        <span class="el-icon-warning" style="margin-left: 5px"></span>
      </el-tooltip>
    </span>
    <div>
      <span class="g-option-btn-2" style="width: 74px;height: 28px;line-height: 26px" @click="addProgramDialog()" v-show="roleType !== 2">
        <i class="el-icon-plus" style="margin-right: 5px;font-size: 14px"></i>
        <span>新增</span>
      </span>
      <span class="g-option-btn-2" style="width: 92px;height: 28px;line-height: 26px" @click="openCalendar()">节目日历</span>
    </div>
  </div>
  <div class="program-wrapper">
    <div class="list-header" ref="header">
      <span class="program-startTime">播出时间</span>
      <span class="program-name">节目名称</span>
      <span class="program-duration">节目时长</span>
      <span class="program-bgm" style="margin-top: 3px">BGM</span>
      <span class="program-options">操作</span>
    </div>
    <div class="program-ul g-hide-scroll" id="program-ul">
      <div class="program-li" :class="{'program-complete' : item.isCompleted, 'active-living': isLiving(item), 'active-next': isNextLiving(item)}"
            :key="item.id" v-for="(item) in programList" @mouseenter="programOptionShow(item.id)" @mouseleave="programLeave(item.id)">
        <div class="status-flag" v-if="isLiving(item)">
          <svg-icon icon-class="double-arrow" style="margin-left: 3px"></svg-icon>
          <span>onAir</span>
        </div>
        <div class="status-flag" v-if="isNextLiving(item)">
          <span style="margin-left: 5px">staby</span>
        </div>
        <div class="program-startTime">
          <p style="margin-top: 5px" v-show="isOldDay(item)">{{item.startTime | formatDate('YYYY-MM-DD')}}</p>
          <p>{{item.startTime | formatDate('HH:mm:ss')}}</p>
        </div>
        <div class="program-name">
          <i class="icon el-icon-video-camera-solid" v-if="isLiving(item)"></i>
          <div class="name" :title="item.name">{{item.name}}</div>
        </div>
        <div class="program-duration">
          <div class="time">{{item.duration | formatDuration}}</div>
        </div>
        <div class="program-bgm" :style="{color: item.globalBgmMute ? '#63738F' : '#62CB0D'}">
          <span style="margin-bottom: 3px">{{item.globalBgmMute ? '关闭' : '开启'}}</span>
        </div>
        <div class="program-option" :class="{'show-option': showOptionId === item.id}">
          <template v-if="roleType !== 2">
            <el-button type="danger" class="my-operate-btn" @click="deleteProgram(item)" v-if="item.isCompleted">删除</el-button>
            <el-dropdown @visible-change="programOptionShow1($event)" v-else>
              <span class="my-operate-btn my-operate-btn-span">更多</span>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item @click.native="showPreview(item)" v-if="!isLiving(item)">预览</el-dropdown-item>
                <el-dropdown-item @click.native="relateSubtitleShow(item)">关联字幕</el-dropdown-item>
                <el-dropdown-item @click.native="showRelateAudioDialog(item)">关联音频</el-dropdown-item>
                <el-dropdown-item @click.native="showFramesClipDialog(item)">画面裁剪</el-dropdown-item>
               <el-dropdown-item @click.native="showPaletteDialog(item)">调色</el-dropdown-item>
                <el-dropdown-item @click.native="showLayoutSelectDialogForProgram(item)">关联布局</el-dropdown-item>
                <span class="dropdown-line"></span>
                <el-dropdown-item @click.native="editProgramsDialog(item)">编辑</el-dropdown-item>
                <el-dropdown-item @click.native="deleteProgram(item)" v-if="!isLiving(item)">删除</el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </template>
          <template v-else>
            <el-button type="danger" class="my-operate-btn" @click="showPreview(item)" v-if="!item.isCompleted && !isLiving(item)">预览</el-button>
          </template>
        </div>
      </div>
      <div class="program-empty wh100" v-if="!programList.length">
        <no-data tips="暂无节目"></no-data>
      </div>
    </div>
  </div>
  <el-dialog class="relate-audio-first" title="节目关联音乐" width="700px" top="70px" :visible.sync="programAudioDialog" :close-on-click-modal="false" v-if="programAudioDialog">
    <el-table :data="relateItem.audios" style="width: 100%" stripe height="500" element-loading-spinner="el-icon-loading"
              element-loading-background="rgba(0, 0, 0, 0.8)" border row-key="id" size="small">
      <el-table-column label="音频文件名" prop="audioName"></el-table-column>
      <el-table-column label="类型">
        <template slot-scope="scope">
          <span>{{scope.row.audioType ? '音频流' : '点播'}}</span>
        </template>
      </el-table-column>
      <el-table-column label="音频时长">
        <template slot-scope="scope">
          <span>{{scope.row.duration | formatDurationMs}}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作">
        <template slot-scope="scope">
          <el-button type="info" @click="cancelRelate(scope.row)" size="mini">取消关联</el-button>
        </template>
      </el-table-column>
      <div class="empty-box wh100" v-if="relateItem.audios && !relateItem.audios.length" slot="empty">
        <no-data tips="暂无音频"></no-data>
      </div>
    </el-table>
    <div  slot="footer" class="dialog-footer">
      <el-button size="small" type="primary" @click="relateAudio">关联新文件</el-button>
    </div>
    <div class="tips">
      <p>注：</p>
      <p>1、关联多个点播文件时，单个文件播完会自动播放下一个</p>
      <p>2、关联音频直播流时，会一直播放该直播流；需要手动取消关联</p>
    </div>
  </el-dialog>
  <relate-subtitle v-if="relateSubtitleDialog" @close="relateSubtitleDialog=false" :currentProgram="currentProgram" :programType="programType"></relate-subtitle>
  <relate-audio @close="showRelateAudio=false" v-if="showRelateAudio" :relateItem="relateItem" @callBack="relateCallBack"
   :selects="relateItem.audios" :programType="programType"></relate-audio>
  <frames-clip @close="showFramesClip = false" v-if="showFramesClip" :currentProgramItem="currentProgramItem" :programType="programType"></frames-clip>
  <palette :programType="programType" @close="showPalette = false" v-if="showPalette" :currentProgramItem="currentProgramItem"></palette>
  <pinp-setting @close="showPinpSetting = false" v-if="showPinpSetting" :currentProgramItem="currentProgramItem" :programType="programType"></pinp-setting>
  <preview-dialog v-if="previewDialog" @close="previewDialog = false" :currentProgramItem="currentProgramItem"></preview-dialog>
  <add-program v-model="currentProgramId" v-if="showProgramDialog" @close="showProgramDialog = false" :currentItem="currentItem" :addType="2" :slotId="slotId"></add-program>

  <!-- 布局选择弹窗 -->
  <LayoutSelectDialog
    :visible.sync="showLayoutSelectDialog"
    :target-ratio="currentLiveRatioString"
    @layout-selected="handleLayoutSelected"
    @close="handleLayoutSelectDialogClose"
  />
</div>
</template>

<script>
  import {mapGetters, mapActions, mapMutations} from 'vuex'
  import liveApi from 'api/live'
  import relateSubtitle from '@/components/cgTemplate/relate-subtitle'
  import relateAudio from '@/components/base/relateAudio/relate-audio'
  import framesClip from '@/components/framesClip/frames-clip'
  import palette from '@/components/palette'
  import pinpSetting from '@/components/pinpSetting/pinp-setting'
  import cloudApi from 'api/cloud'
  import addProgram from '../addProgram'
  import previewDialog from '@/components/programList/previewDialog'
  import moment from 'moment'
  import layoutSelectMixin from '@/common/js/layoutSelectMixin'
  export default {
    name: 'program-list',
    mixins: [layoutSelectMixin],
    components: {relateSubtitle, relateAudio, framesClip, pinpSetting, palette, previewDialog, addProgram},
    props: {
      nextProgramId: {
        type: String
      },
      currentProgramId: {
        type: String
      }
    },
    computed: {
      getDownUrl () {
        if (process.env.NODE_ENV === 'production') return process.env.VUE_APP_BASE_URL + 'template.xlsx'
        return 'template.xlsx'
      },
      ...mapGetters([
        'roleType',
        'todayPrograms',
        'cgs',
        'localTimeDifference',
        'programsSn',
        'audio'
      ])
    },
    data () {
      return {
        currentItem: null,
        programAudioDialog: false,
        scale: 1,
        showRelateAudio: false,
        programList: [],
        slotId: null,
        slotIndex: -1,
        showProgramDialog: false,
        relateSubtitleDialog: false,
        adding: false,
        programForm: {
          id: 0,
          name: '',
          startTime: '', // 播出时间，本地时间，格式为"2006-01-02 15:04:05"，仅节目表第一个节目有效
          hour: 0, // 节目小时
          minute: 0, // 节目分钟
          second: 0, // 节目秒
          type: 'stream', // "stream"：直播；"record"：录播
          url: '',
          cgIds: [],
          audios: []
        },
        programRules: {
          name: [
            {required: true, message: '请输入节目名称', trigger: 'blur'}
          ],
          url: [
            {required: true, message: '请输入地址', trigger: 'blur'}
          ],
          startTime: [
            {required: true, message: '请选择播出时间', trigger: 'blur'}
          ]
        },
        importData: [],
        currentProgram: '',
        relateItem: {},
        currentProgramItem: {},
        showFramesClip: false,
        showPinpSetting: false,
        showPalette: false,
        previewDialog: false,
        showOptionId: '',
        programType: 'today'
      }
    },
    methods: {
      isOldDay (item) {
        if (item.startTime.split(' ')[0] === moment().format('YYYY-MM-DD')) {
          return false
        }
        return true
      },
      openCalendar () {
        this.$bus.$emit('showCalendarView', false)
      },
      globalBgmChange (item) {
        if (this.roleType === 2) {
          return this.$message.warning('巡查员无法操作')
        }
        if (item.isCompleted) {
          return this.$message.warning('当前节目已结束')
        }
        this.$confirm(item.globalBgmMute ? '确定开启背景音乐吗?' : '确定关闭背景音乐吗', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          let editProgram = this.$_deepCopy(item)
          editProgram.globalBgmMute = !item.globalBgmMute
          let param = {
            programType: this.programType,
            program: editProgram
          }
          this.modProgram(param).then(() => {
            this.$message.success('操作成功')
          })
        }).catch(() => {
        })
      },
      programLeave () {
        this.timer = setTimeout(() => {
          if (this.showMore) {
            return
          }
          this.showOptionId = ''
        }, 150)
      },
      programOptionShow (id) {
        clearTimeout(this.timer)
        this.showOptionId = id
      },
      programOptionShow1 (flag) {
        this.showMore = flag
      },
      showPreview (item) {
        this.currentProgramItem = item
        this.previewDialog = true
      },
      showPinpDialog (item) {
        this.currentProgramItem = item
        this.showPinpSetting = true
      },
      showFramesClipDialog (item) {
        this.currentProgramItem = item
        this.showFramesClip = true
      },
      showPaletteDialog (item) {
        this.currentProgramItem = item
        this.showPalette = true
      },
      relateCallBack (editProgram) {
        this.relateItem = editProgram
      },
      cancelRelate (item) {
        let editProgram = this.relateItem
        editProgram.audios.forEach((v, i) => {
          if (v.audioId === item.audioId) {
            editProgram.audios.splice(i, 1)
          }
        })
        let param = {
          programType: this.programType,
          program: editProgram
        }
        this.modProgram(param).then(() => {
          this.relateItem = editProgram
          this.$message.success('取消关联成功')
        })
      },
      async showRelateAudioDialog (item) {
        this.relateItem = this.$_deepCopy(item)
        this.relateItem.audios = await this.getFilterRelateItem(item)
        this.programAudioDialog = true
      },
      getFilterRelateItem (item) {
        return new Promise(resolve => {
          let audioIds = item.audios.map(v => {
            return v.audioId
          })
          cloudApi.checkAudio({ids: audioIds}, res => {
            let result = item.audios.filter(v => {
              return !res.includes(v.audioId)
            })
            resolve(result)
          }, () => {
            resolve(item.audios)
          })
        })
      },
      relateAudio () {
        this.showRelateAudio = true
      },
      relateSubtitleShow (item) {
        this.currentProgram = item.id
        this.relateSubtitleDialog = true
      },
      editProgramsDialog (item) {
        this.currentItem = item
        this.slotId = null
        this.showProgramDialog = true
      },
      isNextLiving (item) {
        return this.nextProgramId === item.id && this.nextProgramId !== this.currentProgramId
      },
      isLiving (item) {
        return this.currentProgramId === item.id
      },
      deleteHistoryProgram (item) {
        this.$confirm('确定要删除该历史节目吗?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          liveApi.removeHistoryProgram({id: item.id}, () => {
            this.getPrograms()
          })
        }).catch(() => {
        })
      },
      deleteProgram (item) {
        this.$confirm('确定删除该节目单吗?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          let param = {
            id: item.id,
            programType: this.programType
          }
          this.delProgram(param)
        }).catch(() => {
        })
      },
      checkAudio (newProgram) {
        let audioIds = newProgram.audios.map(item => {
          return item.audioId
        })
        cloudApi.checkAudio({ids: audioIds}, res => {
          newProgram.audios.forEach(item => {
            if (res.includes(item.audioId)) {
              item.isDeleted = true
            }
          })
          this.copyConfirm(newProgram)
        })
      },
      copyConfirm (newProgram) {
        let param = {
          programType: this.programType,
          programs: [newProgram]
        }
        this.addProgram(param).then(() => {
          this.$emit('close')
        })
      },
      copyProgram (item) {
        this.$confirm('确定复制节目吗?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          let newProgram = this.$_deepCopy(item)
          newProgram.id = this.$_createRandomId()
          if (newProgram.cgIds && newProgram.cgIds.length) {
            newProgram.cgIds = newProgram.cgIds.filter(item => {
              return this.cgs && !!this.cgs.find(v => v.id === item)
            })
          }
          if (newProgram.audios && newProgram.audios.length) {
            this.checkAudio(newProgram)
          } else {
            this.copyConfirm(newProgram)
          }
        }).catch(() => {
        })
      },
      addProgramDialog () {
        this.slotId = null
        this.currentItem = null
        this.showProgramDialog = true
      },
      slotProgramDialog (item) {
        this.slotId = item.id
        this.currentItem = null
        this.showProgramDialog = true
      },
      getInit () {
        this.getPrograms()
      },
      ...mapMutations({
        setProgramsSn: 'SET_PROGRAMS_SN'
      }),
      ...mapActions(['getPrograms', 'addProgram', `delProgram`, 'modProgram'])
    },
    mounted () {
    },
    created () {
      this.getInit()
    },
    watch: {
      todayPrograms: {
        handler () {
          this.programList = this.$_deepCopy(this.todayPrograms)
        },
        deep: true
      },
      currentProgramId () {
        if (this.programList.length) {
          setTimeout(() => {
            let el = document.getElementsByClassName('active-living')[0]
            if (el) {
              el.scrollIntoView({block: 'start'})
            }
          }, 100)
        }
      }
    }
  }
</script>

<style scoped lang="scss">
  .program-list {
    height: 100%;
    position: relative;
    box-sizing: border-box;
    border: 2px solid #373D4A;
    font-size: 16px;
    .top-header {
      padding-left: 19px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      height: 49px;
      background-color: #373D4A;
      box-sizing: border-box;
      .title {
        font-size: 18px;
        color: #909AB0;
      }
    }
    .more-dialog {
      position: absolute;
      width: 100px;
      background: #FFFFFF;
      box-shadow: 0px 3px 8px 0px rgba(0, 0, 0, 0.28);
      border-radius: 4px;
      overflow: hidden;

      .more-li {
        width: 100%;
        height: 32px;
        line-height: 32px;
        padding-left: 19px;
        box-sizing: border-box;
        color: #222222;
        cursor: pointer;
        &:hover {
          color: $color-theme;
          background: #FFEBE3;
        }
      }
    }
    .program-wrapper {
      height: calc(100% - 50px);
      box-sizing: border-box;
    }
    .program-ul {
      height: calc(100% - 46px);
      overflow-y: auto;
      overflow-x: hidden;
      box-sizing: border-box;
      padding-left: 2px;
      .program-li {
        position: relative;
        display: flex;
        justify-content: flex-start;
        margin: 2px 0;
        padding: 6px 0;
        background: #292E37;
        box-sizing: border-box;
        height: 90px;
        &.program-complete {
          background-color: #23272F;
          color: #444C5A;
        }
        &.active-living {
          background-color: #D01616;
          color: #ffffff;
          .program-bgm {
            color: #fff!important;
          }
          .my-operate-btn {
            background: rgba(255, 255, 255, .5);
            border: none;
            color: #ffffff;
            &:hover {
              background: $color-theme;
            }
            &:active {
              opacity: .8;
            }
          }
          &:hover {
            background-color: #D01616;
          }
        }
        &.active-next {
          background-color: #17A30C;
          color: #ffffff;
          .program-bgm {
            color: #fff!important;
          }
          .my-operate-btn {
            background: rgba(255, 255, 255, .5);
            border: none;
            color: #ffffff;
            &:hover {
              background: $color-theme;
            }
            &:active {
              opacity: .8;
            }
          }
          &:hover {
            background-color: #17A30C;
          }
        }
        &:hover {
          background: #3E4252;
          .drag-icon {
            display: inline-block;
          }
        }
        .status-flag {
          position: absolute;
          left: 0;
          top: 0;
          width: 88px;
          height: 28px;
          line-height: 28px;
          color: #ffffff;
          background: linear-gradient(-45deg,transparent 28px,rgba(255, 255, 255, .3) 0);
        }
        .drag-icon {
          position: absolute;
          display: none;
          padding: 0 10px;
          cursor: pointer;
          left: 0;
          top: calc(50% - 3px);
          .icon {
            flex: 0 0 16px;
            width: 16px;
            height: 6px;
            border-top: 2px solid #A6B2CE;
            border-bottom: 2px solid #A6B2CE;
          }
        }
        .el-icon-video-camera-solid {
          position: absolute;
          display: none;
          padding: 0 10px;
          cursor: pointer;
          left: 0;
          top: calc(50% - 3px);
          font-size: 22px;
          margin: 0 8px;
          color: $color-theme;
        }
      }
    }
    .list-header {
      display: flex;
      justify-content: flex-start;
      align-items: center;
      color: #646D80;
      font-size: 16px;
      box-sizing: border-box;
      height: 46px;
    }
    .program-option {
      display: none;
      justify-content: center;
      align-items: center;
      flex-wrap: wrap;
      flex: 0 0 22%;
      width: 22%;
      white-space: nowrap;
      &.show-option {
        display: flex;
      }
      .my-operate-btn {
        display: inline-block;
        margin-right: 6px;
        width: 56px;
      }
      .my-operate-btn-span {
        background: #4f5867;
        border-color: #4f5867;
        color: #C2D1EE;
        cursor: pointer;
        text-align: center;
        line-height: 28px;
        &:hover {
          background: $color-theme;
          border-color: $color-theme;
          color: #ffffff;
        }
      }
    }
    .program-options {
      display: flex;
      align-items: center;
      justify-content: center;
      flex: 0 0 22%;
      width: 22%;
      white-space: nowrap;
    }
    .program-bgm {
      display: flex;
      align-items: center;
      flex: 0 0 5%;
      width: 5%;
      white-space: nowrap;
    }
    .program-startTime {
      display: flex;
      justify-content: center;
      flex-direction: column;
      text-indent: 25px;
      flex: 0 0 22%;
      width: 22%;
      white-space: nowrap;
    }
    .program-name {
      display: flex;
      align-items: center;
      text-indent: 20px;
      flex: 0 0 35%;
      width: 35%;
      padding-right: 40px;
      box-sizing: border-box;
      .name {
        display: inline-block;
        width: 200px;
        @include elliptical();
      }
    }
    .program-duration {
      display: flex;
      align-items: center;
      flex: 0 0 16%;
      width: 16%;
    }
  }
  .box-content {
    max-height: 600px;
    overflow-y: auto;

    .bgm-info-box {
      display: flex;
      justify-content: flex-start;
      align-items: center;
      color: #B5BBCA;
      margin-bottom: 10px;
    }
    .bgm-info-item {
      font-size: 22px;
      display: inline-block;
      margin-right: 50px;
      width: 300px;
      @include elliptical();
    }
  }
  .program-dialog {
    .live-type-tips {
      color: $color-tips;
    }
  }
  ::v-deep .el-table td.el-table__cell {
    border-bottom: 1px solid #434A58;
  }
  .menu-enter-active{
    transition: opacity .5s;
  }
  .menu-enter{
    opacity: 0;
  }
  .tips {
    position: absolute;
    bottom: 25px;
    color: #8892A7;
  }
  .dropdown-line {
    display: inline-block;
    height: 9px;
    width: 100%;
    border-top: 1px solid #E6E6E6;
  }
</style>
