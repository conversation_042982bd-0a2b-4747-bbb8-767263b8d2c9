<template>
  <div>
    <el-dialog class="program-dialog" :title="isAdd ? '新增节目' : '编辑节目'" :visible.sync="showDialog" width="665px" :close-on-click-modal="false" :before-close="beforeClose" @close="close()" append-to-body>
      <span class="g-option-btn-2" style="width: 144px;margin-bottom: 22px" @click="showSelectDialog = true" v-show="![4].includes(addType) && !isLiving">
        <i class="el-icon-document-add"></i>
        从节目管理导入
      </span>
      <el-form :model="programForm" :rules="programRules" ref="programForm" label-width="92px">
        <el-form-item label="节目名称：" prop="name">
          <el-input v-model.trim="programForm.name" :maxlength="255" :placeholder="'请输入节目名称'" size="small" :disabled="isLiving"></el-input>
        </el-form-item>
        <el-form-item label="节目类型：" prop="timeType" v-if="[3].includes(addType)">
          <el-radio-group v-model="programForm.timeType">
            <el-radio :label="1">日播</el-radio>
            <el-radio :label="2">周播</el-radio>
            <el-radio :label="3">指定时间</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="时间选择：" required v-if="[3].includes(addType) && programForm.timeType === 2">
          <el-checkbox-group v-model="dateCheckList">
            <el-checkbox :label="1">周一</el-checkbox>
            <el-checkbox :label="2">周二</el-checkbox>
            <el-checkbox :label="3">周三</el-checkbox>
            <el-checkbox :label="4">周四</el-checkbox>
            <el-checkbox :label="5">周五</el-checkbox>
            <el-checkbox :label="6">周六</el-checkbox>
            <el-checkbox :label="7">周日</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item label="播出时间：" required v-if="[2].includes(addType)">
          <el-time-picker
            :disabled="isLiving"
            v-model="programForm.startTime"
            size="small"
            placeholder="播出时间" style="width: 165px;">
          </el-time-picker>
        </el-form-item>
        <el-form-item label="播出时间：" required v-if="[3].includes(addType)">
          <el-time-picker v-if="[1, 2].includes(programForm.timeType)"
            v-model="programForm.startTime"
            size="small"
            placeholder="播出时间" style="width: 165px;">
          </el-time-picker>
          <el-date-picker v-model="programForm.startTime" type="datetime" placeholder="选择日期时间" value-format="timestamp" size="small" style="width: 218px" v-else></el-date-picker>
        </el-form-item>
        <el-form-item label="节目时长：">
          <el-input-number style="width: 110px;" :min="0" v-model.trim="programForm.hour" size="small"></el-input-number>
          <span class="duration-tips" style="color: #A8B7D2;margin: 0 8px">小时</span>
          <el-input-number style="width: 110px;" :min="0" :max="60" v-model.trim="programForm.minute" size="small"></el-input-number>
          <span class="duration-tips" style="color: #A8B7D2;margin: 0 8px">分钟</span>
          <el-input-number style="width: 110px;" :min="0" :max="60" v-model.trim="programForm.second" size="small"></el-input-number>
          <span class="duration-tips" style="color: #A8B7D2;margin: 0 8px">秒</span>
        </el-form-item>
        <el-form-item label="结束时间：" v-if="[2, 3].includes(addType)">
          <span class="end-tag">{{endTime ? endTime : ''}}</span>
        </el-form-item>
        <el-form-item label="节目源：" prop="type">
          <el-radio-group v-model="programForm.type" :disabled="isLiving" @change="programForm.url = ''">
            <el-radio label="live" v-if="[5].includes(addType)">直播</el-radio>
            <el-radio label="stream">拉流地址</el-radio>
            <el-radio label="record">点播源</el-radio>
            <el-radio label="ndi">NDI输入</el-radio>
            <el-radio label="local" :disabled="addType !== 4">本地文件
              <el-tooltip effect="light" content="本地文件只支持从节目管理中导入" placement="top" v-if="addType !== 4">
                <span class="el-icon-question"></span>
              </el-tooltip>
            </el-radio>
            <el-radio label="pic">图片源</el-radio>
            <el-radio label="html">网页</el-radio>
          </el-radio-group>
          <div v-show="programForm.type === 'live'">
            选择直播
          </div>
          <div class="input-box" v-show="programForm.type === 'ndi'">
            <span class="g-option-btn-2" style="width: 84px" @click="showNdiPicker" v-if="!programForm.url">选择</span>
            <div class="text-box" v-else>
              <span class="text">{{ programForm.url }}</span>
              <span class="my-grey-btn" slot="trigger" @click="programForm.url = ''">
                <i class="el-icon-delete"></i>
                删除
              </span>
            </div>
          </div>
          <div class="input-box" v-show="programForm.type === 'local'">
            <div class="text-box">
              <span class="text">{{programForm.localFileName || '还未添加本地文件'}}</span>
              <el-upload
                      v-show="addType === 4 && !programForm.localFileName"
                      :action="actionUrl"
                      :before-upload="beforeUpload"
                      :on-success="handleSuccess"
                      :on-error="handleError"
                      :show-file-list="false">
                <span class="my-grey-btn" slot="trigger" v-show="addType === 4">
                  <i class="el-icon-upload2"></i>
                  上传
                </span>
              </el-upload>
              <span class="my-grey-btn" slot="trigger" v-show="addType === 4 && programForm.localFileName" @click="deleteLocalFile">
                <i class="el-icon-delete"></i>
                删除
              </span>
            </div>
          </div>
          <p class="live-type-tips" v-if="programForm.type === 'stream'">支持RTMP、RTSP、HLS、HTTP、HTTPS协议、SRT协议</p>
          <p class="live-type-tips" v-else-if="programForm.type === 'record'">支持FLV、MP4、TS格式</p>
          <p class="live-type-tips" v-else-if="programForm.type === 'pic'">支持jpg、jpeg、png格式</p>
          <p class="live-type-tips" v-else-if="programForm.type === 'html'">请输入https链接，避免被网络劫持导致页面出现广告</p>
          <p class="live-type-tips" v-else-if="!['live', 'ndi'].includes(programForm.type)">视频文件仅支持mp4格式，图片文件支持jpg、jpeg、png格式</p>
        </el-form-item>
        <el-form-item :label="urlLabel + '：'" prop="url" v-if="!['live', 'local', 'ndi'].includes(programForm.type)">
          <el-input v-model.trim="programForm.url" :placeholder="'请输入'+urlLabel+'地址'" size="small" :disabled="isLiving"></el-input>
        </el-form-item>
        <el-form-item label="源流声音：">
          <el-switch v-model="programForm.sourceAudioOpen" active-text="" inactive-text="" active-color="#FF5B1F" inactive-color="#BCC9E8"></el-switch>
        </el-form-item>
        <el-form-item label="背景音乐：">
          <el-switch v-model="programForm.audioOpen" active-text="" inactive-text="" active-color="#FF5B1F" inactive-color="#BCC9E8"></el-switch>
          <span class="live-type-tips" style="margin-left: 10px">关闭后，统一设置的背景音乐将不在当前节目时间段内播出</span>
        </el-form-item>
        <el-form-item label="背景图片：" v-if="![5].includes(addType)">
          <el-switch v-model="programForm.bgFlag" active-text="" inactive-text="" active-color="#FF5B1F" inactive-color="#BCC9E8"></el-switch>
          <span class="live-type-tips" style="margin-left: 10px">开启后，统一设置的背景图将应用到当前节目</span>
        </el-form-item>
        <el-form-item label="排列序号" v-if="addType === 4">
          <el-input-number style="width: 140px" size="small" :min="1" :max="200" v-model.trim="programForm.orderNum" :controls="false" placeholder="请输入排列序号" class="orderInput"></el-input-number>
          <span class="live-type-tips" style="margin-left: 10px">tips：请输入1-200间的整数</span>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <span class="g-option-btn-3" style="width: 84px;" v-if="uploadFlag">上传中</span>
        <span class="g-option-btn-1" style="width: 84px" @click="confirmProgram('programForm')" v-else>确定</span>
        <span class="g-option-btn-2" style="width: 84px" @click="close()">取消</span>
      </span>
    </el-dialog>
    <select-program v-if="showSelectDialog" @close="showSelectDialog = false" @selectConfirm="selectProgramConfirm"></select-program>
    <ndi-picker @confirm="ndiPicked" @close="ndiPickerShow = false" v-if="ndiPickerShow" />
  </div>
</template>

<script>
import moment, { duration } from 'moment'
import cloudApi from 'api/cloud'
import selectProgram from '@/components/programManage/select-program'
import ndiPicker from './ndi-picker'
import {mapActions, mapGetters, mapMutations} from 'vuex'
import Upload from 'common/js/upload'
import liveApi from 'api/live'

export default {
  props: {
    value: {
      type: String
    },
    slotId: {
      type: String,
      default: ''
    },
    calendarItem: {
      type: Object,
      default: null
    },
    currentItem: {
      type: Object,
      default: () => ({})
    },
    activeItem: {
      type: Object,
      default: () => ({})
    },
    addType: {
      type: Number,
      default: 1 //  1: 循环节目 2: 今日节目单 3: 节目编排 4: 节目管理 5：节目单管理
    }
  },
  computed: {
    isLiving () {
      if (![1, 2].includes(this.addType)) {
        return false
      }
      if (this.programForm.id) {
        return this.value === this.programForm.id
      }
      return false
    },
    // todayPickerOptions () {
    //   return {
    //     selectableRange: moment().format('HH:mm:ss') + '-23:59:59'
    //   }
    // },
    isAdd () {
      return this.programForm.id === 0
    },
    urlLabel () {
      if (this.programForm.type === 'stream') {
        return '拉流'
      } else if (this.programForm.type === 'record') {
        return '点播视频'
      } else if (this.programForm.type === 'html') {
        return '网页'
      } else {
        return '图片源'
      }
    },
    getTime () {
      return this.programForm.hour * 60 * 60 + this.programForm.minute * 60 + this.programForm.second
    },
    endTime () {
      if (!this.programForm.startTime) {
        return 0
      }
      let endTime = moment(this.programForm.startTime).valueOf() + this.getTime * 1000
      if (this.programForm.addType === 2) {
        return moment(endTime).format('HH:mm:ss')
      }
      if (this.programForm.timeType !== 3) {
        return moment(endTime).format('HH:mm:ss')
      }
      return moment(endTime).format('YYYY-MM-DD HH:mm:ss')
    },
    ...mapGetters([
      'localTimeDifference',
      'programsSn',
      'instanceId',
      'todayPrograms'
    ])
  },
  data () {
    return {
      ndiPickerShow: false,
      actionUrl: '/cloud/fullTimeLive/local/upload?instanceId=',
      showSelectDialog: false,
      showDialog: true,
      dateCheckList: [1],
      programForm: {
        id: 0,
        name: '',
        timeType: 1, // 1: 日播 2: 周播 3: 指定时间
        startTime: '', // 播出时间，本地时间，格式为"2006-01-02 15:04:05"，仅节目表第一个节目有效
        localFileName: '', // 本地上传的文件名称
        hour: 0, // 节目小时
        minute: 0, // 节目分钟
        second: 0, // 节目秒
        type: 'stream', // "stream"：直播；"record"：录播
        url: '',
        cgIds: [],
        audios: [],
        audioOpen: true,
        localFileSize: 0, // 节目管理编辑节目时原来本地文件大小
        orderNum: undefined, // 节目管理列表排序
        bgFlag: false,
        sourceAudioOpen: true
      },
      uploadUrl: '',
      uploadSuccess: false,
      programRules: {
        name: [
          {required: true, message: '请输入节目名称', trigger: 'blur'}
        ]
      },
      localFileSize: 0,
      uploadFlag: false,
      extraInfo: null // 节目来源是节目管理中来，储存关联音频和关联字幕画中画等信息
    }
  },
  components: {selectProgram, ndiPicker},
  methods: {
    showNdiPicker() {
      this.ndiPickerShow = true
    },
    ndiPicked(item) { 
      this.programForm.url = item
    },
    close () {
      if (this.uploadFlag) {
        this.$message.warning('本地文件正在上传中，请勿关闭弹框')
        return
      }
      let that = this
      if (this.uploadUrl && this.uploadSuccess) {
        this.deleteFile([this.uploadUrl], () => {
          that.$emit('close')
        })
      } else {
        this.$emit('close')
      }
    },
    beforeClose (done) {
      if (this.uploadFlag) {
        this.$message.warning('本地文件正在上传中，请勿关闭弹框')
        return false
      } else {
        return done(true)
      }
    },
    deleteFile (pathList, callback) {
      cloudApi.deleteFile({paths: pathList}, () => {
        callback()
        this.getFileStorage()
      }, (code, msg) => {})
    },
    deleteLocalFile () {
      let that = this
      let tip = '删除文件后将无法恢复，确定删除吗?'
      if (!this.isAdd && !this.slotId) {
        tip = '删除文件后将无法恢复，如果删除会导致部分节目不可用，确定删除吗?'
      }
      this.$confirm(tip, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // that.deleteFile([this.uploadUrl], () => {
        //   that.localFileSize = 0
        //   that.programForm.localFileName = ''
        //   that.programForm.localFileSize = 0
        //   that.uploadUrl = ''
        // })
        that.localFileSize = 0
        that.programForm.localFileName = ''
        that.programForm.localFileSize = 0
        that.uploadUrl = ''
      })
    },
    handleSuccess (res, file) {
      this.programForm.localFileName = file.name
      this.uploadUrl = res.value.path
      this.uploadSuccess = true
      this.localFileSize = file.size
      this.uploadFlag = false
      this.getFileStorage()
    },
    handleError () {
      this.$message.error('上传失败')
    },
    async beforeUpload (file) {
      let checkStorage = await this.getFileStoragePromise(file)
      if (!checkStorage) {
        this.$message.warning('存储不足，无法上传')
        reject()
      }
      let fileType = file.name.substring(file.name.lastIndexOf('.') + 1, file.name.length)
      let uploadType = Upload.getUploadType(fileType)
      if (!['mp4', 'jpg', 'jpeg', 'png'].includes(fileType.toLowerCase())) {
        this.$message.error('请上传正确的格式文件')
        reject()
      }
      var errorMsg = Upload.checkFileSize(uploadType, file.size)
      if (errorMsg !== '') {
        this.$message.error(errorMsg)
        reject()
      }
      this.uploadFlag = true
    },
    getFileStoragePromise (file) {
      return new Promise(resolve => {
        cloudApi.getInstanceInfo({}, res => {
          if (file.size + res.currentVolume > res.maxVolume) {
            resolve(false)
          }
          resolve(true)
        }, () => {
          resolve(false)
        })
      })
    },
    selectProgramConfirm (item) { // 从节目管理导入
      if (this.addType === 1) {
        this.setData1(item.program, true)
      }
      if (this.addType === 2) {
        this.setData2(item.program, true)
      }
      if (this.addType === 3) {
        this.setData3(item, true)
      }
      if (this.addType === 5) {
        this.setData5(item.program, true)
      }
    },
    setData1 (currentItem, fromManage) { // 设置循环节目单数据
      let programItem = currentItem
      let masterChannel = programItem.video.channels.find(v => v.idx === 0)
      if (!fromManage) {
        this.programForm.id = currentItem.id
      } else {
        this.extraInfo = {
          cgIds: programItem.cgIds,
          audios: programItem.audios,
          video: programItem.video
        }
      }
      this.programForm.name = programItem.name
      this.programForm.hour = Math.floor(programItem.duration / (60 * 60))
      this.programForm.minute = Math.floor((programItem.duration % (60 * 60)) / 60)
      this.programForm.second = Math.floor(programItem.duration % 60)
      this.programForm.type = masterChannel.type
      this.programForm.url = masterChannel.url
      if (this.programForm.type === 'local') {
        this.programForm.localFileName = masterChannel.localFileName
        this.uploadUrl = masterChannel.url
        this.programForm.url = ''
      }
      this.programForm.audioOpen = !programItem.globalBgmMute
      this.programForm.sourceAudioOpen = !programItem.pgmMute
      this.programForm.bgFlag = programItem.video.useBackground
    },
    setData2 (currentItem, fromManage) { // 设置今日节目单数据
      let programItem = currentItem
      let masterChannel = programItem.video.channels.find(v => v.idx === 0)
      if (!fromManage) {
        this.programForm.id = currentItem.id
        this.programForm.startTime = moment(programItem.startTime).format('YYYY-MM-DD HH:mm:ss')
      } else {
        this.extraInfo = {
          cgIds: programItem.cgIds,
          audios: programItem.audios,
          video: programItem.video
        }
      }
      this.programForm.name = programItem.name
      this.programForm.hour = Math.floor(programItem.duration / (60 * 60))
      this.programForm.minute = Math.floor((programItem.duration % (60 * 60)) / 60)
      this.programForm.second = Math.floor(programItem.duration % 60)
      this.programForm.type = masterChannel.type
      this.programForm.url = masterChannel.url
      if (this.programForm.type === 'local') {
        this.programForm.localFileName = masterChannel.localFileName
        this.uploadUrl = masterChannel.url
        this.programForm.url = ''
      }
      this.programForm.audioOpen = !programItem.globalBgmMute
      this.programForm.sourceAudioOpen = !programItem.pgmMute
      this.programForm.bgFlag = programItem.video.useBackground
    },
    setData3 (currentItem, fromManage) { // 设置节目编排数据
      let programItem = currentItem.program
      let masterChannel = programItem.video.channels.find(v => v.idx === 0)
      if (!fromManage) {
        this.programForm.id = currentItem.id
      } else {
        this.extraInfo = {
          cgIds: programItem.cgIds,
          audios: programItem.audios,
          video: programItem.video
        }
      }
      this.programForm.name = programItem.name
      if (currentItem.type) {
        this.programForm.timeType = currentItem.type
        this.dateCheckList = currentItem.daysOfWeek || []
        if (currentItem.day) {
          this.programForm.startTime = moment(currentItem.day + ' ' + currentItem.time).valueOf()
        } else {
          this.programForm.startTime = moment(moment(new Date().getTime() + this.localTimeDifference).format('YYYY-MM-DD') + ' ' + currentItem.time).valueOf()
        }
      }
      this.programForm.hour = Math.floor(programItem.duration / (60 * 60))
      this.programForm.minute = Math.floor((programItem.duration % (60 * 60)) / 60)
      this.programForm.second = Math.floor(programItem.duration % 60)
      this.programForm.type = masterChannel.type
      this.programForm.url = masterChannel.url
      if (this.programForm.type === 'local') {
        this.programForm.localFileName = masterChannel.localFileName
        this.uploadUrl = masterChannel.url
        this.programForm.url = ''
      }
      this.programForm.audioOpen = !programItem.globalBgmMute
      this.programForm.sourceAudioOpen = !programItem.pgmMute
      this.programForm.bgFlag = programItem.video.useBackground
    },
    setData4 (currentItem, fromManage) { // 设置节目管理数据 duration / 1000 为秒
      let programItem = currentItem.program
      let masterChannel = programItem.video.channels.find(v => v.idx === 0)
      if (!fromManage) {
        this.programForm.id = currentItem.id
      }
      this.programForm.localFileSize = currentItem.fileSize
      this.programForm.name = programItem.name
      this.programForm.hour = Math.floor(programItem.duration / 1000 / (60 * 60))
      this.programForm.minute = Math.floor((programItem.duration / 1000 % (60 * 60)) / 60)
      this.programForm.second = Math.floor(programItem.duration / 1000 % 60)
      this.programForm.type = masterChannel.type
      this.programForm.url = masterChannel.url
      if (this.programForm.type === 'local') {
        this.programForm.localFileName = masterChannel.localFileName
        this.uploadUrl = masterChannel.url
        this.programForm.url = ''
      }
      this.programForm.audioOpen = !programItem.globalBgmMute
      this.programForm.sourceAudioOpen = !programItem.pgmMute
      this.programForm.bgFlag = programItem.video.useBackground
      this.programForm.orderNum = currentItem.orderNum > 200 ? undefined : currentItem.orderNum
    },
    setData5 (currentItem, fromManage) { // 设置节目单管理的节目
      let programItem = currentItem
      let masterChannel = programItem.video.channels.find(v => v.idx === 0)
      if (!fromManage) {
        this.programForm.id = this.activeItem.id
      }
      this.programForm.name = programItem.name
      this.programForm.hour = Math.floor(programItem.duration / (60 * 60))
      this.programForm.minute = Math.floor((programItem.duration % (60 * 60)) / 60)
      this.programForm.second = Math.floor(programItem.duration % 60)
      this.programForm.type = masterChannel.type
      this.programForm.url = masterChannel.url
      if (this.programForm.type === 'local') {
        this.programForm.localFileName = masterChannel.localFileName
        this.uploadUrl = masterChannel.url
        this.programForm.url = ''
      }
      this.programForm.audioOpen = !programItem.globalBgmMute
      this.programForm.sourceAudioOpen = !programItem.pgmMute
    },
    confirm1 (formName) { // 提交循环节目单
      const programType = 'loop'
      if (!this.getTime) {
        this.$message.warning('请选择时长')
        return
      }
      if (this.programForm.type === 'local' && !this.programForm.localFileName) {
        this.$message.warning('请添加本地文件')
        return
      }
      if (!this.programForm.url && this.programForm.type !== 'local') {
        this.$message.warning('请输入节目源')
        return
      }
      this.$refs[formName].validate((valid) => {
        if (valid) {
          if (this.slotId) return this.slotPrograms1()
          if (this.isAdd) return this.addPrograms1({programType: programType})
          return this.editPrograms1({programType: programType})
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    confirm2 (formName) { // 提交今日节目单
      const programType = 'today'
      if (!this.getTime) {
        this.$message.warning('请选择时长')
        return
      }
      if (!this.programForm.startTime) {
        this.$message.warning('请选择播出时间')
        return
      }
      if (this.isAdd && moment(moment(new Date().getTime() + this.localTimeDifference).format('YYYY-MM-DD') + ' ' + moment(this.programForm.startTime).format('HH:mm:ss')).valueOf() <= new Date().getTime() + this.localTimeDifference) {
        this.$message.warning('播出时间不得早于此刻')
        return
      }
      if (this.programForm.type === 'local' && !this.programForm.localFileName) {
        this.$message.warning('请添加本地文件')
        return
      }
      if (!this.programForm.url && this.programForm.type !== 'local') {
        this.$message.warning('请输入节目源')
        return
      }
      let checkFlag = this.checkTodayProgram()
      if (checkFlag) {
        this.$message.warning('当前时间段已有节目')
        return
      }
      this.$refs[formName].validate((valid) => {
        if (valid) {
          if (this.slotId) return this.slotPrograms2()
          if (this.isAdd) return this.addPrograms2({programType: programType})
          return this.editPrograms2({programType: programType})
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    confirm3 (formName) { // 提交节目编排
      if (this.programForm.timeType === 2 && !this.dateCheckList.length) {
        this.$message.warning('周播时间至少选择一天')
        return
      }
      if (!this.programForm.startTime) {
        this.$message.warning('请选择播出时间')
        return
      }
      if (!this.getTime) {
        this.$message.warning('请选择时长')
        return
      }
      if (this.programForm.type === 'local' && !this.programForm.localFileName) {
        this.$message.warning('请添加本地文件')
        return
      }
      if (!this.programForm.url && this.programForm.type !== 'local') {
        this.$message.warning('请输入节目源')
        return
      }
      this.$refs[formName].validate((valid) => {
        if (valid) {
          if (this.isAdd) return this.addPrograms3()
          return this.editPrograms3()
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    confirm4 (formName) { // 提交节目管理
      if (!this.getTime) {
        this.$message.warning('请选择时长')
        return
      }
      if (this.programForm.type === 'local' && !this.programForm.localFileName) {
        this.$message.warning('请添加本地文件')
        return
      }
      if (!this.programForm.url && this.programForm.type !== 'local') {
        this.$message.warning('请输入节目源')
        return
      }
      if (this.programForm.orderNum && (this.programForm.orderNum > 200 || this.programForm.orderNum < 1)) {
        this.$message.warning('排列序号范围为1-200之间')
        return
      }
      this.$refs[formName].validate((valid) => {
        if (valid) {
          if (this.isAdd) return this.addPrograms4()
          return this.editPrograms4()
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    confirm5 (formName) { // 提交节目单管理的节目
      if (!this.getTime) {
        this.$message.warning('请选择时长')
        return
      }
      if (this.programForm.type === 'local' && !this.programForm.localFileName) {
        this.$message.warning('请添加本地文件')
        return
      }
      if (!this.programForm.url && !['live', 'local'].includes(this.programForm.type)) {
        this.$message.warning('请输入节目源')
        return
      }
      this.$refs[formName].validate((valid) => {
        if (valid) {
          if (this.isAdd) return this.addPrograms5()
          return this.editPrograms5()
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    slotPrograms1 () {
      this.addPrograms1({slotId: this.slotId, programType: 'loop'})
    },
    slotPrograms2 () {
      this.addPrograms1({slotId: this.slotId, programType: 'today'})
    },
    editPrograms1 ({programType}) { // 编辑循环节目
      let editObj = {
        name: this.programForm.name,
        duration: this.getTime,
        globalBgmMute: !this.programForm.audioOpen,
        pgmMute: !this.programForm.sourceAudioOpen,
        type: programType
      }
      if (this.extraInfo) {
        editObj = Object.assign({}, editObj, this.extraInfo)
      }
      let editProgram = Object.assign({}, this.currentItem, editObj)
      let masterIndex = editProgram.video.channels.findIndex(item => item.idx === 0)
      editProgram.video.channels[masterIndex].type = this.programForm.type
      editProgram.video.channels[masterIndex].url = this.programForm.type === 'local' ? this.uploadUrl : this.programForm.url
      editProgram.video.useBackground = this.programForm.bgFlag
      let param = {
        programType: programType,
        program: editProgram
      }
      this.modProgramApi(param)
    },
    editPrograms2 ({programType}) { // 编辑今日节目
      let editObj = {
        name: this.programForm.name,
        duration: this.getTime,
        startTime: moment(new Date().getTime() + this.localTimeDifference).format('YYYY-MM-DD') + ' ' + moment(this.programForm.startTime).format('HH:mm:ss'),
        globalBgmMute: !this.programForm.audioOpen,
        pgmMute: !this.programForm.sourceAudioOpen,
        type: programType
      }
      if (this.extraInfo) {
        editObj = Object.assign({}, editObj, this.extraInfo)
      }
      let editProgram = Object.assign({}, this.currentItem, editObj)
      let masterIndex = editProgram.video.channels.findIndex(item => item.idx === 0)
      editProgram.video.channels[masterIndex].type = this.programForm.type
      editProgram.video.channels[masterIndex].url = this.programForm.type === 'local' ? this.uploadUrl : this.programForm.url
      editProgram.video.useBackground = this.programForm.bgFlag
      let param = {
        programType: programType,
        program: editProgram
      }
      this.modProgramApi(param)
    },
    modProgramApi (param) {
      param.programsSn = this.programsSn
      liveApi.modProgram(param, res => {
        this.setProgramSn(res.programsSn)
        this.getPrograms()
        this.$emit('close')
      }, (code, msg, value) => {
        if (code === -60004) {
          this.setProgramSn(value.programsSn)
          this.modProgramApi(param)
          return
        }
        this.$message.error(msg)
      })
    },
    editPrograms3 () { // 编辑节目编排
      let editObj = {
        name: this.programForm.name,
        duration: this.getTime,
        type: 'today',
        globalBgmMute: !this.programForm.audioOpen,
        pgmMute: !this.programForm.sourceAudioOpen
      }
      if (this.extraInfo) {
        editObj = Object.assign({}, editObj, this.extraInfo)
      }
      let editProgram = Object.assign({}, this.currentItem.program, editObj)
      let masterIndex = editProgram.video.channels.findIndex(item => item.idx === 0)
      editProgram.video.channels[masterIndex].type = this.programForm.type
      editProgram.video.channels[masterIndex].url = this.programForm.type === 'local' ? this.uploadUrl : this.programForm.url
      editProgram.video.useBackground = this.programForm.bgFlag
      let param = {
        id: this.programForm.id,
        name: this.programForm.name,
        type: this.programForm.timeType,
        daysOfWeek: this.programForm.timeType === 2 ? this.dateCheckList : null,
        duration: this.getTime,
        program: JSON.stringify(editProgram)
      }
      if (this.programForm.timeType === 3) {
        param.day = moment(this.programForm.startTime).format('YYYY-MM-DD')
        param.time = moment(this.programForm.startTime).format('HH:mm:ss')
      } else {
        param.day = ''
        param.time = moment(this.programForm.startTime).format('HH:mm:ss')
      }
      cloudApi.uploadProgramArrangement(param, () => {
        this.$emit('refresh')
        this.$emit('close')
      }, (code, msg) => {
        this.$message.error(msg)
      })
    },
    editPrograms4 () { // 编辑节目管理
      let editProgram = Object.assign({}, this.currentItem.program, {
        name: this.programForm.name,
        duration: this.getTime * 1000,
        type: 'loop',
        globalBgmMute: !this.programForm.audioOpen,
        pgmMute: !this.programForm.sourceAudioOpen
      })
      let masterIndex = editProgram.video.channels.findIndex(item => item.idx === 0)
      editProgram.video.channels[masterIndex].type = this.programForm.type
      editProgram.video.channels[masterIndex].url = this.programForm.type === 'local' ? this.uploadUrl : this.programForm.url
      editProgram.video.channels[masterIndex].localFileName = this.programForm.type === 'local' ? this.programForm.localFileName : ''
      editProgram.video.useBackground = this.programForm.bgFlag
      let param = {
        id: this.programForm.id,
        program: JSON.stringify(editProgram)
      }
      if (this.programForm.type === 'local' && this.programForm.localFileName) {
        param.fileSize = this.localFileSize > 0 ? this.localFileSize : this.programForm.localFileSize
      } else {
        param.fileSize = 0
      }
      if (this.programForm.orderNum) {
        param.orderNum = this.programForm.orderNum
      }
      cloudApi.editProgram(param, () => {
        this.$emit('refresh')
        this.$emit('close')
      }, (code, msg) => {
        this.$message.error(msg)
      })
    },
    editPrograms5 () { // 编辑节目单管理的节目
      const schedule = {
        id: this.activeItem.id,
        name: this.activeItem.name,
        enable: this.activeItem.enable,
        isDefault: this.activeItem.isDefault,
        programs: this.activeItem.programs,
        scheduleConfig: this.activeItem.scheduleConfig
      }
      const program = {
        id: this.currentItem.id,
        type: 'loop',
        name: this.programForm.name,
        // isCompleted: false,
        globalBgmMute: !this.programForm.audioOpen,
        pgmMute: !this.programForm.sourceAudioOpen,
        // startTime: moment(this.activeItem.startTime).format('YYYY-MM-DD HH:mm:ss'),
        duration: this.getTime,
        video: this.extraInfo && this.extraInfo.video ? this.extraInfo.video : {
          useBackground: false,
          usePip: false,
          channels: [
            {
              type: this.programForm.type,
              url: this.programForm.type === 'local' ? this.uploadUrl : this.programForm.url,
              localFileName: this.programForm.type === 'local' ? this.programForm.localFileName : '',
              idx: 0,
              Crop: null,
              display: null
            }
          ]
        }
        // audios: this.extraInfo && this.extraInfo.audios ? this.extraInfo.audios : [],
      }
      const index = schedule.programs.findIndex(item => item.id === program.id)
      if (index >= 0) {
        schedule.programs.splice(index, 1, program)
      }
      liveApi.updPgm(
        {
          id: this.activeItem.id,
          sn: this.programsSn,
          schedule
        },
        () => {
          this.$message.success('操作成功')
          this.getPrograms()
          this.$emit('refresh')
          this.$emit('close')
        }, (code, msg) => {
          this.$message.error(msg)
        }
      )
    },
    addPrograms1 ({slotId, programType}) { // 新增循环节目
      let programsParam = [this.getNewPrograms(programType)]
      let param = {
        programType: programType,
        programs: programsParam,
        after: slotId || null
      }
      this.addProgramApi(param)
    },
    addProgramApi (param) {
      param.programsSn = this.programsSn
      liveApi.addProgram(param, res => {
        this.setProgramSn(res.programsSn)
        this.getPrograms()
        this.$emit('close')
      }, (code, msg, value) => {
        if (code === -60004) {
          this.setProgramSn(value.programsSn)
          this.addProgramApi(param)
          return
        }
        this.$message.error(msg)
      })
    },
    addPrograms2 ({programType}) { // 新增今日节目
      this.addPrograms1({programType: programType})
    },
    addPrograms3 () { // 新增节目编排
      let param = {
        name: this.programForm.name,
        type: this.programForm.timeType,
        daysOfWeek: this.programForm.timeType === 2 ? this.dateCheckList : null,
        duration: this.getTime,
        program: JSON.stringify(this.getNewPrograms('today'))
      }
      if (this.programForm.timeType === 3) {
        param.day = moment(this.programForm.startTime).format('YYYY-MM-DD')
        param.time = moment(this.programForm.startTime).format('HH:mm:ss')
      } else {
        param.day = ''
        param.time = moment(this.programForm.startTime).format('HH:mm:ss')
      }
      cloudApi.addProgramArrangement(param, () => {
        this.$emit('refresh')
        this.$emit('close')
      }, (code, msg) => {
        this.$message.error(msg)
      })
    },
    addPrograms4 () { // 新增节目管理
      let programsParam = []
      let newProgram = {
        program: JSON.stringify(this.getNewPrograms('loop'))
      }
      if (this.programForm.type === 'local') {
        newProgram.fileSize = this.localFileSize
      }
      if (this.programForm.orderNum) {
        newProgram.orderNum = this.programForm.orderNum
      }
      programsParam.push(newProgram)
      let param = {
        programs: programsParam
      }
      cloudApi.addProgram(param, () => {
        this.$emit('refresh')
        this.$emit('close')
      }, (code, msg) => {
        this.$message.error(msg)
      })
    },
    addPrograms5 () { // 新增节目单管理的节目
      const schedule = {
        id: this.activeItem.id,
        name: this.activeItem.name,
        enable: this.activeItem.enable,
        isDefault: this.activeItem.isDefault,
        programs: this.activeItem.programs,
        scheduleConfig: this.activeItem.scheduleConfig
      }
      schedule.programs.push({
          id: this.$_createRandomId(),
          type: 'loop',
          name: this.programForm.name,
          // isCompleted: false,
          globalBgmMute: !this.programForm.audioOpen,
          pgmMute: !this.programForm.sourceAudioOpen,
          // startTime: moment(this.activeItem.startTime).format('YYYY-MM-DD HH:mm:ss'),
          duration: this.getTime,
          video: this.extraInfo && this.extraInfo.video ? this.extraInfo.video : {
            useBackground: false,
            usePip: false,
            channels: [
              {
                type: this.programForm.type,
                url: this.programForm.type === 'local' ? this.uploadUrl : this.programForm.url,
                localFileName: this.programForm.type === 'local' ? this.programForm.localFileName : '',
                idx: 0,
                Crop: null,
                display: null
              }
            ]
          }
          // audios: this.extraInfo && this.extraInfo.audios ? this.extraInfo.audios : [],
        })
      liveApi.updPgm(
        {
          id: this.activeItem.id,
          sn: this.programsSn,
          schedule
        },
        () => {
          this.$message.success('操作成功')
          this.getPrograms()
          this.$emit('refresh')
          this.$emit('close')
        }, (code, msg) => {
          this.$message.error(msg)
        }
      )
    },
    getNewPrograms (programType) {
      return {
        id: this.$_createRandomId(),
        name: this.programForm.name,
        startTime: this.programForm.timeType === 3 ? moment(this.programForm.startTime).format('YYYY-MM-DD HH:mm:ss') : moment(new Date().getTime() + this.localTimeDifference).format('YYYY-MM-DD') + ' ' + moment(this.programForm.startTime).format('HH:mm:ss'),
        duration: this.addType === 4 ? this.getTime * 1000 : this.getTime,
        cgIds: this.extraInfo && this.extraInfo.cgIds ? this.extraInfo.cgIds : [],
        audios: this.extraInfo && this.extraInfo.audios ? this.extraInfo.audios : [],
        type: programType,
        globalBgmMute: !this.programForm.audioOpen,
        pgmMute: !this.programForm.sourceAudioOpen,
        video: this.extraInfo && this.extraInfo.video ? this.extraInfo.video : {
          useBackground: this.programForm.bgFlag,
          channels: [
            {
              type: this.programForm.type,
              url: this.programForm.type === 'local' ? this.uploadUrl : this.programForm.url,
              localFileName: this.programForm.type === 'local' ? this.programForm.localFileName : '',
              idx: 0,
              Crop: null,
              display: null
            }
          ]
        }
      }
    },
    checkTodayProgram () {
      let endTime = moment(this.programForm.startTime).valueOf() + this.getTime * 1000
      let startTime = moment(this.programForm.startTime).valueOf()
      let currentItemStartTime
      let currentItemEndTime
      let findItem = this.todayPrograms.find(item => {
        currentItemStartTime = moment(item.startTime).valueOf()
        currentItemEndTime = moment(item.startTime).valueOf() + duration * 1000
        if (startTime > currentItemStartTime && startTime < currentItemEndTime) {
          return true
        }
        if (endTime > currentItemStartTime && endTime < currentItemEndTime) {
          return true
        }
        return false
      })
      return findItem
    },
    setCalendarDate () {
      this.programForm.timeType = 3
      this.programForm.duration = this.calendarItem.duration
      this.programForm.startTime = moment(this.calendarItem.date + ' ' + this.calendarItem.time).valueOf()
      this.programForm.hour = Math.floor(this.calendarItem.duration / (60 * 60))
      this.programForm.minute = Math.floor((this.calendarItem.duration % (60 * 60)) / 60)
      this.programForm.second = Math.floor(this.calendarItem.duration % 60)
    },
    addBefore () {
      if (this.calendarItem) {
        this.setCalendarDate()
      }
    },
    initData () {
      if (!this.currentItem || !this.currentItem.id) {
        this.addBefore()
        return
      }
      if (this.addType === 1) {
        this.setData1(this.currentItem, false)
      }
      if (this.addType === 2) {
        this.setData2(this.currentItem, false)
      }
      if (this.addType === 3) {
        this.setData3(this.currentItem, false)
      }
      if (this.addType === 4) {
        this.setData4(this.currentItem, false)
      }
      if (this.addType === 5) {
        this.setData5(this.currentItem, false)
      }
    },
    confirmProgram (formName) {
      if (this.addType === 1) {
        this.confirm1(formName)
      }
      if (this.addType === 2) {
        this.confirm2(formName)
      }
      if (this.addType === 3) {
        this.confirm3(formName)
      }
      if (this.addType === 4) {
        this.confirm4(formName)
      }
      if (this.addType === 5) {
        this.confirm5(formName)
      }
    },
    ...mapMutations({
      setProgramSn: 'SET_PROGRAMS_SN'
    }),
    ...mapActions(['addProgram', 'modProgram', 'getPrograms', 'getFileStorage'])
  },
  created () {
    this.actionUrl = this.actionUrl + this.instanceId
    this.initData()
  }
}
</script>

<style scoped lang="scss">
  .program-dialog {
    .live-type-tips {
      font-size: 12px;
      color: #7A859C;
      font-family: SimSun;
      line-height: 18px;
    }
  }
  .end-tag {
    font-size: 16px;
    font-family: SimSun;
    color: #AEBBD9;
  }
  ::v-deep .el-dialog__body {
    padding: 25px;
  }
  ::v-deep .el-form-item__label {
    padding-right: 5px;
  }
  ::v-deep .el-form-item {
    margin-bottom: 12px;
  }
  ::v-deep .el-radio {
    margin-right: 32px;
  }
  ::v-deep .el-checkbox {
    margin-right: 17px;
  }
  ::v-deep .el-checkbox__label {
    margin-left: 6px;
    font-size: 14px;
    font-family: SimSun;
    color: #AEBBD9;
  }

  .input-box {
    position: relative;
    width: 296px;
    padding: 10px 12px;
    background-color: #525B6C;
    box-sizing: border-box;
    border-radius: 6px;
    .text-box {
      display: flex;
      justify-content: space-between;
      box-sizing: border-box;
      align-items: center;
    }
    .text {
      display: inline-block;
      font-size: 14px;
      font-family: SimSun;
      color: #AEBBD9;
      font-weight: 400;
      line-height: 40px;
      width: 160px;
      @include elliptical();
    }
    &::after {
      content: "";
      width: 0;
      height: 0;
      position: absolute;
      top: -8px;
      left: 260px;
      border-width: 0 8px 8px;
      border-style: solid;
      border-color: transparent transparent #525B6C;
    }
  }
</style>
