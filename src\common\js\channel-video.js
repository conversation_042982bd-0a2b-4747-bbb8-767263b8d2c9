import liveApi from 'api/live'
import webrtcMgr from 'common/js/webrtc-manager'

class ChannelVideo {
  constructor (videoId, channelName, instanceId) {
    this.channelName = channelName
    this.el = document.getElementById(videoId)
    let pc = new RTCPeerConnection({})
    pc.onconnectionstatechange = event => {
      if (pc.connectionState === 'disconnected') {
        console.log('disconnected')
        setTimeout(() => {
          this.reconnect(instanceId)
        }, 10000)
      }
    }
    pc.onicecandidate = event => {
    }
    pc.addTransceiver('video')
    pc.addTransceiver('audio')
    this.sdpFailCount = 0 // 获取sdp失败次数
    this.connect(pc, instanceId)
    webrtcMgr.registerVideo(pc)
    pc.ontrack = event => {
      this.el.srcObject = event.streams[0]
      this.el.style = 'display: block'
    }
  }
  reconnect (instanceId) {
    webrtcMgr.destroyAllVideo()
    var pc = new RTCPeerConnection({})
    pc.onconnectionstatechange = event => {
      if (pc.connectionState === 'disconnected') {
        console.log('disconnected')
        setTimeout(() => {
          this.reconnect(instanceId)
        }, 10000)
      }
    }
    pc.onicecandidate = event => {
    }
    pc.addTransceiver('video')
    pc.addTransceiver('audio')
    this.sdpFailCount = 0 // 获取sdp失败次数
    this.connect(pc, instanceId)
    webrtcMgr.registerVideo(pc)
    pc.ontrack = event => {
      this.el.srcObject = event.streams[0]
      this.el.style = 'display: block'
    }
  }
  connect (pc, instanceId) {
    pc.createOffer()
      .then(d => {
        pc.setLocalDescription(d, () => {
          this.getSdp(pc, instanceId)
        })
      })
  }

  getSdp (pc, instanceId) {
    liveApi.getChannelVideo(this.channelName, pc.localDescription, (message) => {
      if (message === '' && this.sdpFailCount < 5) {
        this.sdpFailCount++
        setTimeout(() => {
          this.getSdp(pc, instanceId)
        }, 500)
        return
      }
      try {
        pc.setRemoteDescription(new RTCSessionDescription(JSON.parse(atob(message.sdp))))
      } catch (e) {
        console.log(JSON.parse(atob(message.sdp)))
        console.log('setRemoteDescription error:' + e)
      }
    }, () => {}, instanceId)
  }
}

export default ChannelVideo
