<template>
  <el-dialog
    title="新建布局"
    :visible.sync="dialogVisible"
    width="409px"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    custom-class="create-layout-dialog"
    @close="handleClose"
  >
    <div class="dialog-content">
      <div class="ratio-label">布局比例：</div>
      <div class="ratio-options">
        <el-radio-group v-model="selectedRatio" class="ratio-group">
          <el-radio label="16:9" class="ratio-radio">16:9</el-radio>
          <el-radio label="4:3" class="ratio-radio">4:3</el-radio>
          <el-radio label="9:16" class="ratio-radio">9:16</el-radio>
          <el-radio label="4:9" class="ratio-radio">4:9</el-radio>
        </el-radio-group>
      </div>
    </div>

    <div slot="footer" class="dialog-footer">
      <el-button type="primary" @click="handleConfirm" size="small" class="confirm-btn">确认</el-button>
      <el-button @click="handleCancel" size="small" type="info">取消</el-button>
    </div>
  </el-dialog>
</template>

<script>
export default {
  name: 'CreateLayoutDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      selectedRatio: '16:9' // 默认选择16:9
    }
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible
      },
      set(val) {
        this.$emit('update:visible', val)
      }
    }
  },
  methods: {
    handleConfirm() {
      // 发射确认事件，传递选择的比例
      this.$emit('confirm', {
        ratio: this.selectedRatio
      })
      this.handleClose()
    },
    handleCancel() {
      this.handleClose()
    },
    handleClose() {
      this.$emit('update:visible', false)
      this.$emit('close')
      // 重置选择
      this.selectedRatio = '16:9'
    }
  }
}
</script>

<style lang="scss" scoped>
:deep(.create-layout-dialog) {
  .el-dialog {
    background: #4a4a4a;
    border-radius: 8px;
    height: 228px; // 固定弹窗总高度

    .el-dialog__header {
      background: #4a4a4a;
      padding: 12px 16px;
      border-bottom: 1px solid #5a5a5a;
      height: 48px;
      box-sizing: border-box;

      .el-dialog__title {
        color: #ffffff;
        font-size: 16px;
        font-weight: 500;
      }

      .el-dialog__close {
        color: #ffffff;
        font-size: 18px;

        &:hover {
          color: #ff6b35;
        }
      }
    }

    .el-dialog__body {
      background: #4a4a4a;
      padding: 16px 20px;
      height: 120px; // 固定内容区域高度
      box-sizing: border-box;
    }

    .el-dialog__footer {
      background: #4a4a4a;
      padding: 12px 16px;
      border-top: 1px solid #5a5a5a;
      height: 60px;
      box-sizing: border-box;
    }
  }
}

.dialog-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.ratio-label {
  color: #ffffff;
  font-size: 14px;
  font-weight: 500;
}

.ratio-options {
  .ratio-group {
    display: flex;
    gap: 20px;

    .ratio-radio {
      :deep(.el-radio__input) {
        .el-radio__inner {
          background: transparent;
          border-color: #999999;

          &:hover {
            border-color: #ff6b35;
          }
        }

        &.is-checked .el-radio__inner {
          background: #ff6b35;
          border-color: #ff6b35;

          &::after {
            background: #ffffff;
          }
        }
      }

      :deep(.el-radio__label) {
        color: #ffffff;
        font-size: 14px;

        &:hover {
          color: #ff6b35;
        }
      }

      &.is-checked {
        :deep(.el-radio__label) {
          color: #ff6b35;
        }
      }
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;

  .confirm-btn {
    background: #ff6b35;
    border-color: #ff6b35;

    &:hover {
      background: #ff5722;
      border-color: #ff5722;
    }
  }
}
</style>
