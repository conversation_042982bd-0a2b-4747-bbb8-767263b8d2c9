<template>
  <div class="layout-tab">
    <!-- 顶部添加布局按钮 -->
    <div class="header-actions">
      <el-button
        type="primary"
        size="small"
        icon="el-icon-plus"
        @click="handleAddLayout"
        class="add-layout-btn"
      >
        添加布局
      </el-button>
    </div>

    <!-- 布局列表 -->
    <div class="layout-list" v-loading="loading">
      <div
        v-for="layout in filteredLayouts"
        :key="layout.id"
        class="layout-item"
        @click="handleSelectLayout(layout)"
        :class="{ active: currentLayoutId === layout.id }"
      >
        <!-- 图片预览区域 -->
        <div class="image-preview">
          <!-- 比例显示区域 -->
          <div class="ratio-display">
            {{ layout.ratio || '16:9' }}
          </div>
          <div
            v-if="layout.previewImage"
            class="preview-background"
            :style="{ backgroundImage: `url(${layout.previewImage})` }"
          ></div>
          <div v-else class="preview-placeholder">
            <i class="el-icon-picture-outline"></i>
          </div>
        </div>

        <!-- 底部信息区域 -->
        <div class="bottom-info">
          <div class="layout-name" :title="layout.name">{{ layout.name }}</div>
          <div class="action-buttons">
            <button class="edit-btn" @click.stop="handleEditLayout(layout)" title="编辑">
              <i class="el-icon-edit"></i>
            </button>
            <button class="delete-btn" @click.stop="handleDeleteLayout(layout)" title="删除">
              <i class="el-icon-delete"></i>
            </button>
          </div>
        </div>
      </div>

      <!-- 空状态 -->
      <div v-if="!loading && filteredLayouts.length === 0" class="empty-state">
        <i class="el-icon-folder-opened"></i>
        <p>暂无符合当前比例的布局</p>
      </div>
    </div>

    <!-- 布局选择弹窗 -->
    <LayoutSelectDialog
      :visible.sync="showSelectDialog"
      :target-ratio="liveRatioString"
      @layout-selected="handleLayoutSelected"
      @close="handleDialogClose"
    />

    <!-- 布局编辑器 -->
    <LayoutEditor
      :visible.sync="showLayoutEditor"
      :ratio="editingLayout ? editingLayout.ratio : '16:9'"
      :layout-data="editingLayout"
      @save-layout="handleLayoutSaved"
      @close="handleLayoutEditorClose"
    />
  </div>
</template>

<script>
import { mapGetters, mapActions } from 'vuex'
import LayoutSelectDialog from './LayoutSelectDialog.vue'
import LayoutEditor from '../layoutManage/layoutEditor.vue'
import liveApi from '@/api/live'

export default {
  name: 'LayoutTab',
  components: {
    LayoutSelectDialog,
    LayoutEditor
  },
  data() {
    return {
      loading: false,
      showSelectDialog: false,
      showLayoutEditor: false,
      editingLayout: null
    }
  },
  computed: {
    ...mapGetters(['layouts', 'liveRatio', 'currentLayoutId']),

    // 将数字比例转换为字符串比例
    liveRatioString() {
      const ratio = this.liveRatio
      if (Math.abs(ratio - 16 / 9) < 0.01) return '16:9'
      if (Math.abs(ratio - 9 / 16) < 0.01) return '9:16'
      if (Math.abs(ratio - 4 / 3) < 0.01) return '4:3'
      if (Math.abs(ratio - 4 / 9) < 0.01) return '4:9'
      if (Math.abs(ratio - 1) < 0.01) return '1:1'
      return '16:9' // 默认值
    },

    // 过滤符合当前直播比例且usedType为1的布局
    filteredLayouts() {
      return this.layouts.filter(layout => {
        const ratioMatch = layout.ratio === this.liveRatioString
        const usedTypeMatch = layout.usedType === 1
        return ratioMatch && usedTypeMatch
      })
    }
  },
  mounted() {
    this.loadLayoutsData()
  },
  methods: {
    ...mapActions(['loadLayouts', 'deleteLayoutFromStore', 'updateLayoutInStore', 'setCurrentLayoutId']),

    async loadLayoutsData() {
      this.loading = true
      try {
        await this.loadLayouts()
      } catch (error) {
        console.error('加载布局列表失败:', error)
        this.$message.error('加载布局列表失败')
      } finally {
        this.loading = false
      }
    },

    handleAddLayout() {
      console.log('打开添加布局对话框')
      this.showSelectDialog = true
    },

    async handleSelectLayout(layout) {
      console.log('选择布局:', layout)

      // 调用useLayout接口切换当前使用布局
      try {
        await this.callUseLayoutAPI(layout.id)

        // 设置当前布局ID
        this.setCurrentLayoutId(layout.id)
        this.filteredLayouts.forEach(layoutItem => {
          layoutItem.selected = false
          this.updateLayoutInStore(layoutItem)
        })
        layout.selected = true
        this.updateLayoutInStore(layout)
        this.$message.success(`已切换至布局: ${layout.name}`)
        this.$emit('select-layout', layout)
      } catch (error) {
        console.error('切换布局失败:', error)
        this.$message.error('切换布局失败')
      }
    },

    handleEditLayout(layout) {
      console.log('编辑布局:', layout)
      // 直接在当前组件中打开布局编辑器
      this.editingLayout = { ...layout } // 创建副本避免直接修改
      this.showLayoutEditor = true
    },

    async handleDeleteLayout(layout) {
      try {
        // 判断是否为当前使用的布局
        if (this.currentLayoutId === layout.id) {
          this.$message.warning('无法删除当前使用的布局')
          return
        }

        await this.$confirm(`确定要删除布局 "${layout.name}" 吗？`, '确认删除', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })

        // 将usedType设为0并调用更新接口
        const updatedLayout = {
          ...layout,
          usedType: 0
        }

        await this.callUpdateLayoutAPI(updatedLayout)

        // 更新store中的数据
        this.updateLayoutInStore(updatedLayout)

        this.$message.success(`已删除布局: ${layout.name}`)
      } catch (error) {
        if (error !== 'cancel') {
          console.error('删除布局失败:', error)
          this.$message.error('删除布局失败')
        }
      }
    },

    async handleLayoutSelected(layout) {
      console.log('选择的布局:', layout)

      try {
        // 创建布局副本并设置usedType为1
        const updatedLayout = {
          ...layout,
          usedType: 1
        }

        // 调用更新布局接口
        await this.callUpdateLayoutAPI(updatedLayout)

        // 更新Vuex中的布局数据
        this.updateLayoutInStore(updatedLayout)

        this.$message.success(`已添加布局: ${layout.name}`)
        this.$emit('select-layout', updatedLayout)
      } catch (error) {
        console.error('添加布局失败:', error)
        this.$message.error('添加布局失败')
      }
    },

    handleDialogClose() {
      this.showSelectDialog = false
    },

    // API调用辅助方法
    callUpdateLayoutAPI(layout) {
      return new Promise((resolve, reject) => {
        // 导入数据转换函数
        const { convertToBackendLayout } = require('@/components/layoutManage/layoutDataConverter')

        // 转换为后端格式
        const backendLayout = convertToBackendLayout(layout)

        liveApi.updateLayout({layout: backendLayout},
          (response) => {
            console.log('更新布局成功:', response)
            resolve(response)
          },
          (error) => {
            console.error('更新布局失败:', error)
            reject(error)
          }
        )
      })
    },

    callUseLayoutAPI(layoutId) {
      return new Promise((resolve, reject) => {
        liveApi.useLayout({ id: layoutId },
          (response) => {
            console.log('切换布局成功:', response)
            resolve(response)
          },
          (error) => {
            console.error('切换布局失败:', error)
            reject(error)
          }
        )
      })
    },

    // 布局编辑器相关方法
    handleLayoutSaved(savedLayoutData) {
      console.log('布局保存成功:', savedLayoutData)

      // 更新Vuex store中的数据
      this.updateLayoutInStore(savedLayoutData)

      // 关闭编辑器
      this.showLayoutEditor = false
      this.editingLayout = null

      this.$message.success('布局保存成功')
    },

    handleLayoutEditorClose() {
      console.log('关闭布局编辑器')
      this.showLayoutEditor = false
      this.editingLayout = null
    }
  }
}
</script>

<style lang="scss" scoped>
.layout-tab {
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 12px;
  background: #2c2c2c;
}

.header-actions {
  margin-bottom: 12px;

  .add-layout-btn {
    background: #ff6b35;
    border-color: #ff6b35;

    &:hover {
      background: #ff5722;
      border-color: #ff5722;
    }
  }
}

.layout-list {
  flex: 1;
  overflow-y: auto;
  display: flex;
  flex-wrap: wrap;
  gap: 12px;

  .layout-item {
    width: 186px;
    height: 140px;
    background: #1a1a1a;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
    transition: transform 0.2s ease, box-shadow 0.2s ease, border 0.2s ease;
    display: flex;
    flex-direction: column;
    margin-bottom: 12px;
    cursor: pointer;
    border: 1px solid transparent;

    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4);
      border-color: #ff6b35;
    }

    &.active {
      border-color: #ff6b35;
      box-shadow: 0 0 0 2px rgba(255, 107, 53, 0.3);

      .bottom-info {
        background: #ff6b35;
      }

      .layout-name {
        color: #ffffff;
      }
    }
  }

  .image-preview {
    width: 100%;
    height: 110px;
    overflow: hidden;
    position: relative;
    flex-shrink: 0;

    img {
        object-fit: cover;
    }
    .preview-background {
      width: 100%;
      height: 100%;
      background-size: cover;
      background-position: center;
      background-repeat: no-repeat;
      transition: transform 0.3s ease;
    }

    &:hover .preview-background {
      transform: scale(1.05);
    }

    .preview-placeholder {
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      background: #555555;
      color: #999999;
      font-size: 24px;
    }
  }

  .ratio-display {
    position: absolute;
    top: 6px;
    left: 6px;
    color: white;
    font-size: 10px;
    font-weight: 500;
    z-index: 2;
    background: rgba(0, 0, 0, 0.6);
    padding: 1px 4px;
    border-radius: 2px;
    backdrop-filter: blur(3px);
  }

  .bottom-info {
    height: 30px;
    background: #2E333D;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 8px;
    flex-shrink: 0;
    transition: background 0.2s ease;
  }

  .layout-name {
    color: white;
    font-size: 11px;
    font-weight: 500;
    flex: 1;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .action-buttons {
    display: flex;
    gap: 4px;
    opacity: 0;
    transition: opacity 0.2s ease;

    button {
      width: 16px;
      height: 16px;
      border: none;
      border-radius: 2px;
      background: transparent;
      backdrop-filter: blur(5px);
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.2s ease;
      color: #fff;
      font-size: 8px;

      &:hover {
        background: rgba(255, 255, 255, 0.2);
        transform: scale(1.1);
      }
    }

    .edit-btn:hover {
      color: #ff6b35;
    }

    .delete-btn:hover {
      color: #f56c6c;
    }
  }

    .layout-item:hover .action-buttons {
    opacity: 1;
  }

  .empty-state {
    margin: 0 auto;
    margin-top: 70px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 120px;
    color: #999999;
    text-align: center;

    i {
      font-size: 32px;
      margin-bottom: 8px;
      color: #666666;
    }

    p {
      margin: 0 0 8px 0;
      font-size: 12px;
    }

    .el-button {
      color: #ff6b35;
      font-size: 12px;
    }
  }
}

// 滚动条样式
.layout-list::-webkit-scrollbar {
  width: 4px;
}

.layout-list::-webkit-scrollbar-track {
  background: #2c2c2c;
}

.layout-list::-webkit-scrollbar-thumb {
  background: #555555;
  border-radius: 2px;

  &:hover {
    background: #666666;
  }
}
</style>
