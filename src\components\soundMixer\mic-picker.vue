<template>
  <el-dialog title="设置" :visible.sync="visible" width="500px" top="20vh" append-to-body
    :close-on-click-modal="false" @close="close()">
    <el-form label-width="120px">
      <el-form-item label="音频设备：">
        <el-select v-model="micDeviceId" placeholder="请选择音频设备">
          <el-option v-for="item in deviceList" :key="item.CardNumber" :label="item.name" :value="item.CardNumber"></el-option>
        </el-select>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <span class="g-option-btn-1" style="width: 84px" @click="confirm">确定</span>
      <span class="g-option-btn-2" style="width: 84px" @click="close">取消</span>
    </span>
  </el-dialog>
</template>

<script>
import liveApi from 'api/live'
import { mapGetters } from 'vuex'

export default {
  components: {},
  props: {},
  computed: {
    ...mapGetters(['cardNumber'])
  },
  data() {
    return {
      visible: true,
      deviceList: [],
      micDeviceId: ''
    }
  },
  methods: {
    getMicList() {
      liveApi.getAudioDevice(
        {},
        (res) => {
          if (res && Array.isArray(res.soundDevices)) {
            this.deviceList = res.soundDevices
            const activeIndex = this.deviceList.findIndex(item => item.CardNumber === this.cardNumber)
            if (activeIndex >= 0) {
              this.micDeviceId = this.cardNumber
            }
          }
        },
        (code, msg) => {
          console.log(code, msg)
          this.$message.error(msg)
        }
      )
    },
    close() {
      this.$emit('close')
    },
    confirm() {
      if (!this.micDeviceId) {
        this.$message.warning('请选择设备')
        return
      }
      liveApi.setAudioDevice(
        {
          name: this.micDeviceId
        },
        () => {
          this.$message.success('设置成功')
          this.$emit('close')
        },
        (code, msg) => {
          this.$message.error(msg)
        }
      )
    }
  },
  created() {
    this.getMicList()
  },
  mounted() {}
}
</script>

<style scoped lang="scss">
</style>
