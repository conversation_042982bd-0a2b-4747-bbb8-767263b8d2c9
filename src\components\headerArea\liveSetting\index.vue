<template>
  <el-dialog title="直播设置" width="750px" top="70px" :visible.sync="showDialog" :close-on-click-modal="false"
             @close="cancel()">
    <div class="live-setting">
      <div class="left-tabs">
        <ul>
          <li v-for="item in tabList" class="tab-item" :class="{active: item.active}" @click.stop="selectItem(item)">
            <span class="tab-title">{{item.name}}</span>
          </li>
        </ul>
        <div class="footer">
          <el-button type="success" @click="showLog" size="small" v-show="!isSingleInstance && roleType !== 2">日 志</el-button>
          <el-button type="primary" @click="reStart" size="small" v-show="roleType !== 2" style="margin-left: 0;margin-top: 10px">重 启</el-button>
        </div>
      </div>
      <div class="right-content">
        <template class="live-publish" v-if="currentId===1">
          <tab-title titleText="输出编码" style="width: 100%;">
            <span slot="right" class="tips" style="line-height: 20px;font-size: 12px;font-weight: 400">按当前输出编码参数，请确保本地宽带在
              <span style="color:red;">{{Math.ceil(publishInfo.rateType * urlList.length * 2 / 1024)}}</span> M</span>
          </tab-title>
          <div class="push-setting">
            <div class="push-item" v-if="$_isLocal">
              <span class="name">分辨率</span>
              <el-select v-model="publishInfo.resolutionType" placeholder="请选择分辨率" size="small" style="width: 130px;height: 34px;"
                         @change="resolutionTypeChange" :disabled="isLiving">
                <el-option v-for="item in resolutionTypeOptions" :key="item.id" :label="item.label" :value="item.id"></el-option>
              </el-select>
            </div>
            <div class="push-item" v-if="$_isLocal">
              <span class="name">帧率</span>
              <el-select v-model="publishInfo.frameType" placeholder="请选择帧率" size="small" style="width: 130px;height: 34px;" :disabled="isLiving">
                <el-option v-for="item in frameTypeOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
              </el-select>
            </div>
            <div class="push-item">
              <span class="name">码率</span>
              <el-select v-model="publishInfo.rateType" placeholder="请选择码率" size="small" style="width: 130px;height: 34px;" :disabled="isLiving">
                <el-option v-for="item in rateTypeOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
              </el-select>
            </div>
          </div>
          <span style="padding-left: 20px;font-size: 12px" class="tips">注：修改分辨率后，原节目单关联CG 自动取消关联，需重新创建对应分辨率的CG字幕</span>
          <tab-title titleText="分发平台" style="width: 100%;">
            <template slot="right">
              <span class="tips" style="line-height: 20px;font-size: 12px;font-weight: 400">不超过10个</span>
              <span style="margin-left: 250px;">NDI输出：</span>
              <el-switch
                v-model="ndiOutputEnabled"
                active-color="#f55b0f"
                inactive-color="#535d72"
              >
              </el-switch>
            </template>
          </tab-title>
          <div class="push-platform">
            <ul class="url-ul" style="margin-bottom: 60px">
              <li class="url-li" :key="index" v-for="(item, index) in urlList" style="margin-bottom: 10px">
                <svg-icon v-show="warningUrlIds.includes(index)" icon-class="warning" style="margin-left: -20px;margin-right: 8px"></svg-icon>
                <el-input style="width: 470px;" size="small" v-model.trim="item.url" placeholder="请输入分发地址"></el-input>
                <el-button style="margin-left: 10px;" icon="el-icon-minus" type="info" size="small" v-if="index" @click="deleteItem('url', index)"></el-button>
                <el-button style="margin-left: 10px;" icon="el-icon-plus" type="info" size="small" v-else @click="addNewItem('url')"></el-button>
              </li>
              <span class="tips">注：支持RTMP、SRT协议输出，SRT协议规则：srt://ip:端口</span>
            </ul>
          </div>
          <span slot="footer" class="dialog-footer">
            <el-button type="info" @click="cancel" size="small">取 消</el-button>
            <el-button type="primary" @click="confirmPublishBefore()" size="small">确 定</el-button>
          </span>
        </template>
        <template class="output-set" v-if="currentId === 2">
          <tab-title titleText="SRT输出" style="width: 100%;"></tab-title>
          <el-input style="margin-left: 20px;width: 400px;" size="small" v-model.trim="outputForm.srtUrl" placeholder="请输入SRT地址"></el-input>
          <span slot="footer" class="dialog-footer">
            <el-button type="info" @click="cancel" size="small">取 消</el-button>
            <el-button type="primary" @click="confirmOutputSet()" size="small">确 定</el-button>
          </span>
        </template>
        <template class="base-set" v-if="currentId===3">
          <el-form :model="globalForm" ref="globalForm" label-width="120px">
            <el-form-item label="延时安全：" prop="delay">
              <el-input-number :min="0" :max="60" v-model.trim="globalForm.delay" size="small" :disabled="isLiving"></el-input-number>
              <p style="line-height: 20px;" class="tips">支持0~60秒延迟输出，直播中不可以修改延时时间，请提前设置</p>
            </el-form-item>
            <el-form-item label="紧急切换地址：" prop="url">
              <el-radio v-model="globalForm.safeSwitchType" :label="1">选择本地文件</el-radio>
              <el-radio v-model="globalForm.safeSwitchType" :label="2">网络流地址</el-radio>
              <div class="input-box" :class="{label1: globalForm.safeSwitchType === 1, label2: globalForm.safeSwitchType === 2}">
                <el-input size="small" v-model.trim="globalForm.safeVideoUrl" placeholder="请输入紧急切换地址" style="width: 326px" v-if="globalForm.safeSwitchType === 2"></el-input>
                <div v-else class="text-box">
                  <span class="text">{{globalForm.safeFileName || '还未添加本地文件'}}</span>
                  <el-upload
                          :action="fileUploadApi"
                          :before-upload="beforeUpload"
                          :on-success="handleSuccess"
                          :on-error="handleError"
                          :show-file-list="false">
                    <span class="my-grey-btn" slot="trigger">
                      <i class="el-icon-upload2"></i>
                      上传
                    </span>
                  </el-upload>
                </div>
              </div>
              <p class="tips" style="font-size: 12px;line-height: 20px" v-if="globalForm.safeSwitchType === 1">仅支持MP4格式的视频文件</p>
            </el-form-item>
            <el-form-item label="静帧图：">
              <upload-img class="capture-img" hasDelete :editImgUrl="$_getPicture(globalForm.capture)" @uploadImgUrl="uploadImgUrl" :fileSize="4">
              </upload-img>
              <p class="tips" style="font-size: 12px;line-height: 20px">上传图片大小控制在4M以内,支持PNG/JPG/JPEG格式,当前窗口分辨率{{liveWidth}}*{{liveHeight}}</p>
            </el-form-item>
            <el-form-item  label="断流自动切换:">
              <el-switch v-model="globalForm.flag" active-text="" inactive-text="" active-color="#FF5B1F" inactive-color="#BCC9E8"></el-switch>
              <span style="line-height: 20px;margin-left: 20px" class="tips">开启后断流时将自动切换到紧急切换源</span>
            </el-form-item >
          </el-form>
          <span slot="footer" class="dialog-footer">
            <el-button type="info" @click="cancel" size="small">取 消</el-button>
            <el-button type="primary" @click="confirmSetting()" size="small">确 定</el-button>
          </span>
        </template>

        <template class="warning-set" v-if="currentId===5 && showWarningBox">
          <el-form>
            <el-form-item label="断流预警手机：" label-width="120px">
              <ul class="email-ul">
                <li class="email-li" :key="index" v-for="(item, index) in mobileList">
                  <el-input style="width: 240px;" size="small" :maxlength="30" v-model.trim="item.mobile" placeholder="请输入预警手机号码"></el-input>
                  <el-button style="margin-left: 10px;" icon="el-icon-minus" type="info" size="small" v-if="index" @click="deleteItem('mobile', index)"></el-button>
                  <el-button style="margin-left: 10px;" icon="el-icon-plus" type="info" size="small" v-else @click="addNewItem('mobile')"></el-button>
                </li>
              </ul>
              <span style="line-height: 20px" class="tips">用于直播流在非正常断开时接收预警提醒信息</span>
            </el-form-item>
            <el-form-item label="断流预警邮箱：" label-width="120px">
              <ul class="email-ul">
                <li class="email-li" :key="index" v-for="(item, index) in mailList">
                  <el-input style="width: 240px;" size="small" :maxlength="30" v-model.trim="item.email" placeholder="请输入预警邮箱地址"></el-input>
                  <el-button style="margin-left: 10px;" icon="el-icon-minus" type="info" size="small" v-if="index" @click="deleteItem('email', index)"></el-button>
                  <el-button style="margin-left: 10px;" icon="el-icon-plus" type="info" size="small" v-else @click="addNewItem('email')"></el-button>
                </li>
              </ul>
              <span style="line-height: 20px" class="tips">用于直播流在非正常断开时接收预警提醒信息</span>
            </el-form-item>
          </el-form>
          <span slot="footer" class="dialog-footer">
            <el-button type="info" @click="cancel" size="small">取 消</el-button>
            <el-button type="primary" @click="confirmWarning()" size="small">确 定</el-button>
          </span>
        </template>
        <template v-if="currentId===6">
          <el-form class="pwd-set">
            <el-form-item label="口令：" label-width="60px">
              <div style="display: flex;justify-content: flex-start;align-items: center">
                <el-input v-if="!visibleInput" type="password" v-model.trim="password" ref="password" style="width: 220px;" size="small">
                  <i slot="suffix"  class="icon-no-view" @click="changePass('show')"></i>
                </el-input>
                <el-input v-else type="text" v-model.trim="password" ref="password" style="width: 220px;" size="small">
                  <i slot="suffix"  class="icon-view" @click="changePass('hide')"></i>
                </el-input>
                <el-button class="copy-btn" type="primary" style="margin-left: 10px; height: 32px" :data-clipboard-text="oldPassWord" size="small">复制口令</el-button>
                <el-button type="success" style="margin-left: 10px; height: 32px" @click="modifyPassword" size="small">修改口令</el-button>
              </div>
            </el-form-item>
          </el-form>
        </template>
        <template v-if="currentId===7">
          <div class="version-info">
            <span style="margin-right: 40px">当前版本号: V{{versionInfo.currentVersion}}</span>
            <!-- <el-button type="primary" @click="upgrade" v-if="checkVersion()">升级</el-button> -->
          </div>
          <p class="version-info">设备编号: {{versionInfo.deviceCode}}</p>

          <el-button type="success" @click="showLog" size="small">日志</el-button>
          <div class="upload-block flex-c-c">
          <!-- <div class="upload-block flex-c-c" v-if="checkVersion()"> -->
            <template v-if="versionInfo.upgradeDownloadStatus === 'downloading'">
              <div class="tips-text">正在下载，请稍后 <i class="el-icon-loading"></i></div>
            </template>
            <template v-else-if="versionInfo.upgradeDownloadStatus === 'finish'">
              <div class="tips-text">已完成下载，是否升级</div>
              <el-button type="primary" @click="upgrade" size="small">立即升级</el-button>
            </template>
            <template v-else>
              <div class="tips-text">检测到有新版本V{{versionInfo.latestVersion}}</div>
              <el-button type="primary" size="small" @click="doDownloadUpgrade">下载</el-button>
            </template>
          </div>
        </template>
        <template v-if="currentId===8">
          <el-button class="copy-btn" type="primary" style="margin-left: 10px; height: 32px" size="small" @click="getHaikangSetting">海康摄像头接入</el-button>
          <haikang-setting v-if="showHaikangDialog" @close="showHaikangDialog= false" :haikangSettingInfo="haikangSettingInfo"></haikang-setting>
        </template>
        <template v-if="currentId===9">
          <el-form class="pwd-set">
            <el-form-item label="UDP端口范围：" label-width="120px">
              <div style="display: flex;justify-content: flex-start;align-items: center">
                <el-input v-model.trim="port.portMin"
                          onkeypress="return( /[\d]/.test(String.fromCharCode(event.keyCode)))"
                          style="width: 100px;" size="small">
                </el-input>
                <span style="margin: 0 10px; color: #A8B7D2">至</span>
                <el-input v-model.trim="port.portMax"
                          onkeypress="return( /[\d]/.test(String.fromCharCode(event.keyCode)))"
                          style="width: 100px;" size="small">
                </el-input>
                <el-button type="success" style="margin-left: 10px; height: 32px" @click="onSavePort" size="small">保存并重启</el-button>
              </div>
            </el-form-item>
            <span class="tips">注：udp端口主要用于控制页面的画面预览和数据传输，多人打开页面都会占用端口，端口总数配置建议在1000以上</span>
            <el-form-item label="外网映射IP：" label-width="120px" style="margin-top: 20px;">
              <el-input v-model="currentMappedIp" style="width: 236px;"></el-input>
              <el-button type="success" style="margin-left: 10px; height: 32px" @click="onSaveMappedIp" size="small">保存</el-button>
            </el-form-item>
            <span class="tips">注：内容为空则代表未开启外网IP映射，只支持内网IP访问</span>
          </el-form>
        </template>
      </div>
    </div>

    <el-dialog title="系统日志" width="1400px" top="30px" :visible.sync="showLogDialog" :close-on-click-modal="true" append-to-body>
      <div class="log-box" v-html="logText" id="logBox">
      </div>
    </el-dialog>
  </el-dialog>
</template>

<script>
  import uploadImg from '@/components/base/v-upload-img'
  import liveApi from 'api/live'
  import cloudApi from 'api/cloud'
  import {mapGetters, mapMutations} from 'vuex'
  import TabTitle from '@/components/base/tab-title'
  import Clipboard from 'clipboard'
  import Upload from 'common/js/upload'
  import haikangSetting from './haikang-setting'

  export default {
    name: 'live-setting',
    components: {uploadImg, TabTitle, haikangSetting},
    props: {
      typeId: {
        type: Number,
        default: 1
      }
    },
    data () {
      return {
        visibleInput: false,
        fileUploadApi: '/cloud/fullTimeLive/safe/file/upload?instanceId=',
        showHaikangDialog: false,
        outputForm: {
          srtUrl: ''
        },
        uploadUrl: '',
        pinpInfo: {
          oldImgList: [],
          capture: ''
        },
        warningUrlIds: [],
        publishInfo: {
          resolutionType: 1,
          rateType: 3000,
          frameType: 25
        },
        resolutionTypeOptions: [],
        frameTypeOptions: [
          {value: 20, label: '20 fps'},
          {value: 25, label: '25 fps'},
          {value: 30, label: '30 fps'},
          {value: 50, label: '50 fps'}
          // {value: 60, label: '60 fps'}

        ],
        rateTypeOptions: [
          {value: 800, label: '800 kbps'},
          {value: 900, label: '900 kbps'},
          {value: 1000, label: '1000 kbps'},
          {value: 1500, label: '1500 kbps'},
          {value: 2000, label: '2000 kbps'},
          {value: 2500, label: '2500 kbps'},
          {value: 3000, label: '3000 kbps'},
          {value: 3500, label: '3500 kbps'},
          {value: 4000, label: '4000 kbps'},
          {value: 5000, label: '5000 kbps'},
          {value: 6000, label: '6000 kbps'},
          {value: 7000, label: '7000 kbps'},
          {value: 8000, label: '8000 kbps'},
          {value: 9000, label: '9000 kbps'},
          {value: 10000, label: '10000 kbps'}
        ],
        showDialog: true,
        showLogDialog: false,
        tabList: [
          {id: 1, name: '直播分发', active: false},
          // {id: 2, name: '输出设置', active: false},
          {id: 3, name: '安全播出设置', active: false},
          {id: 5, name: '预警设置', active: false},
          {id: 8, name: '工具', active: false},
          {id: 9, name: 'UDP端口设置', active: false},
          {id: 6, name: '口令', active: false},
          {id: 7, name: '关于', active: false}
        ],
        currentId: 1,
        globalForm: {
          delay: '',
          safeVideoUrl: '', // 紧急切换地址
          flag: false,
          safeSwitchType: 2,
          capture: '',
          safeVideoUrlLocal: '',
          safeFileName: '',
          oldFilePathList: [],
          oldImgList: []
        },
        password: '',
        port: {
          portMin: 0,
          portMax: 0
        },
        currentMappedIp: '',
        oldPassWord: '',
        mailList: [{email: ''}],
        mobileList: [{mobile: ''}],
        ndiOutputEnabled: false,
        urlList: [{url: ''}],
        validatorMobile: true,
        validatorEmail: true,
        showWarningBox: false,
        logText: '',
        haikangSettingInfo: null
      }
    },
    watch: {
      currentId (val) {
        if (val === 3) {
          this.globalForm.capture = this.defaultPic
          this.globalForm.delay = this.output.delay / 1000
          if (this.Emergency.safeVideoUrl.split('/')[1] === 'data') {
            this.globalForm.safeVideoUrl = ''
            this.globalForm.safeVideoUrlLocal = this.Emergency.safeVideoUrl
            this.globalForm.safeFileName = this.Emergency.name
            this.globalForm.safeSwitchType = 1
          } else {
            this.globalForm.safeVideoUrl = this.Emergency.safeVideoUrl
            this.globalForm.safeVideoUrlLocal = ''
            this.globalForm.safeFileName = ''
            this.globalForm.safeSwitchType = 2
          }
          this.globalForm.flag = this.Emergency.autoFlag
        } else if (val === 4) {
          this.pinpInfo.capture = this.background
        } else if (val === 5) {
          this.showWarningLocal()
        } else if (val === 6) {
          this.getPwd()
        } else if (val === 9) {
          this.port.portMin = this.portRange.portMin >= 0 ? this.portRange.portMin : 30000
          this.port.portMax = this.portRange.portMax >= 0 ? this.portRange.portMax : 40000
          this.currentMappedIp = this.mappedIp
        }
      }
    },
    computed: {
      isSingleInstance () { // 是否是单实例
        return this.machineInfo.instanceCount === 1
      },
      isLiving () {
        return this.output.isLiving
      },
      ...mapGetters([
        'roleType',
        'machineInfo',
        'instanceId',
        'pwd',
        'output',
        'Emergency',
        'defaultPic',
        'liveRatio',
        'liveWidth',
        'liveHeight',
        'warnSetting',
        'versionInfo',
        'background',
        'programs',
        'portRange',
        'mappedIp'
      ])
    },
    methods: {
      changePass () {
        this.visibleInput = !this.visibleInput
      },
      getHaikangSetting () {
        liveApi.getHaikangSetting({}, res => {
          this.showHaikangDialog = true
          if (res) {
            this.haikangSettingInfo = res
          } else {
            this.haikangSettingInfo = {
              ip: '',
              port: '',
              appKey: '',
              appSecret: ''
            }
          }
        })
      },
      cancel () {
        if (this.globalForm.oldFilePathList.length) {
          let deleteList = []
          this.globalForm.oldFilePathList.forEach(item => {
            if (item !== this.Emergency.safeVideoUrl) {
              deleteList.push(item)
            }
          })
          deleteList.push(this.globalForm.safeVideoUrlLocal)
          this.deleteFile(deleteList, () => { this.globalForm.oldFilePathList = [] })
        }
        this.close()
      },
      close () {
        this.$emit('close')
      },
      handleSuccess (res, file) {
        if (this.globalForm.safeVideoUrlLocal) {
          this.globalForm.oldFilePathList.push(this.globalForm.safeVideoUrlLocal)
        }
        this.globalForm.safeVideoUrlLocal = res.value.path
        this.globalForm.safeFileName = file.name
      },
      handleError () {
        this.$message.error('上传失败')
      },
      beforeUpload (file) {
        let fileType = file.name.substring(file.name.lastIndexOf('.') + 1, file.name.length)
        let uploadType = Upload.getUploadType(fileType)
        if (fileType.toLowerCase() !== 'mp4') {
          this.$message.error('请上传mp4格式文件')
          return false
        }
        var errorMsg = Upload.checkFileSize(uploadType, file.size)
        if (errorMsg !== '') {
          this.$message.error(errorMsg)
          return false
        }
        this.uploadDisable = true
      },
      checkVersion () {
        let result = this.$_compareVersion(this.versionInfo.latestVersion, this.versionInfo.currentVersion)
        if (result === 1) return true
        return false
      },
      doDownloadUpgrade () {
        cloudApi.downloadUpgrade({latestVersion: this.versionInfo.latestVersion}, () => {
          this.setVersionInfo({...this.versionInfo, upgradeDownloadStatus: 'downloading'})
          this.getMachineInfo()
        })
      },
      upgrade () {
        this.$confirm('确定要升级最新版本吗?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          cloudApi.upgradeReq({systemVersion: this.versionInfo.latestVersion}, () => {
            const loading = this.$loading({
              lock: true,
              text: '正在升级中',
              spinner: 'el-icon-loading',
              background: 'rgba(0, 0, 0, 0.7)'
            })
            setInterval(() => {
              cloudApi.checkUpdate({}, res => {
                if (res.isUpgrade) {
                  location.reload(true)
                }
              })
            }, 2000)
          })
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消'
          })
        })
      },
      resolutionTypeChange (value) {
        let item = this.resolutionTypeOptions[value - 1]
        if ([2, 10].includes(item.id)) {
          this.publishInfo.rateType = 5000
          this.publishInfo.frameType = 30
        } else if ([1, 8, 9].includes(item.id)) {
          this.publishInfo.rateType = 3000
          this.publishInfo.frameType = 25
        } else if ([3, 4, 5, 6, 7].includes(item.id)) {
          this.publishInfo.rateType = 1500
          this.publishInfo.frameType = 25
        }
      },
      modifyPassword () {
        if (this.password.length < 6 || this.password.length > 255) {
          return this.$message.warning('口令长度应在6-255字之间')
        }
        let param = {
          pwd: this.oldPassWord,
          newPwd: this.password
        }
        cloudApi.modifyPassword(param, () => {
          if (this.roleType === 0) {
            localStorage.removeItem('live_pwd')
          }
          this.oldPassWord = param.newPwd
          this.$message.success('口令修改成功')
        })
      },
      onSaveMappedIp() {
        cloudApi.setMappedIp({ip: this.currentMappedIp}, () => {
          this.setMappedIp(this.currentMappedIp)
          this.$message.success('保存成功')
        })
      },
      onSavePort () {
        if (!this.port.portMin || !this.port.portMax) {
          return this.$message.warning('请输入端口范围')
        }
        this.port.portMin = parseInt(this.port.portMin)
        this.port.portMax = parseInt(this.port.portMax)
        if (this.port.portMin < 30000 || this.port.portMin > 40000 || this.port.portMax < 30000 || this.port.portMax > 40000) {
          return this.$message.warning('请确保端口范围在30000到40000之间')
        }
        if (this.port.portMin >= this.port.portMax) {
          return this.$message.warning('请输入正确的端口范围')
        }
        let param = this.port
        this.$confirm('重启实例后生效，是否立即重启?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          cloudApi.saveInstancePort(param, () => {
            this.setPortRange(this.port)
            this.reStartPop()
          })
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消'
          })
        })
      },
      clipboard () {
        let clipboard = new Clipboard('.copy-btn')
        clipboard.on('success', (e) => {
          this.$message.success('已复制')
        })
      },
      getOptions () {
        this.resolutionTypeOptions = [
          {id: 1, label: '1920x1080', width: 1920, height: 1080},
          // {id: 2, label: '3840x2160', width: 3840, height: 2160},
          {id: 3, label: '1280x720', width: 1280, height: 720},
          {id: 4, label: '1280x960', width: 1280, height: 960},
          {id: 5, label: '1024x768', width: 1024, height: 768},
          {id: 6, label: '720x1280', width: 720, height: 1280},
          {id: 7, label: '720x1620', width: 720, height: 1620},
          {id: 8, label: '1080x1920', width: 1080, height: 1920},
          {id: 9, label: '1080x2430', width: 1080, height: 2430}
          // {id: 10, label: '2160x3840', width: 2160, height: 3840}
        ]
        if (this.machineInfo.is4k) {
          this.resolutionTypeOptions.unshift({id: 10, label: '2160x3840', width: 2160, height: 3840}, {id: 2, label: '3840x2160', width: 3840, height: 2160})
          this.rateTypeOptions.push(
            {value: 15000, label: '15000 kbps'},
            {value: 20000, label: '20000 kbps'},
            {value: 30000, label: '30000 kbps'},
            {value: 50000, label: '50000 kbps'})
        }
      },
      warningValidator () {
        let that = this
        this.validatorMobile = true
        this.validatorEmail = true
        this.mobileList.forEach(item => {
          if (!(/^1[3456789]\d{9}$/.test(item.mobile))) {
            that.validatorMobile = false
            return false
          }
        })
        this.mailList.forEach(item => {
          if (!(/^\w+@[a-z0-9]+\.[a-z]{2,4}$/.test(item.email))) {
            that.validatorEmail = false
            return false
          }
        })
        if (this.mobileList[0].mobile !== '' && this.mailList[0].email !== '') {
          if (!this.validatorMobile) {
            that.$message.warning('请输入正确的手机号码')
            return false
          } else if (!this.validatorEmail) {
            that.$message.warning('请输入正确的邮箱地址')
            return false
          } else {
            return true
          }
        } else if (this.mobileList[0].mobile === '' && this.mailList[0].email !== '') {
          if (!this.validatorEmail) {
            that.$message.warning('请输入正确的邮箱地址')
            return false
          } else {
            return true
          }
        } else if (this.mailList[0].email === '' && this.mobileList[0].mobile !== '') {
          if (!this.validatorMobile) {
            that.$message.warning('请输入正确的手机号码')
            return false
          } else {
            return true
          }
        } else {
          that.$message.warning('请填写预警设置')
        }
      },
      showWarning () {
        this.mobileList = []
        this.mailList = []
        if (this.warnSetting && this.warnSetting.phoneList && this.warnSetting.phoneList.length) {
          this.warnSetting.phoneList.forEach(item => {
            this.mobileList.push({mobile: item})
          })
        } else {
          this.mobileList = [{mobile: ''}]
        }
        if (this.warnSetting && this.warnSetting.mailList && this.warnSetting.mailList.length) {
          this.warnSetting.mailList.forEach(item => {
            this.mailList.push({email: item})
          })
        } else {
          this.mailList = [{email: ''}]
        }
        this.showWarningBox = true
      },
      showWarningLocal () {
        this.mobileList = []
        this.mailList = []
        this.showWarningBox = false
        cloudApi.getSettingList({}, res => {
          if (res.telephoneList && res.telephoneList.length) {
            res.telephoneList.forEach(item => {
              this.mobileList.push({mobile: item})
            })
          } else {
            this.mobileList = [{mobile: ''}]
          }
          if (res.emailList && res.emailList.length) {
            res.emailList.forEach(item => {
              this.mailList.push({email: item})
            })
          } else {
            this.mailList = [{email: ''}]
          }
          this.showWarningBox = true
        })
      },
      deleteItem (type, index) {
        if (type === 'mobile') {
          this.mobileList.splice(index, 1)
        } else if (type === 'email') {
          this.mailList.splice(index, 1)
        } else if (type === 'url') {
          if (this.warningUrlIds.includes(index)) {
            this.warningUrlIds.find((item, i) => {
              if (item === index) this.warningUrlIds.splice(i, 1)
            })
          }
          this.urlList.splice(index, 1)
        }
      },
      addNewItem (type) {
        if (type === 'mobile') {
          if (this.mobileList.length < 5) {
            this.mobileList.push({mobile: ''})
          }
        } else if (type === 'email') {
          if (this.mailList.length < 5) {
            this.mailList.push({email: ''})
          }
        } else if (type === 'url') {
          if (this.urlList.length < 10) {
            this.urlList.push({url: ''})
          }
        }
      },
      getPublishInfo () {
        let currentOptionId = 0
        this.resolutionTypeOptions.findIndex(item => {
          if (item.width === this.output.width && item.height === this.output.height) {
            currentOptionId = item.id
          }
        })
        this.publishInfo.resolutionType = currentOptionId
        this.publishInfo.rateType = this.output.videoBitrate / 1024
        this.publishInfo.frameType = this.output.fps
        this.ndiOutputEnabled = this.output.ndiOutputEnabled
        this.urlList = []
        liveApi.queryOutputsReq({}, res => {
          if (this.output.outputUrls && this.output.outputUrls.length) {
            this.output.outputUrls.forEach((item, index) => {
              res.outputsUrls.forEach(v => {
                if (v === item) {
                  this.warningUrlIds.push(index)
                }
              })
              this.urlList.push({
                url: item
              })
            })
          } else {
            this.urlList.push({url: ''})
          }
        })
      },
      reStart () {
        this.$confirm('确定重启吗?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          cloudApi.restartInstanceReq({}, () => {}, () => {})
          this.reStartPop()
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消'
          })
        })
      },
      reStartPop () {
        const loading = this.$loading({
          lock: true,
          text: '正在重启中',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        })
        setInterval(() => {
          liveApi.getConfig({}, res => {
            location.reload(true)
          }, () => {})
        }, 2000)
      },
      resetPinp () {
      },
      fpsChange (nowValue) {
        return nowValue.fps !== this.output.fps
      },
      radioChange (nowValue) {
        return nowValue.width !== this.output.width || nowValue.height !== this.output.height
      },
      confirmPublishBefore () {
        let hasUrl = this.urlList.find(item => item.url)
        if (!hasUrl && this.isLiving) {
          this.$message.warning('请填写分发地址')
          return
        }
        let resolutionObj = {}
        this.resolutionTypeOptions.findIndex(item => {
          if (this.publishInfo.resolutionType === item.id) {
            resolutionObj = item
          }
        })
        let param = {}
        param.width = resolutionObj.width
        param.height = resolutionObj.height
        param.fps = this.publishInfo.frameType
        param.ndiOutputEnabled = this.ndiOutputEnabled

        param.videoBitrate = this.publishInfo.rateType * 1024
        param.outputUrls = []
        let radioChange = this.radioChange(param)
        let fpsChange = this.fpsChange(param)
        let tip = ''
        if (radioChange && fpsChange) {
          tip = '分辨率或帧率改变将会重新启动，分辨率改变会重置画中画设置，确定重启吗?'
        } else if (radioChange) {
          tip = '分辨率改变将会重新启动，并且会重置画中画设置，确定重启吗?'
        } else if (fpsChange) {
          tip = '帧率改变将会重新启动，确定重启吗?'
        }
        if (radioChange || fpsChange) {
          this.$confirm(tip, '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            if (radioChange) {
              // this.resetPinp()
            }
            this.confirmPublish(param, 'reload')
          }).catch(() => {
            this.$message({
              type: 'info',
              message: '已取消'
            })
          })
        } else {
          this.confirmPublish(param)
        }
      },
      confirmPublish (param, type) {
        if (param.width !== this.output.width || param.height !== this.output.height) {
          this.$bus.$emit('clearCgs')
        }
        if (this.urlList.length === 1) {
          if (this.urlList[0].url) {
            param.outputUrls.push(this.urlList[0].url)
          } else {
            param.outputUrls = []
          }
        } else if (this.urlList.length > 1) {
          this.urlList.forEach(item => {
            if (item.url) {
              param.outputUrls.push(item.url)
            }
          })
        }
        liveApi.setOutput(param, () => {
          if (type === 'reload') {
            this.$loading({
              lock: true,
              text: '正在重启中',
              spinner: 'el-icon-loading',
              background: 'rgba(0, 0, 0, 0.7)'
            })
            setInterval(() => {
              liveApi.getConfig({}, res => {
                location.reload(true)
              }, () => {})
            }, 2000)
          } else {
            this.setOutput(param)
            this.$message.success('直播分发设置成功')
            this.close()
          }
        })
      },
      confirmWarning () {
        if (!this.warningValidator()) return false
        let param = {}
        param.telephoneList = this.mobileList.filter(item => item.mobile).map(item => item.mobile)
        param.emailList = this.mailList.filter(item => item.email).map(item => item.email)
        cloudApi.settingList(param, () => {
          this.$message.success('预警设置成功')
          this.close()
        })
      },
      confirmSetting () {
        let safeUrl = this.globalForm.safeSwitchType === 1 ? this.globalForm.safeVideoUrlLocal : this.globalForm.safeVideoUrl
        liveApi.setSafeVideo({url: safeUrl, autoFlag: this.globalForm.flag, name: this.globalForm.safeFileName}, () => {
          this.setEmergency({safeVideoUrl: safeUrl, autoFlag: this.globalForm.flag, name: this.globalForm.safeFileName})
          if (this.globalForm.oldFilePathList.length && this.globalForm.safeSwitchType === 1) {
            this.deleteFile(this.globalForm.oldFilePathList, () => { this.globalForm.oldFilePathList = [] })
          } else if (this.globalForm.safeSwitchType === 2 && this.globalForm.safeVideoUrlLocal) {
            this.deleteFile(this.globalForm.oldFilePathList.concat(this.globalForm.safeVideoUrlLocal), () => { this.globalForm.oldFilePathList = [] })
          }
          liveApi.setDelay({delay: this.globalForm.delay * 1000}, () => {
            this.setOutput({delay: this.globalForm.delay * 1000})
            this.close()
            liveApi.setDefaultPic({url: this.globalForm.capture}, () => {
              this.setDefaultPicture(this.globalForm.capture)
              if (this.globalForm.oldImgList.length) {
                this.deleteFile(this.pinpInfo.oldImgList, () => { this.pinpInfo.oldImgList = [] })
              }
              this.$message.success('安全播出设置成功')
            })
          })
        })
      },
      confirmPinpBg () {
        liveApi.setBackground({url: this.pinpInfo.capture}, () => {
          this.setbackground(this.pinpInfo.capture)
          this.$message.success('背景图设置成功')
          if (this.pinpInfo.oldImgList.length) {
            this.deleteFile(this.pinpInfo.oldImgList, () => {
              this.pinpInfo.oldImgList = []
            })
          }
          this.close()
        })
      },
      deleteFile (pathList, callback) {
        cloudApi.deleteFile({paths: pathList}, () => {
          callback()
        }, (code, msg) => {})
      },
      uploadImgUrl (uploadInfo) {
        if (this.globalForm.capture) {
          this.globalForm.oldImgList.push(this.globalForm.capture)
        }
        this.globalForm.capture = uploadInfo
      },
      uploadImgUrlPinp (uploadInfo) {
        if (this.pinpInfo.capture) {
          this.pinpInfo.oldImgList.push(this.pinpInfo.capture)
        }
        this.pinpInfo.capture = uploadInfo
      },
      selectItem (item) {
        this.tabList.forEach(v => {
          v.active = false
        })
        item.active = true
        this.currentId = item.id
      },
      showLog () {
        liveApi.getLog(res => {
          this.logText = res.replace(/\r\n/g, '<br>').replace(/\n/g, '<br>')
          this.showLogDialog = true
          this.$nextTick(() => {
            let divContainer = document.getElementById('logBox')
            divContainer.scrollTop = divContainer.scrollHeight
          })
        })
      },
      getMachineInfo () {
        cloudApi.getMachineInfo({liveId: 0}, res => {
          this.setVersionInfo({deviceCode: res.deviceCode, currentVersion: res.currentVersion, latestVersion: res.latestVersion, upgradeDownloadStatus: res.upgradeDownloadStatus})
          this.setMappedIp(res.accessIp)
          // 循环检测升级文件是否下载完成
          if (res.upgradeDownloadStatus === 'downloading') {
            setTimeout(() => {
              this.getMachineInfo()
            }, 5 * 60 * 1000)
          }
        })
      },
      getPwd () {
        cloudApi.getInstancePass({}, res => {
          this.password = res.pwd
          this.oldPassWord = res.pwd
        })
      },
      ...mapMutations({
        setEmergency: 'SET_EMERGENCY',
        setOutput: 'SET_OUTPUT',
        setDefaultPicture: 'SET_DEFAULTPIC',
        setPwd: 'SET_PWD',
        setbackground: 'SET_BACKGROUND',
        setVersionInfo: 'SET_VERSION_INFO',
        setPortRange: 'SET_PPOR_INFO',
        setMappedIp: 'SET_MAPPED_IP'
      })
    },
    created () {
      this.fileUploadApi = this.fileUploadApi + this.instanceId
      if (!this.isSingleInstance) {
        this.tabList.splice(-1)
        if (this.roleType === 2) {
          this.tabList.splice(-1)
        }
        // 如果是多实例则不显示udp端口设置
        this.tabList = this.tabList.filter(item => item.id !== 9)
      } else {
        this.getMachineInfo()
      }
      this.currentId = this.typeId
      this.tabList.find(item => item.id === this.currentId).active = true
      this.clipboard()
      this.getOptions()
      this.getPublishInfo()
    }
  }
</script>

<style scoped lang="scss">
  .live-setting {
    display: flex;
    justify-content: flex-start;
    .tab-item {
      padding: 15px 0 15px 18px;
      cursor: pointer;
      &:hover {
        background-color: #3E4553;
      }
      &.active {
        .tab-title {
          color: $color-theme;
        }
        background-color: #3E4553;
      }
    }
    .tab-title {
      font-size: 16px;
      color: #AEBBD9;
      line-height: 18px;
    }
    .left-tabs {
      position: relative;
      flex: 0 0 140px;
      background-color: #373E4A!important;
      min-height: 570px;
      .footer {
        position: absolute;
        bottom: 20px;
        left: 50%;
        transform: translateX(-50%);
        z-index: 100;
      }
    }
    .right-content {
      flex: 1 1 auto;
      padding: 20px;
      .push-setting {
        display: flex;
        justify-content: flex-start;
        padding-left: 20px;
        margin-bottom: 10px;
        .push-item {
          display: flex;
          flex-direction: column;
          justify-content: flex-start;
          margin-right: 10px;
        }
        .name {
          font-size: 14px;
          color: #AEBBD9;
          line-height: 18px;
          margin-bottom: 10px;
        }
      }
      .push-platform {
        padding-left: 20px;
      }
      .version-info {
        font-weight: 700;
        font-size: 20px;
        color: #AEBBD9;
        margin-right: 10px;
        margin-bottom: 20px;
      }
      .input-box {
        position: relative;
        width: 350px;
        padding: 10px 12px;
        background-color: #525B6C;
        box-sizing: border-box;
        border-radius: 2px;
        .text-box {
          display: flex;
          justify-content: space-between;
          box-sizing: border-box;
        }
        .text {
          display: inline-block;
          font-size: 14px;
          font-family: SimSun;
          font-weight: 400;
          color: #AEBBD9;
          line-height: 40px;
          width: 240px;
          @include elliptical();
        }

        &.label1 {
          &::after {
            content: "";
            width: 0;
            height: 0;
            position: absolute;
            top: -8px;
            left: 10px;
            border-width: 0 8px 8px;
            border-style: solid;
            border-color: transparent transparent #525B6C;
          }
        }

        &.label2 {
          &::after {
            content: "";
            width: 0;
            height: 0;
            position: absolute;
            top: -8px;
            left: 137px;
            border-width: 0 8px 8px;
            border-style: solid;
            border-color: transparent transparent #525B6C;
          }
        }
      }
    }
  }
  .pwd-set {
    .icon-view {
      margin-top: 12px;
      display: inline-block;
      width: 20px;
      height: 20px;
      background: url('../../instanceList/visible.png') no-repeat;
      background-size: 20px;
      &:hover {
        background-image: url('../../instanceList/visible-hover.png');
      }
    }
    .icon-no-view {
      margin-top: 12px;
      display: inline-block;
      width: 20px;
      height: 20px;
      background: url('../../instanceList/unvisible.png') no-repeat;
      background-size: 20px;
      &:hover {
        background-image: url('../../instanceList/unvisible-hover.png');
      }
    }
  }
  ::v-deep .el-dialog__body {
    padding: 0;
    background-color: #3E4553!important;
  }
  .dialog-footer {
    @include flex-v-c();
    justify-content: flex-end;
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 60px;
    padding-right: 20px;
    box-sizing: border-box;
  }
  .log-box {
    width: 1380px;
    min-height: 600px;
    max-height: 800px;
    overflow-y: auto;
    padding: 10px;
    font-size: 16px;
    color:#bbb;
    word-break: break-all;
    background-color: #1B1E24;
  }
  .tips {
    color: $color-tips;
  }
  .upload-block {
    flex-direction: column;
    height: 200px;
    .tips-text {
      color: #AEBBD9;
      font-size: 20px;
      margin-bottom: 10px;
    }
  }
</style>
