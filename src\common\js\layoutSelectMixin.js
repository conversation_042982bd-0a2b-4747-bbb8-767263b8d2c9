import LayoutSelectDialog from '@/components/bottomTabs/LayoutSelectDialog.vue'
import { mapActions } from 'vuex'

export default {
  components: {
    LayoutSelectDialog
  },

  data() {
    return {
      showLayoutSelectDialog: false,
      currentProgramForLayout: null
    }
  },

  computed: {
    // 获取当前直播比例的字符串形式
    currentLiveRatioString() {
      // 从Vuex获取当前直播比例
      const ratio = this.$store.getters.liveRatio
      if (Math.abs(ratio - 16 / 9) < 0.01) return '16:9'
      if (Math.abs(ratio - 9 / 16) < 0.01) return '9:16'
      if (Math.abs(ratio - 4 / 3) < 0.01) return '4:3'
      if (Math.abs(ratio - 4 / 9) < 0.01) return '4:9'
      if (Math.abs(ratio - 1) < 0.01) return '1:1'
      return '16:9' // 默认值
    }
  },

  methods: {
    ...mapActions(['modProgram']),

    // 显示布局选择弹窗
    showLayoutSelectDialogForProgram(programItem) {
      console.log('显示布局选择弹窗:', programItem)
      this.currentProgramForLayout = { ...programItem }
      this.showLayoutSelectDialog = true
    },

    // 处理布局选择确认
    handleLayoutSelected(layout) {
      console.log('选择的布局:', layout)

      if (!this.currentProgramForLayout) {
        this.$message.error('未找到关联的节目信息')
        return
      }

      // 调用modProgram action更新节目的关联布局
      this.updateProgramLayout(this.currentProgramForLayout, layout.id)
    },

    // 处理布局选择弹窗关闭
    handleLayoutSelectDialogClose() {
      this.showLayoutSelectDialog = false
      this.currentProgramForLayout = null
    },

    // 调用modProgram action更新节目布局
    updateProgramLayout(programItem, layoutId) {
      const params = {
        program: {
          ...programItem,
          layoutId: layoutId
        },
        programType: programItem.type,
      }

      this.modProgram(params).then(() => {
        console.log('更新节目布局成功')
        this.$message.success(`节目 "${programItem.name}" 关联布局成功`)

        // 关闭弹窗
        this.showLayoutSelectDialog = false
        this.currentProgramForLayout = null

      }).catch((error) => {
        console.error('更新节目布局失败:', error)
        this.$message.error('关联布局失败，请重试')
      })
    }
  }
}
