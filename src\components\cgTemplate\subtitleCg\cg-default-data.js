export function DefaultSubtitle (width, height) {
  return {
    dx: 0,
    dy: 0,
    height: height,
    loop: 0,
    id: parseInt(Math.random() * 1000000),
    pgmFlag: false,
    pvwFlag: false,
    obj: {
      layers: []
    },
    startType: 0,
    type: 'subtitle',
    width: width,
    name: ''
  }
}
export function DefaultGif () {
  return {
    dx: 0,
    dy: 0,
    height: 200,
    width: 200,
    loop: 0,
    id: parseInt(Math.random() * 1000000),
    pgmFlag: false,
    pvwFlag: false,
    obj: {
      type: 'gif',
      url: ''
    },
    startType: 0,
    type: 'gif',
    name: ''
  }
}

export function DefaultClock () {
  return {
    dx: 30,
    dy: 30,
    height: 56,
    id: parseInt(Math.random() * 1000000),
    loop: 0,
    obj: {
      boldItalic: '',
      boxColor: 'FFFFFF00',
      color: '#FEDE02',
      dx: 0,
      dy: 0,
      font: 'Microsoft YaHei',
      height: 56,
      position: 5,
      refract: 100,
      size: 42,
      text: '15:04:05',
      type: 'clock',
      width: 165
    },
    pgmFlag: false,
    pvwFlag: false,
    startType: 0,
    type: 'clock',
    width: 165,
    name: ''
  }
}

export function DefeultMove () {
  return {
    dx: 30,
    dy: 30,
    height: 56,
    id: parseInt(Math.random() * 1000000),
    loop: 1, // 单次循环
    obj: {
      boldItalic: '',
      boxColor: 'FFFFFF00',
      color: '#FEDE02',
      dx: 0,
      dy: 0,
      font: 'Microsoft YaHei',
      height: 56,
      position: 5,
      refract: 100,
      size: 42,
      text: '游走字幕',
      type: 'subtitle',
      width: 180,
      speed: 0.1
    },
    pgmFlag: false,
    pvwFlag: false,
    startType: 1,
    type: 'movingSubtitle',
    width: 180,
    name: ''
  }
}

export function DefaultTimekeeping () {
  return {
    id: parseInt(Math.random() * 1000000),
    name: '',
    type: 'timekeeping',
    obj: {
      // "hour:minute:second":时分秒
      // "day hour:minute":天时分
      // "day":天
      text: 'hour:minute:second',
      box: true, // 是否带框
      fontSize: 42,
      fontColor: '#E6ECFBFF',
      boxColor: '#FF8400FF',
      drawBox: [
        {
          dx: 0,
          dy: 0,
          width: 0,
          height: 0
        },
        {
          dx: 0,
          dy: 0,
          width: 0,
          height: 0
        },
        {
          dx: 0,
          dy: 0,
          width: 0,
          height: 0
        }
      ]
    },
    width: 165,
    height: 56,
    dx: 30,
    dy: 30,
    loop: 0
  }
}
export function DefaultWeather () {
  return {
    id: parseInt(Math.random() * 1000000),
    name: '',
    type: 'weather',
    obj: {
      // "hour:minute:second":时分秒
      // "day hour:minute":天时分
      // "day":天
      cityName: '杭州市', // 城市名称
      cityCode: '330100', // 城市编码
      dataType: 'realTime', // "realTime"：即时天气；"lastThreeDays"：未来三天天气
      coordinates: 2, // 位置基准: 0表示右上角；1表示右下角;2表示左上角;3表示左下角
      styleType: 1, // 样式类型
      transparence: 0.6, // 背景透明度
      scale: 1 // 缩放比例
    },
    width: 0,
    height: 0,
    dx: 30,
    dy: 30
  }
}
