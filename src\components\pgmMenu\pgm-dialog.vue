<template>
  <el-dialog :title="editItem.id ? '编辑节目单' : '新增节目单'" :visible.sync="visible" width="32%"
    :before-close="close">
    <div>
      <el-form ref="formRef" :model="form" :rules="rules" label-width="120px">
        <el-form-item label="节目单类型：">
          <el-radio-group v-model="form.type" :disabled="!!editItem.id">
            <el-radio label="loop">定时</el-radio>
            <el-radio label="none">不定时</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="节目单名称：" prop="name">
          <el-input v-model.trim="form.name" :placeholder="'输入节目单名称'" size="small"></el-input>
        </el-form-item>
        <el-form-item label="定时方式：" key="mode" v-if="form.type === 'loop'">
          <el-radio-group v-model="form.mode">
            <el-radio label="daily">每天</el-radio>
            <el-radio label="weekly">周播</el-radio>
            <el-radio label="once">
              指定日期&nbsp;
              <el-tooltip effect="light" content="不同类型节目时间冲突时，播出优先级：指定日期>周播>每天" placement="bottom">
                <svg-icon icon-class="notice"></svg-icon>
              </el-tooltip>
            </el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="时间选择：" prop="daysOfWeek" key="daysOfWeek"
          v-if="form.type === 'loop' && form.mode === 'weekly'">
          <el-checkbox-group v-model="form.daysOfWeek">
            <el-checkbox :label="1">周一</el-checkbox>
            <el-checkbox :label="2">周二</el-checkbox>
            <el-checkbox :label="3">周三</el-checkbox>
            <el-checkbox :label="4">周四</el-checkbox>
            <el-checkbox :label="5">周五</el-checkbox>
            <el-checkbox :label="6">周六</el-checkbox>
            <el-checkbox :label="7">周日</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item label="指定日期：" prop="date" key="date"
          v-if="form.type === 'loop' && form.mode === 'once'">
          <el-date-picker size="small" style="width: 142px" v-model="form.date" type="date"
            placeholder="选择日期" :clearable="false">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="播出时间：" required key="playTime" v-if="form.type === 'loop'">
          <el-time-picker size="small" style="width: 142px" v-model="form.startTime"
            placeholder="开始时间">
          </el-time-picker>
          ——
          <el-time-picker size="small" style="width: 142px" v-model="form.endTime"
            placeholder="结束时间">
          </el-time-picker>
        </el-form-item>
      </el-form>
    </div>
    <span slot="footer" class="dialog-footer">
      <span class="g-option-btn-1" style="width: 84px" @click="confirm">确定</span>
      <span class="g-option-btn-2" style="width: 84px" @click="close">取消</span>
    </span>
  </el-dialog>
</template>

<script>
import liveApi from 'api/live'
import { mapGetters } from 'vuex'
import moment from 'moment'

export default {
  components: {},
  props: {
    editItem: {
      type: Object,
      default: () => ({})
    }
  },
  computed: {
    ...mapGetters(['programsSn'])
  },
  data() {
    return {
      visible: true,
      form: {
        type: 'loop',
        name: '',
        enable: true,
        mode: 'daily',
        daysOfWeek: [],
        date: '',
        startTime: '',
        endTime: ''
      },
      rules: {
        name: [{ required: true, message: '请输入节目单名称', trigger: 'blur' }],
        daysOfWeek: [
          { type: 'array', required: true, message: '一周中请至少选择一天', trigger: 'change' }
        ],
        date: [{ type: 'date', required: true, message: '请选择播出日期', trigger: 'change' }],
        startTime: [
          { type: 'date', required: true, message: '请选择播出开始时间', trigger: 'change' }
        ],
        endTime: [
          { type: 'date', required: true, message: '请选择播出结束时间', trigger: 'change' }
        ]
      }
    }
  },
  methods: {
    confirm() {
      this.$refs.formRef.validate((valid) => {
        if (valid) {
          this.doUpdate()
        } else {
          return false
        }
      })
    },
    doUpdate() {
      const schedule = {
        id: this.$_createRandomId(),
        name: this.form.name,
        enable: this.form.enable,
        isDefault: !!this.editItem.isDefault,
        programs: this.editItem.programs || [],
        scheduleConfig: {
          mode: this.form.type === 'loop' ? this.form.mode : 'none',
          daysOfWeek: [],
          date: '',
          startTime: moment(this.form.startTime).format('HH:mm:ss'),
          duration: moment(this.form.endTime).diff(moment(this.form.startTime), 'seconds')
        }
      }
      if (this.form.type === 'loop') {
        if (this.form.mode === 'weekly') {
          schedule.scheduleConfig.daysOfWeek = this.form.daysOfWeek
        } else if (this.form.mode === 'once') {
          schedule.scheduleConfig.date = moment(this.form.date).format('YYYY-MM-DD')
        }
      } else {
        schedule.scheduleConfig.startTime = '00:00:00'
        schedule.scheduleConfig.duration = 86400
      }

      let api = liveApi.addPgm
      const editExtraParam = {
        id: this.editItem.id
      }
      if (this.editItem.id) {
        api = liveApi.updPgm
        schedule.id = this.editItem.id
      }
      api(
        { schedule, sn: this.programsSn, ...editExtraParam },
        (res) => {
          this.$message.success('操作成功')
          this.$emit('refresh')
          this.$emit('close')
        },
        (code, msg) => {
          this.$message.error(msg)
        }
      )
    },
    close() {
      this.$emit('close')
    }
  },
  created() {
    if (this.editItem.id) {
      this.form.name = this.editItem.name
      this.form.enable = this.editItem.enable
      this.form.mode = this.editItem.scheduleConfig.mode
      if (this.form.mode === 'none') {
        this.form.type = 'none'
        this.form.mode = 'daily'
      }

      if (this.form.type !== 'none') {
        if (this.form.mode === 'weekly') {
          this.form.daysOfWeek = this.editItem.scheduleConfig.daysOfWeek || []
        } else if (this.form.mode === 'once') {
          this.form.date = new Date(this.editItem.scheduleConfig.date || null)
        }
        const timePrefix = moment().format('YYYY-MM-DD')
        this.form.startTime = new Date(timePrefix + ' ' + this.editItem.scheduleConfig.startTime)
        this.form.endTime = new Date(
          moment(this.form.startTime)
            .add(this.editItem.scheduleConfig.duration, 'seconds')
            .format('YYYY-MM-DD HH:mm:ss')
        )
      }
    }
  },
  mounted() {}
}
</script>

<style scoped lang="scss">
</style>
