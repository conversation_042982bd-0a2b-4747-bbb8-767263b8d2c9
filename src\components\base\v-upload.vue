<template>
  <el-upload type="primary" class="v-upload" :action="supportHttps(uploadInfo.uploadUrl)" :on-success="handleSuccess" :on-error="handleError"
             :data="uploadInfo.AuthData" :before-upload="beforeUpload" :show-file-list="false">
    <slot />
  </el-upload>
</template>

<script>
  import Upload from 'common/js/upload'
  import {mapGetters} from 'vuex'

  export default {
    name: 'v-upload',
    props: {
      supportFileType: {
        type: String,
        default: ''
      },
      fileSize: {
        type: Number,
        default: 4
      },
      uploadFileType: {
        type: String,
        default: 'picture'
      },
      isGetResolution: {
        type: Boolean,
        default: false
      }
    },
    computed: {
      ...mapGetters([
        'instanceId'
      ])
    },
    data () {
      return {
        uploadInfo: {
          AuthData: {}, /* 鉴权信息 */
          uploadType: '', /* 文件类型：'':默认空字段显示图片（与picture不同），video:视频, audio: 音频, doc:文件，picture:素材图片（注，与默认的图片不同的是，存储的路径不同，这里主要给素材图片用）  */
          uploadUrl: '/cloud/fullTimeLive/pic/upload?instanceId=', /* 上传链接 */
          visitUrl: '' /* 文件访问链接前缀 */
        }
      }
    },
    methods: {
      handleSuccess (res, file) { /* 上传成功处理 */
        if (file) {
          let Url = ''
          if (this.$_isLocal) {
            Url = res.value.visitUrl
          } else {
            Url = this.uploadInfo.visitUrl + this.uploadInfo.AuthData.key
          }
          this.$emit('uploadUrl', Url, this.uploadInfo)
        }
      },
      handleError (err, file, fileList) {
        if (err) {
          this.$message.error({
            title: '文件上传失败',
            message: file.name + '上传失败。原因：' + err,
            duration: 0,
            offset: 100
          })
        }
      },
      async beforeUpload (file) { /* 上传前校验并获取鉴权信息 */
        let fileType = file.name.substring(file.name.lastIndexOf('.') + 1, file.name.length)
        let uploadType = Upload.getUploadType(fileType)
        this.uploadInfo.uploadType = uploadType
        if (uploadType !== this.uploadFileType) {
          this.$message.error('上传文件格式不支持')
          reject()
        }
        if (this.supportFileType && fileType !== this.supportFileType) {
          this.$message.error('上传文件只支持' + this.supportFileType + '格式')
          reject()
        }
        let isLt = file.size / 1024 / 1024 < this.fileSize
        if (!isLt && this.uploadFileType === 'picture') {
          this.$message.error('上传图片大小不能超过' + this.fileSize + 'MB!')
          reject()
        }
        if (this.uploadFileType === 'picture') {
          let errorMsg = await Upload.verificationPicFile(file)
          if (errorMsg !== '') {
            this.$message.error(errorMsg)
            reject()
          }
        }
        if (this.isGetResolution) {
          return new Promise((resolve, reject) => {
            let _URL = window.URL || window.webkitURL
            let img = new Image()
            img.src = _URL.createObjectURL(file)
            img.onload = () => {
              this.uploadInfo.width = img.width
              this.uploadInfo.height = img.height
              resolve()
            }
          }).then(() => {
            if (this.$_isLocal) {
              return null
            } else {
              return Upload.getImgPromise(this.uploadInfo, file)
            }
          }, () => {
          })
        } else {
          if (this.$_isLocal) {
            return null
          } else {
            return Upload.getImgPromise(this.uploadInfo, file)
          }
        }
      }
    },
    created () {
      this.uploadInfo.uploadUrl += this.instanceId
    }
  }
</script>

<style scoped lang="scss">
  .v-upload {
  }
</style>
