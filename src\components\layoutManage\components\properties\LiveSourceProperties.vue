<template>
  <div class="live-source-properties">
    <!-- 拉流地址 -->
    <div class="property-section">
      <div class="section-title">拉流地址</div>
      <div class="property-group">
        <div class="url-input-section">
          <div class="supported-formats">
            支持RTMP、RTSP、HLS、HTTP、HTTPS、SRT的地址
          </div>
          <el-input
            v-model="localUrl"
            placeholder="请输入拉流地址"
            type="textarea"
            :rows="2"
          />
          <div style="text-align: right;margin-top: 10px;">
            <el-button type="info" @click="handleUrlConfirm" size="small">确认</el-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 位置大小 -->
    <PositionSizeProperties
      :component="component"
      :canvas-size="canvasSize"
      @update="handleUpdate"
    />

    <!-- 画面显示 -->
    <DisplayModeProperties
      :component="component"
      @update="handleUpdate"
    />

    <!-- 音频调节 -->
    <VolumeProperties
      :component="component"
      @update="handleUpdate"
    />
  </div>
</template>

<script>
import PositionSizeProperties from './common/PositionSizeProperties.vue'
import DisplayModeProperties from './common/DisplayModeProperties.vue'
import VolumeProperties from './common/VolumeProperties.vue'

export default {
  name: 'LiveSourceProperties',
  components: {
    PositionSizeProperties,
    DisplayModeProperties,
    VolumeProperties
  },
  props: {
    component: {
      type: Object,
      required: true
    },
    canvasSize: {
      type: Object,
      default: () => ({ width: 1920, height: 1080 })
    }
  },
  data() {
    return {
      localUrl: ''
    }
  },
  watch: {
    component: {
      handler() {
        this.syncLocalValues()
      },
      immediate: true
    }
  },
  methods: {
    syncLocalValues() {
      this.localUrl = this.component.url || ''
    },
    handleUpdate(data) {
      this.$emit('update', data)
    },
    handleUrlConfirm() {
      // 拉流地址校验
      if (!this.localUrl) {
        this.$message.error('请输入拉流地址')
        return
      }
      if (!this.localUrl.startsWith('rtmp://') && !this.localUrl.startsWith('rtsp://') && !this.localUrl.startsWith('hls://') && !this.localUrl.startsWith('http://') && !this.localUrl.startsWith('https://') && !this.localUrl.startsWith('srt://')) {
        this.$message.error('请输入正确的拉流地址')
        return
      }
      this.$emit('update', { url: this.localUrl })
    }
  }
}
</script>

<style lang="scss" scoped>
.live-source-properties {
  .property-section {
    margin-bottom: 20px;

    .section-title {
      font-size: 14px;
      font-weight: 500;
      margin-bottom: 12px;
      padding: 0 16px;
    }
  }

  .property-group {
    margin-bottom: 16px;
    padding: 0 16px;
  }

  .url-input-section {
    .supported-formats {
      font-size: 12px;
      margin-bottom: 8px;
      color: #7A8195;
    }

    :deep(.el-textarea) {
      .el-textarea__inner {
        background: #1C1E23;
        resize: none;

        &::placeholder {
          color: #999999;
        }
      }
    }
  }

  .volume-control {
    display: flex;
    align-items: center;
    gap: 12px;

    :deep(.el-slider) {
      flex: 1;

      .el-slider__runway {
        background: #4a4a4a;
      }

      .el-slider__bar {
        background: #ff6b35;
      }

      .el-slider__button {
        border-color: #ff6b35;
      }
    }

    .volume-input {
      width: 80px;

      :deep(.el-input-number) {
        width: 100%;

        .el-input__inner {
          color: #ffffff;
          height: 28px;
          line-height: 28px;
        }
      }
    }
  }
}
</style>
