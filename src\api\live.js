import Server from 'common/js/request'
import store from 'store/index'
const CryptoJS = require('crypto-js')

let instance = null
let VERSION = '1.1'

let localConfig = {
  isLocal: true,
  secretKey: '',
  liveId: '8008208820',
  instanceIp: '************'
}
const getAuthToken = function (timestamp) {
  if (localConfig.isLocal) {
    let time = store.getters.localTimeDifference + new Date().getTime()
    let content = time + '_' + localConfig.liveId
    return time + '@' + CryptoJS.HmacSHA1(content, '').toString(CryptoJS.enc.Base64)
  } else {
    return instance.$_getAuthToken(new Date().getTime())
  }
}
export default {
  bind: function (vue) {
    instance = vue
  },
  /**
   * 节目单25.8
   */
  // 查询节目单列表
  getPgmList: function (param, successCall, failCall) {
    param.token = getAuthToken()
    param.version = VERSION
    Server.requestJson('/cli/querySchedules?instanceId=' + store.getters.instanceId, param, successCall, failCall, true)
  },
  // 新增节目单
  addPgm: function (param, successCall, failCall) {
    param.token = getAuthToken()
    param.version = VERSION
    Server.requestJson('/cli/addSchedule?instanceId=' + store.getters.instanceId, param, successCall, failCall)
  },
  // 删除节目单
  removePgm: function (param, successCall, failCall) {
    param.token = getAuthToken()
    param.version = VERSION
    Server.requestJson('/cli/removeSchedule?instanceId=' + store.getters.instanceId, param, successCall, failCall)
  },
  // 更新节目单（启用、禁用、添加节目、删除节目）
  updPgm: function (param, successCall, failCall) {
    param.token = getAuthToken()
    param.version = VERSION
    Server.requestJson('/cli/modSchedule?instanceId=' + store.getters.instanceId, param, successCall, failCall)
  },
  /**
   * 直播管理25.8
   */
  // 查询直播列表
  getLiveList: function (param, successCall, failCall) {
    Server.request('/cloud/livestream/list?instanceId=' + store.getters.instanceId, param, successCall, failCall, true)
  },
  // 新建/修改直播
  updateLive: function (param, successCall, failCall) {
    Server.requestJson('/cloud/livestream/saveOrUpdate?instanceId=' + store.getters.instanceId, param, successCall, failCall, true)
  },
  // 删除直播
  removeLive: function (param, successCall, failCall) {
    Server.request('/cloud/livestream/delete?instanceId=' + store.getters.instanceId, param, successCall, failCall, true)
  },
  // 重新拉流
  retryPullLive: function (param, successCall, failCall) {
    Server.request('/cloud/livestream/retryPull?instanceId=' + store.getters.instanceId, param, successCall, failCall, true)
  },
  // 禁播
  banLive: function (param, successCall, failCall) {
    Server.request('/cloud/livestream/banLive?instanceId=' + store.getters.instanceId, param, successCall, failCall, true)
  },
  // 恢复禁播
  retrieveLive: function (param, successCall, failCall) {
    Server.request('/cloud/livestream/retrieveLive?instanceId=' + store.getters.instanceId, param, successCall, failCall, true)
  },
  /**
   * 音频播单25.8
   */
  // 音频播单列表查询
  getAudioPlayList: function (param, successCall, failCall) {
    Server.request('/cloud/fullTimeLive/audioPlaylist/list?instanceId=' + store.getters.instanceId, param, successCall, failCall, true)
  },
  // 创建音频播单
  addAudioPlay: function (param, successCall, failCall) {
    param.token = getAuthToken()
    param.pwd = store.getters.pwd
    Server.requestJson('/cloud/fullTimeLive/audioPlaylist/add?instanceId=' + store.getters.instanceId, param, successCall, failCall, true)
  },
  // 编辑音频播单
  updAudioPlay: function (param, successCall, failCall) {
    Server.requestJson('/cloud/fullTimeLive/audioPlaylist/update?instanceId=' + store.getters.instanceId, param, successCall, failCall, true)
  },
  // 删除音频播单
  delAudioPlay: function (param, successCall, failCall) {
    param.pwd = store.getters.pwd
    Server.requestJson('/cloud/fullTimeLive/audioPlaylist/delete?instanceId=' + store.getters.instanceId, param, successCall, failCall, true)
  },
  // 音频播单排序调整
  sortAudioPlay: function (param, successCall, failCall) {
    param.pwd = store.getters.pwd
    Server.requestJson('/cloud/fullTimeLive/audioPlaylist/order?instanceId=' + store.getters.instanceId, param, successCall, failCall, true)
  },
  // 音频播单排序调整
  removeItemFromPlay: function (param, successCall, failCall) {
    param.pwd = store.getters.pwd
    Server.requestJson('/cloud/fullTimeLive/audioPlaylist/removeAudioFromPlaylist?instanceId=' + store.getters.instanceId, param, successCall, failCall, true)
  },
  /**
   * 获取局域网内的ndi源
   */
  // 查询局域网内ndi源
  getNdiList: function (param, successCall, failCall) {
    param.token = getAuthToken()
    param.version = VERSION
    Server.requestJson('/cli/queryNdiSources?instanceId=' + store.getters.instanceId, param, successCall, failCall, true)
  },
  /**
   * 硬件声卡设备获取
   */
  // 获取当前设备声卡名称
  getAudioDevice: function (param, successCall, failCall) {
    param.token = getAuthToken()
    param.version = VERSION
    Server.requestJson('/cli/querySoundDevices?instanceId=' + store.getters.instanceId, param, successCall, failCall, true)
  },
  // 设置设备声卡
  setAudioDevice: function (param, successCall, failCall) {
    param.token = getAuthToken()
    param.version = VERSION
    Server.requestJson('/cli/setSoundDevice?instanceId=' + store.getters.instanceId, param, successCall, failCall, true)
  },
  getConfig: function (param, successCall, failCall) {
    param.token = getAuthToken()
    param.version = VERSION
    Server.requestJson('/cli/getCfg?instanceId=' + store.getters.instanceId, param, successCall, failCall, true)
  },
  setBgm: function (param, successCall, failCall) {
    param.token = getAuthToken()
    param.version = VERSION
    Server.requestJson('/cli/setBgm?instanceId=' + store.getters.instanceId, param, successCall, failCall)
  },
  deleteProgram: function (param, successCall, failCall) {
    param.token = getAuthToken()
    param.version = VERSION
    Server.requestJson('/cli/removeProgram?instanceId=' + store.getters.instanceId, param, successCall, failCall)
  },
  queryPrograms: function (param, successCall, failCall) {
    param.token = getAuthToken()
    param.version = VERSION
    Server.requestJson('/cli/queryPrograms?instanceId=' + store.getters.instanceId, param, successCall, failCall, true)
  },
  addProgram: function (param, successCall, failCall) {
    param.token = getAuthToken()
    param.version = VERSION
    Server.requestJson('/cli/addPrograms?instanceId=' + store.getters.instanceId, param, successCall, failCall)
  },
  setSafeVideo: function (param, successCall, failCall) {
    param.token = getAuthToken()
    param.version = VERSION
    Server.requestJson('/cli/setSafeVideo?instanceId=' + store.getters.instanceId, param, successCall, failCall)
  },
  setDelay: function (param, successCall, failCall) {
    param.token = getAuthToken()
    param.version = VERSION
    Server.requestJson('/cli/setDelay?instanceId=' + store.getters.instanceId, param, successCall, failCall)
  },
  nextProgram: function (param, successCall, failCall) {
    param.token = getAuthToken()
    param.version = VERSION
    Server.requestJson('/cli/nextProgram?instanceId=' + store.getters.instanceId, param, successCall, failCall)
  },
  urgency: function (param, successCall, failCall) {
    param.token = getAuthToken()
    param.version = VERSION
    Server.requestJson('/cli/urgency?instanceId=' + store.getters.instanceId, param, successCall, failCall)
  },
  resume: function (param, successCall, failCall) {
    param.token = getAuthToken()
    param.version = VERSION
    Server.requestJson('/cli/resume?instanceId=' + store.getters.instanceId, param, successCall, failCall)
  },
  startLive: function (param, successCall, failCall) {
    param.token = getAuthToken()
    param.version = VERSION
    Server.requestJson('/cli/startLive?instanceId=' + store.getters.instanceId, param, successCall, failCall)
  },
  stopLive: function (param, successCall, failCall) {
    param.token = getAuthToken()
    param.version = VERSION
    Server.requestJson('/cli/stopLive?instanceId=' + store.getters.instanceId, param, successCall, failCall)
  },
  addCg: function (param, successCall, failCall) {
    param.token = getAuthToken()
    param.version = VERSION
    Server.requestJson('/cli/addCg?instanceId=' + store.getters.instanceId, param, successCall, failCall)
  },
  setCg: function (param, successCall, failCall) {
    param.token = getAuthToken()
    param.version = VERSION
    Server.requestJson('/cli/setCg?instanceId=' + store.getters.instanceId, param, successCall, failCall)
  },
  selectCg: function (param, successCall, failCall) {
    param.token = getAuthToken()
    param.version = VERSION
    Server.requestJson('/cli/selectCg?instanceId=' + store.getters.instanceId, param, successCall, failCall)
  },
  deSelectCg: function (param, successCall, failCall) {
    param.token = getAuthToken()
    param.version = VERSION
    Server.requestJson('/cli/deselectCg?instanceId=' + store.getters.instanceId, param, successCall, failCall)
  },
  removeCg: function (param, successCall, failCall) {
    param.token = getAuthToken()
    param.version = VERSION
    Server.requestJson('/cli/removeCg?instanceId=' + store.getters.instanceId, param, successCall, failCall)
  },
  setSoundMixer: function (param, successCall, failCall) {
    param.token = getAuthToken()
    param.version = VERSION
    Server.requestJson('/cli/setSoundMixer?instanceId=' + store.getters.instanceId, param, successCall, failCall)
  },
  getPollData: function (urlSnippet, param, successCall, failCall, instanceId) {
    let newParam = {token: getAuthToken(), version: VERSION, sdp: btoa(JSON.stringify(param))}
    Server.requestJson('/cli/sdp/' + urlSnippet + '?instanceId=' + (instanceId || store.getters.instanceId), newParam, successCall, failCall)
  },
  getChannelVideo: function (urlSnippet, param, successCall, failCall, instanceId) {
    let newParam = {token: getAuthToken(), version: VERSION, sdp: btoa(JSON.stringify(param))}
    Server.requestJson('/cli/sdp/' + urlSnippet + '?instanceId=' + instanceId || store.getters.instanceId, newParam, successCall, failCall)
  },
  getPreviewVideo: function (param, sdp, successCall, failCall) {
    let newParam = {token: getAuthToken(), version: VERSION, sdp: btoa(JSON.stringify(sdp)), program: param.program, audio: param.audio, cgs: param.cgs, defaultPic: param.defaultPic}
    Server.requestJson('/cli/preview?instanceId=' + store.getters.instanceId, newParam, successCall, failCall)
  },
  setRepeat: function (param, successCall, failCall) {
    param.token = getAuthToken()
    param.version = VERSION
    Server.requestJson('/cli/setRepeat?instanceId=' + store.getters.instanceId, param, successCall, failCall)
  },
  setBackground: function (param, successCall, failCall) {
    param.token = getAuthToken()
    param.version = VERSION
    Server.requestJson('/cli/setBackground?instanceId=' + store.getters.instanceId, param, successCall, failCall)
  },
  setDefaultPic: function (param, successCall, failCall) {
    param.token = getAuthToken()
    param.version = VERSION
    Server.requestJson('/cli/setDefaultPic?instanceId=' + store.getters.instanceId, param, successCall, failCall)
  },
  modProgram: function (param, successCall, failCall) {
    param.token = getAuthToken()
    param.version = VERSION
    Server.requestJson('/cli/modProgram?instanceId=' + store.getters.instanceId, param, successCall, failCall)
  },
  setOutput: function (param, successCall, failCall) {
    param.token = getAuthToken()
    param.version = VERSION
    Server.requestJson('/cli/setOutput?instanceId=' + store.getters.instanceId, param, successCall, failCall)
  },
  clearCgsReq: function (param, successCall, failCall) {
    param.token = getAuthToken()
    param.version = VERSION
    Server.requestJson('/cli/clearCgs?instanceId=' + store.getters.instanceId, param, successCall, failCall)
  },
  queryOutputsReq: function (param, successCall, failCall) {
    param.token = getAuthToken()
    param.version = VERSION
    Server.requestJson('/cli/queryOutputs?instanceId=' + store.getters.instanceId, param, successCall, failCall)
  },
  removeHistoryProgram: function (param, successCall, failCall) {
    param.token = getAuthToken()
    param.version = VERSION
    Server.requestJson('/cli/removePastPgm?instanceId=' + store.getters.instanceId, param, successCall, failCall)
  },
  getLog: function (successCall, failCall) {
    Server.getText('/cli/log?instanceId=' + store.getters.instanceId, successCall, failCall)
  },
  getHaikangSetting: function (param, successCall, failCall) {
    param.token = getAuthToken()
    param.version = VERSION
    Server.request('/cloud/fullTimeLive/haikang/setting/get?instanceId=' + store.getters.instanceId, param, successCall, failCall)
  },
  setHaikangSetting: function (param, successCall, failCall) {
    param.token = getAuthToken()
    param.version = VERSION
    Server.requestJson('/cloud/fullTimeLive/haikang/setting/save?instanceId=' + store.getters.instanceId, param, successCall, failCall)
  },
  getHaikangList: function (param, successCall, failCall) {
    param.token = getAuthToken()
    param.version = VERSION
    Server.requestJson('/cloud/fullTimeLive/haikang/camera/list?instanceId=' + store.getters.instanceId, param, successCall, failCall)
  },
  getHaikangUrl: function (param, successCall, failCall) {
    param.token = getAuthToken()
    param.version = VERSION
    Server.requestJson('/cloud/fullTimeLive/haikang/camera/url?instanceId=' + store.getters.instanceId, param, successCall, failCall)
  },
  cutProgram: function (param, successCall, failCall) {
    param.token = getAuthToken()
    param.version = VERSION
    Server.requestJson('/cli/cutProgram?instanceId=' + store.getters.instanceId, param, successCall, failCall)
  },
  checkTodayProgram: function (param, successCall, failCall) {
    param.token = getAuthToken()
    param.version = VERSION
    Server.requestJson('/cli/checkConflictProgram?instanceId=' + store.getters.instanceId, param, successCall, failCall)
  },
  searchCity: function (param, successCall, failCall) {
    Server.requestGet('/cloud/fullTimeLive/weather/getCity', param, successCall, failCall)
  },
  getLayouts: function (param, successCall, failCall) {
    param.token = getAuthToken()
    param.version = VERSION
    Server.requestJson('/cli/queryLayouts?instanceId=' + store.getters.instanceId, param, successCall, failCall)
  },
  addLayout: function (param, successCall, failCall) {
    param.token = getAuthToken()
    param.version = VERSION
    param.sn = store.getters.programsSn
    Server.requestJson('/cli/addLayout?instanceId=' + store.getters.instanceId, param, successCall, failCall)
  },
  updateLayout: function (param, successCall, failCall) {
    param.token = getAuthToken()
    param.version = VERSION
    param.sn = store.getters.programsSn
    Server.requestJson('/cli/modLayout?instanceId=' + store.getters.instanceId, param, successCall, failCall)
  },
  deleteLayout: function (param, successCall, failCall) {
    param.token = getAuthToken()
    param.version = VERSION
    param.sn = store.getters.programsSn
    Server.requestJson('/cli/removeLayout?instanceId=' + store.getters.instanceId, param, successCall, failCall)
  },
  useLayout: function (param, successCall, failCall) {
    param.token = getAuthToken()
    param.version = VERSION
    param.sn = store.getters.programsSn
    Server.requestJson('/cli/selectLayout?instanceId=' + store.getters.instanceId, param, successCall, failCall)
  }
}
