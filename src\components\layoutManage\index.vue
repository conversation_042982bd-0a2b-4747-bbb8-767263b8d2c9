<template>
  <div class="layout-manage">
    <!-- 顶部操作栏 -->
    <div class="header-toolbar">
      <div class="left-actions">
        <el-button type="warning" icon="el-icon-plus" size="small" @click="handleCreateLayout">
          新建布局
        </el-button>
      </div>
      <div class="right-actions">
        <el-input
          v-model="searchKeyword"
          placeholder="输入搜索关键字"
          class="search-input"
          clearable
          size="small"
          @input="handleSearch"
        >
          <template #append>
            <el-button icon="el-icon-search" size="small" @click="handleSearch">搜索</el-button>
          </template>
        </el-input>
      </div>
    </div>

    <!-- 滚动容器 -->
    <div class="scroll-container" v-loading="loading" element-loading-text="正在加载布局列表...">
      <!-- 布局网格 -->
      <div v-if="!loading" class="layout-grid">
        <LayoutItem
          v-for="layout in paginatedLayouts"
          :key="layout.id"
          :layout-data="layout"
          @edit="handleEditLayout"
          @delete="handleDeleteLayout"
        />
        <!-- 用于一行不够4个的情况 -->
        <div class="filler" v-for="i in 4" :key="i + 'filler'"></div>
      </div>

      <!-- 空状态 -->
      <div v-if="!loading && filteredLayouts.length === 0" class="empty-state">
        <div class="empty-content">
          <i class="el-icon-folder-opened empty-icon"></i>
          <p class="empty-text">暂无布局数据</p>
          <el-button type="primary" @click="handleCreateLayout">创建第一个布局</el-button>
        </div>
      </div>
    </div>

    <!-- 分页组件 -->
    <footer-pagination
      v-if="filteredLayouts.length > 0"
      :page="pageInfo"
      @getPageInfo="handlePageChange"
    />

    <!-- 新建布局弹窗 -->
    <CreateLayoutDialog
      :visible.sync="showCreateDialog"
      @confirm="handleCreateConfirm"
      @close="handleCreateClose"
    />

    <!-- 布局编辑器 -->
    <LayoutEditor
      :visible.sync="showLayoutEditor"
      :ratio="selectedRatio"
      :layout-data="editingLayout"
      @save-layout="handleSaveLayoutData"
      @close="handleEditorClose"
    />
  </div>
</template>

<script>
import LayoutItem from './layoutItem.vue'
import footerPagination from '@/components/pagination/footer-pagination'
import CreateLayoutDialog from './createLayoutDialog.vue'
import LayoutEditor from './layoutEditor.vue'
import Live from '@/api/live'
import { mapGetters, mapActions } from 'vuex'

export default {
  name: 'LayoutManage',
  components: {
    LayoutItem,
    footerPagination,
    CreateLayoutDialog,
    LayoutEditor
  },
  data() {
    return {
      searchKeyword: '',
      showCreateDialog: false, // 控制新建弹窗显示
      showLayoutEditor: false, // 控制布局编辑器显示
      selectedRatio: '16:9', // 选择的比例
      editingLayout: null, // 正在编辑的布局数据
      loading: false, // 加载状态
      pageInfo: {
        pageIndex: 1,
        amount: 0,
        pageSize: 12, // 每页显示12个布局项
        pageTotal: 0
      }
    }
  },
  mounted() {
    // 组件挂载后加载布局列表
    this.loadLayoutsData()
    this.$bus.$on('edit-layout', this.handleEditLayout)
  },
  computed: {
    ...mapGetters(['layouts']),
    filteredLayouts() {
      if (!this.searchKeyword.trim()) {
        return this.layouts
      }
      return this.layouts.filter(layout =>
        layout.name.toLowerCase().includes(this.searchKeyword.toLowerCase())
      )
    },
    paginatedLayouts() {
      const start = (this.pageInfo.pageIndex - 1) * this.pageInfo.pageSize
      const end = start + this.pageInfo.pageSize
      return this.filteredLayouts.slice(start, end)
    }
  },
  methods: {
    ...mapActions(['loadLayouts', 'addLayoutToStore', 'updateLayoutInStore', 'deleteLayoutFromStore']),
    // 加载布局列表
    async loadLayoutsData() {
      this.loading = true
      try {
        await this.loadLayouts()
        console.log('获取布局列表成功')
      } catch (error) {
        console.error('加载布局列表异常:', error)
        this.$message.error('获取布局列表失败')
      } finally {
        this.loading = false
      }
    },

    handleCreateLayout() {
      console.log('打开新建布局弹窗')
      this.showCreateDialog = true
    },

    async handleDeleteLayout(layoutData) {
      console.log('删除布局:', layoutData)

      try {
        // 调用删除API
        await new Promise((resolve, reject) => {
          Live.deleteLayout({ layoutId: layoutData.id }, (response) => {
            console.log('删除布局成功:', response)
            this.$message.success(`删除布局成功`)
            resolve(response)
          }, (code, msg) => {
            console.error('删除布局失败:', code, msg)
            this.$message.error(msg)
            resolve(null)
          })
        })
        // 从Vuex store中移除
        this.deleteLayoutFromStore(layoutData.id)
      } catch (error) {
          console.error('删除布局异常:', error.msg)
          this.$message.error('删除布局失败')
      }
    },
    handleSearch() {
      console.log('搜索关键字:', this.searchKeyword)
      // 搜索时重置到第一页
      this.pageInfo.pageIndex = 1
      this.updatePageInfo()
    },
    handlePageChange(page) {
      this.pageInfo.pageIndex = page || this.pageInfo.pageIndex
      console.log('切换到第', this.pageInfo.pageIndex, '页')
    },
    updatePageInfo() {
      // 更新分页信息
      this.pageInfo.amount = this.filteredLayouts.length
      this.pageInfo.pageTotal = Math.ceil(this.filteredLayouts.length / this.pageInfo.pageSize)
    },
    handleCreateConfirm(data) {
      console.log('创建新布局:', data)
      // 保存选择的比例并打开布局编辑器
      this.selectedRatio = data.ratio
      this.editingLayout = null // 新建模式
      this.showLayoutEditor = true
    },
    handleCreateClose() {
      console.log('关闭新建布局弹窗')
    },
    handleEditLayout(layoutData) {
      console.log('编辑布局:', layoutData)
      // 设置编辑模式并打开布局编辑器
      this.selectedRatio = layoutData.ratio
      this.editingLayout = { ...layoutData } // 创建副本避免直接修改
      this.showLayoutEditor = true
    },
    handleSaveLayoutData(savedLayoutData) {
      console.log('接收到保存的布局数据:', savedLayoutData)

      try {
        if (this.editingLayout) {
          // 编辑模式：更新Vuex store中的数据
          this.updateLayoutInStore(savedLayoutData)
          this.$message.success('布局更新成功')
        } else {
          // 新建模式：添加到Vuex store
          this.addLayoutToStore(savedLayoutData)
          this.$message.success('新布局创建成功')
          this.pageInfo.pageIndex = 1
        }

        // 关闭编辑器
        this.showLayoutEditor = false
      } catch (error) {
        console.error('处理保存布局数据异常:', error)
        this.$message.error('处理布局数据失败')
      }
    },
    handleEditorClose() {
      console.log('关闭布局编辑器')
      this.editingLayout = null
    }
  },
  watch: {
    filteredLayouts: {
      handler() {
        this.updatePageInfo()
      },
      immediate: true
    }
  }
}
</script>

<style lang="scss" scoped>
.layout-manage {
  padding-top: 15px;
  height: 100%;
  box-sizing: border-box;
  padding-bottom: 0;
  display: flex;
  flex-direction: column;
}

.header-toolbar {
  padding: 0 15px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  flex-shrink: 0;

  .left-actions {
    .el-button {
      background: #ff6b35;
      border-color: #ff6b35;

      &:hover {
        background: #ff5722;
        border-color: #ff5722;
      }
    }
  }

  .right-actions {
    .search-input {
      width: 327px;

      :deep(.el-input__inner) {
        background: #555D70;
        border-color: #606060;
        color: #ffffff;

        &::placeholder {
          color: #999999;
        }
      }

      :deep(.el-input-group__append) {
        background: #555D70;

        .el-button {
          background: #555D70;
          color: #ffffff;

          &:hover {
            background: $color-theme;
          }
        }
      }
    }
  }
}

.scroll-container {
  padding: 0 15px;
  flex: 1;
  overflow-y: auto;
}

.layout-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  justify-content: space-between;
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;

  .empty-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 400px;
    color: #666;
    gap: 16px;

    .empty-icon {
      font-size: 64px;
      color: #c0c4cc;
    }

    .empty-text {
      font-size: 14px;
      color: #999999;
      margin: 0;
    }
  }
}

// 分页组件样式
:deep(.footer-pagination) {
  margin: 0;
  flex-shrink: 0;
  box-shadow: 0px -6px 4px 0px rgba(0,0,0,0.14);
}
.filler {
  width: 280px;
  height: 0;
}
</style>
