<template>
  <div class="main-screen-renderer">
    <div class="preview-placeholder">
      <i class="el-icon-video-camera"></i>
      <span>主画面</span>
    </div>
  </div>
</template>

<script>
export default {
  name: 'MainScreenRenderer',
  props: {
    component: {
      type: Object,
      required: true
    },
    isSelected: {
      type: Boolean,
      default: false
    }
  }
}
</script>

<style lang="scss" scoped>
.main-screen-renderer {
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.preview-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: rgba(0, 150, 255, 0.1);
  border: 1px dashed rgba(0, 150, 255, 0.3);
  color: #0096ff;
  font-size: 12px;

  i {
    font-size: 24px;
    margin-bottom: 4px;
  }
}
</style>
