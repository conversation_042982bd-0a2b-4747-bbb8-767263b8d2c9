html, body, #app {
  height: 100%;
  width: 100%;
  overflow: hidden;
}
body {
  margin: 0;
  font-size: 14px;
  font-family: "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "微软雅黑", Arial, sans-serif;
  color: $color-text;
  background: #fff;
}
h1, h2, h3, h4, h5, h6, p {
  margin: 0;
}
ul, ol, dl, dt, dd {
  margin: 0;
  padding: 0;
}
li {
  list-style: none;
}
a {
  text-decoration: none;
}
img {
  display: inline-block;
  border: none;
}
i {
  font-style: normal;
}
:focus {
  outline: none;
}
@media screen and (max-width: 1440px) {
  html {
    font-size: 14px
  }
  .hide-1440 {
    display: none;
  }
}
@media screen and (min-width: 1440px) and (max-width: 1680px) {
  html {
    font-size: 15px
  }
}
@media screen and (min-width: 1680px) and (max-width: 1800px) {
  html {
    font-size: 16px
  }
}
@media screen and (min-width: 1800px) and (max-width: 1920px) {
  html {
    font-size: 17px
  }
}
@media screen and (min-width: 1920px) {
  html {
    font-size: 18px
  }
}
#app {
  position: relative;
}
::-webkit-scrollbar { /* 血槽宽度 */
  width: 8px;
  height: 8px;
}
::-webkit-scrollbar-thumb { /* 拖动条 */
  background-color: rgba(245, 245, 245, .3);
  border-radius: 6px;
}
::-webkit-scrollbar-track { /* 背景槽 */
  background-color: #2D3039;
}
/* ======原子类======== */
.w100 {
  width: 100%;
}
.h100 {
  height: 100%;
}
.wh100 {
  width: 100%;
  height: 100%;
}
.flex-c-c {
  display: flex;
  align-items: center;
  justify-content: center;
}
.flex-v-c {
  display: flex;
  align-items: center;
}
.bdr-50 {
  border-radius: 50%;
  overflow: auto;
}
.img-cover {
  display: block;
  width: 100%;
  height: 100%;
}
.g-option-btn {
  padding: 6px 14px;
  margin: 0 6px;
  border: 1px solid rgba(79, 86, 103, 1);
  border-radius: 4px;
  font-size: 13px;
  cursor: pointer;
  font-family: 宋体;
  &:hover {
    color: #FC4F08;
    border-color: #FC4F08;
  }
  .icon {
    margin-right: 6px;
  }
}
.g-option-btn-1 {
  display: inline-block;
  height: 34px;
  line-height: 34px;
  text-align: center;
  margin: 0 6px;
  background-color: #FC4F08;
  border: 1px solid #FC4F08;
  border-radius: 4px;
  font-size: 14px;
  color: #ffffff;
  box-sizing: border-box;
  cursor: pointer;
  font-family: 宋体;
  user-select: none;
  &:hover {
    opacity: .8;
  }
  .icon {
    margin-right: 6px;
  }
}
.g-option-btn-2 {
  display: inline-block;
  height: 34px;
  line-height: 34px;
  text-align: center;
  margin: 0 6px;
  background-color: #535D72;
  border: 1px solid #535D72;
  border-radius: 4px;
  font-size: 14px;
  color: #BCC9E8;
  cursor: pointer;
  font-family: 宋体;
  box-sizing: border-box;
  user-select: none;
  &:hover {
    opacity: .8;
  }
  .icon {
    margin-right: 6px;
  }
}
.g-option-btn-3 {
  display: inline-block;
  height: 34px;
  line-height: 34px;
  text-align: center;
  margin: 0 6px;
  background-color: #A9AEB9;
  border: 1px solid #A9AEB9;
  border-radius: 4px;
  font-size: 14px;
  color: #ffffff;
  box-sizing: border-box;
  cursor: no-drop;
  font-family: 宋体;
  user-select: none;
  .icon {
    margin-right: 6px;
  }
}
.g-hide-scroll {
  &::-webkit-scrollbar {
    width: 0 !important
  }
  -ms-overflow-style: none;
  overflow: -moz-scrollbars-none;
  scrollbar-width: none;
}
/* ====== 全局样式设置 ======= */
/deep/ .el-divider--vertical {
  background-color: $color-divider;
}
/deep/ .el-form-item__label {
  color: #A8B7D2;
}
/deep/ .el-dialog {
  border-radius: $border-radius;
  overflow: hidden;
}
/deep/ .el-dialog__header {
  display: flex;
  padding-top: 10px;
  background-color: $color-bg-header;
  .el-dialog__title {
    font-size: 16px;
  }
  .el-dialog__close {
    position: relative;
    bottom: 10px;
    right: -8px;
    font-size: 24px;
    color: $color-text;
  }
}
/deep/ .el-dialog__body,
/deep/ .el-dialog__footer {
  background-color: $color-content-bg;
}
/deep/ .el-input__inner {
  background-color: #14181F !important;
  border-color: #14181F !important;
  color: $color-text;
  &::placeholder {
    color: #404755;
  }
}
/deep/ .el-input-number__decrease,
/deep/ .el-input-number__increase {
  background-color: #424957;
  color: #fff;
}
.el-tooltip__popper {
  font-size: $font-size;
  &.is-light {
    background: #FFFFFF!important;
    color: #222222;
  }
}
.el-tooltip:focus {
  outline: none;
}
.el-radio__inner {
  background-color: #232831;
  border-color: #586174;
}
/deep/ .el-radio {
  color: $color-text;
}
// 按钮样式
.my-operate-btn {border-radius: 14px;width: 73px;height: 28px;padding: 0;margin-left: 0!important;}
/deep/ .el-button--danger.my-operate-btn{background: #4f5867;border-color:#4f5867;color: #C2D1EE}
/deep/ .el-button--danger.my-operate-btn:hover{background: $color-theme;border-color:$color-theme;color: #ffffff}
/deep/ .el-button--danger.my-operate-btn:active{opacity: .8}

::v-deep .el-button--success {background: #535d72!important;border-color:#535d72!important;color: #ffffff!important}
::v-deep .el-button--success:hover {background: #535d72!important;border-color:#535d72!important;color: #ffffff; opacity: .8}
::v-deep .el-button--success:active {background: #535d72!important;border-color:#535d72!important;color: #ffffff}

// 表格
/deep/ .el-table {
  background-color: $color-content-bg !important;
  &:before {
    background-color: $color-border !important;
  }
  &.el-table--border,
  th.is-leaf,
  &.el-table--group {
    border-color: $color-border !important;
    &:after {
      border-color: $color-border !important;
      background-color: $color-border !important;
    }
  }
  tr, th, td,
  .el-table__empty-block {
    background-color: $color-content-bg !important;
    border-color: $color-border;
    color: $color-text;
  }
  th {
    background-color: #454D5C!important;
  }
}
// 复选框样式
::v-deep{
  .el-checkbox__input.is-checked .el-checkbox__inner, .el-checkbox__input.is-indeterminate .el-checkbox__inner{
    background: #FC4F08;
    border: 1px solid #FC4F08;
  }
  .el-checkbox__input.is-checked + .el-checkbox__label {
    color: #FC4F08;
  }
  .el-checkbox.is-bordered.is-checked{
    border: 1px solid #FC4F08;
  }
  .el-checkbox__input.is-focus .el-checkbox__inner{
    border: 1px solid #FC4F08;
  }
  .el-checkbox__input .el-checkbox__inner{
    background: #1A1D23;
    border: 1px solid #586072;
  }
  .el-checkbox__input.is-disabled.is-checked .el-checkbox__inner {
    background-color: #333843!important;
    border-color: #3c424f!important;
  }
  .el-checkbox__input.is-disabled.is-checked .el-checkbox__inner::after {
    border-color: #74453a;
  }
}
::v-deep {
  .v-pagination.is-background .el-pager li {
    /*对页数的样式进行修改*/
    background-color: #535D72;
    color: #B9C2DA;
  }

  .v-pagination.is-background .btn-next {
    /*对下一页的按钮样式进行修改*/
    background-color: #535D72;
    color: #B9C2DA;
  }

  .v-pagination.is-background .btn-prev {
    /*对上一页的按钮样式进行修改*/
    background-color: #535D72;
    color: #B9C2DA;
  }

  .v-pagination.is-background .el-pager li:not(.disabled).active {
    /*当前选中页数的样式进行修改*/
    background-color: #FF5712;
    color: #ffffff;
  }
}
.my-grey-btn {
  display: flex;
  justify-content: center;
  align-items: center;
  color: #BCC6DC;
  padding: 7px 20px;
  height: 32px;
  background-color: #656E7F;
  border-radius: 3px;
  cursor: pointer;
  box-sizing: border-box;

  &:hover {
    background-color: #BCC6DC;
    color: #656E7F;
  }
}
.lineThrough {
  text-decoration:line-through
}
.videoBg {
  position: absolute;
  top: 0;
  z-index: 1;
  object-fit: cover;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
}

/deep/ .el-popper .popper__arrow {
  display: none;
}
.el-popper {
  margin: 0!important;
}
.el-popper /deep/ .popper__arrow {
  visibility: hidden;
}
.el-popover {
  min-width: 0px!important;
}
.dark-slider {
  &.el-popover {
    background-color: #3D465B;
    border-color: #465067;
    .popper__arrow::after {
      border-bottom-color: #3D465B;
    }
  }
  &.el-popper .popper__arrow {
    border-bottom-color: #3D465B;
  }
  .el-slider__runway {
    background-color: #535D72;
  }
}
.time-slider {
  padding: 12px 0;

  .el-slider.is-vertical .el-slider__runway {
    width: 4px;
  }
  .el-slider.is-vertical .el-slider__bar {
    width: 4px;
  }
  .el-slider__button {
    width: 7px;
    height: 7px;
    border: 5px solid #fff;
    background-color: #FF5B0C;
  }
  .el-slider.is-vertical .el-slider__button-wrapper {
    left: -17px
  }
}
.view-pop {
  ::-webkit-scrollbar-thumb { /* 拖动条 */
    background-color: #B5BBCA;
    border-radius: 6px;
  }
  ::-webkit-scrollbar-track { /* 背景槽 */
    background-color: #fff;
  }
}