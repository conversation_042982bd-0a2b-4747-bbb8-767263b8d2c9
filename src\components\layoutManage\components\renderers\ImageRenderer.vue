<template>
  <div class="image-renderer">
    <div
      v-if="component.url"
      class="image-background"
      :style="imageBackgroundStyle"
    ></div>
    <div v-else class="image-placeholder" :style="placeholderStyle">
      <i class="el-icon-picture-outline"></i>
      <span>图片</span>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ImageRenderer',
  props: {
    component: {
      type: Object,
      required: true
    },
    isSelected: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    imageBackgroundStyle() {
      // 将 objectFit 值转换为对应的 backgroundSize 值
      const getBackgroundSize = (objectFit) => {
        switch (objectFit) {
          case 'contain':
            return 'contain'
          case 'cover':
            return 'cover'
          case 'fill':
            return '100% 100%'
          default:
            return 'contain'
        }
      }

      const style = {
        width: '100%',
        height: '100%',
        backgroundImage: `url(${this.component.url})`,
        backgroundSize: getBackgroundSize(this.component.objectFit),
        backgroundPosition: 'center',
        backgroundRepeat: 'no-repeat'
      }

      if (this.component.opacity !== undefined) {
        style.opacity = this.component.opacity / 100
      }

      // 应用高斯模糊效果
      if (this.component.gaussianBlur && this.component.gaussianBlurRadius > 0) {
        style.filter = `blur(${this.component.gaussianBlurRadius}px)`
      }

      return style
    },
    placeholderStyle() {
      const style = {}

      if (this.component.opacity !== undefined) {
        style.opacity = this.component.opacity / 100
      }

      // 对占位符也应用模糊效果
      if (this.component.gaussianBlur && this.component.gaussianBlurRadius > 0) {
        style.filter = `blur(${this.component.gaussianBlurRadius}px)`
      }

      return style
    }
  }
}
</script>

<style lang="scss" scoped>
.image-renderer {
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.image-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: rgba(230, 162, 60, 0.1);
  border: 1px dashed rgba(230, 162, 60, 0.3);
  color: #e6a23c;
  font-size: 12px;

  i {
    font-size: 24px;
    margin-bottom: 4px;
  }
}

img {
  display: block;
}
</style>
