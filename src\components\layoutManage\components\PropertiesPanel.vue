<template>
  <div
    class="properties-panel"
    v-shortkey="{ delete: ['del'] }"
    @shortkey="handleShortkey"
  >
    <div v-if="!component" class="no-selection">
      <div class="no-selection-content">
        <i class="el-icon-info"></i>
        <p>请选择一个组件</p>
      </div>
    </div>

    <div v-else class="properties-content">
      <!-- 组件标题 -->
      <div class="properties-header">
        <div class="properties-header-left">
          <h3>{{ component.name || '组件' }}</h3>
          <svg-icon icon-class="edit" @click.native="handleEditName" />
        </div>
        <svg-icon icon-class="delete" @click.native="handleDelete" v-if="!component.disableDelete" />
      </div>

      <!-- 主画面属性 -->
      <MainScreenProperties
        v-if="component.type === 'main'"
        :component="component"
        :canvas-size="canvasSize"
        @update="handleUpdate"
      />

      <!-- 直播源属性 -->
      <LiveSourceProperties
        v-else-if="component.type === 'stream'"
        :component="component"
        :canvas-size="canvasSize"
        @update="handleUpdate"
      />

      <!-- 点播源属性 -->
      <VodSourceProperties
        v-else-if="component.type === 'video'"
        :component="component"
        :canvas-size="canvasSize"
        @update="handleUpdate"
      />

      <!-- 背景属性 -->
      <BackgroundProperties
        v-else-if="component.type === 'bg'"
        :component="component"
        :canvas-size="canvasSize"
        @update="handleUpdate"
      />

      <!-- 图片属性 -->
      <ImageProperties
        v-else-if="component.type === 'logo'"
        :component="component"
        :canvas-size="canvasSize"
        @update="handleUpdate"
      />

      <!-- 文字属性 -->
      <TextProperties
        v-else-if="component.type === 'subtitle'"
        :component="component"
        :canvas-size="canvasSize"
        @update="handleUpdate"
      />

      <!-- 网页属性 -->
      <WebpageProperties
        v-else-if="component.type === 'web'"
        :component="component"
        :canvas-size="canvasSize"
        @update="handleUpdate"
      />

      <!-- 节目单属性 -->
      <ProgramProperties
        v-else-if="component.type === 'program'"
        :component="component"
        :canvas-size="canvasSize"
        @update="handleUpdate"
      />

      <!-- 未知组件 -->
      <div v-else class="unknown-properties">
        <p>未知组件类型: {{ component.type }}</p>
      </div>
    </div>
  </div>
</template>

<script>
import MainScreenProperties from './properties/MainScreenProperties.vue'
import LiveSourceProperties from './properties/LiveSourceProperties.vue'
import VodSourceProperties from './properties/VodSourceProperties.vue'
import BackgroundProperties from './properties/BackgroundProperties.vue'
import ImageProperties from './properties/ImageProperties.vue'
import TextProperties from './properties/TextProperties.vue'
import WebpageProperties from './properties/WebpageProperties.vue'
import ProgramProperties from './properties/ProgramProperties.vue'

export default {
  name: 'PropertiesPanel',
  components: {
    MainScreenProperties,
    LiveSourceProperties,
    VodSourceProperties,
    BackgroundProperties,
    ImageProperties,
    TextProperties,
    WebpageProperties,
    ProgramProperties
  },
  props: {
    component: {
      type: Object,
      default: null
    },
    canvasSize: {
      type: Object,
      default: () => ({ width: 1920, height: 1080 })
    }
  },
  methods: {
    handleUpdate(updatedData) {
      if (this.component) {
        const updatedComponent = {
          ...this.component,
          ...updatedData
        }
        this.$emit('update-component', updatedComponent)
      }
    },
    handleEditName() {
      this.$prompt('请输入新的名称', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        inputValidator: (value) => {
          if (!value) {
            return '请输入名称'
          }
          return true
        },
        inputErrorMessage: '请输入名称',
        inputPlaceholder: '请输入名称',
        inputValue: this.component.name
      }).then(({ value }) => {
        this.handleUpdate({ name: value })
      })
    },
    handleDelete() {
      this.$confirm('确定要删除该组件吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$emit('delete-component', this.component)
      })
    },
    handleShortkey(event) {
      const { srcKey } = event

      if (srcKey === 'delete') {
        // 只有当有选中组件且不是禁用删除的组件时才允许删除
        if (this.component && !this.component.disableDelete) {
          event.preventDefault()
          this.handleDelete()
        }
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.properties-panel {
  height: 100%;
  background: #323743;
  color: #BEC9E5;
}

.no-selection {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;

  .no-selection-content {
    text-align: center;

    i {
      font-size: 48px;
      margin-bottom: 16px;
      display: block;
    }

    p {
      margin: 0;
      font-size: 14px;
    }
  }
}

.properties-content {
  height: 100%;
  overflow-y: auto;
}

.properties-header {
  padding: 12px 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;

  .properties-header-left {
    display: flex;
    align-items: center;
    gap: 10px;
  }

  h3 {
    margin: 0;
    font-size: 16px;
    font-weight: 500;
  }
  svg {
    cursor: pointer;
    &:hover {
      color: $color-theme;
    }
  }
}

.unknown-properties {
  padding: 16px;
  text-align: center;
  color: #999999;

  p {
    margin: 0;
    font-size: 14px;
  }
}

// 滚动条样式
:deep(.properties-content) {
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: #2a2a2a;
  }

  &::-webkit-scrollbar-thumb {
    background: #5a5a5a;
    border-radius: 3px;

    &:hover {
      background: #6a6a6a;
    }
  }
}
</style>
