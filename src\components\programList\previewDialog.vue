<template>
  <el-dialog title="预览画面" :visible.sync="showDialog" top="80px" :close-on-click-modal="false" @close="$emit('close')" :width="videoWidth + 40 + 'px'">
    <div class="video-area" :class="{ratioEqual: ratioEqual}" ref="videoArea" :style="{width: videoWidth + 'px'}">
      <video id="preview" class="video wh100" autoplay controls disablePictureInPicture></video>
    </div>
  </el-dialog>
</template>

<script>
  import {mapGetters} from 'vuex'
  import ChannelVideo from 'common/js/channel-preview'
  import webrtcMgr from 'common/js/webrtc-manager'

  export default {
    name: 'previewDialog',
    props: {
      currentProgramItem: {
        type: Object,
        default: () => {}
      }
    },
    computed: {
      getPoster () {
        return require('@/common/img/video-placeholder.png')
      },
      ...mapGetters([
        'liveRatio',
        'defaultPic',
        'cgs',
        'audio'
      ])
    },
    data () {
      return {
        pgmEle: null,
        showDialog: true,
        video: null,
        videoWidth: 0,
        ratioEqual: true
      }
    },
    methods: {
      programListInit () {
        let that = this
        let program = this.$_deepCopy(this.currentProgramItem)
        program.startTime = null
        let cgs = this.$_deepCopy(this.cgs)
        cgs.forEach(item => {
          if (item.usedType === 1) {
            item.selected = false
          }
          if (program.cgIds && program.cgIds.includes(item.id)) {
            item.selected = true
          }
        })
        cgs.reverse()
        setTimeout(() => {
          this.video = new ChannelVideo('preview', program, this.audio, this.defaultPic, cgs, (res) => {
            if (res.width / res.height > 1 && that.liveRatio > 1) {
              that.ratioEqual = false
            }
            if (res.width / res.height < 1 && that.liveRatio < 1) {
              that.ratioEqual = true
            }
            if (res.width / res.height > 1 && that.liveRatio < 1) {
              that.ratioEqual = false
            }
            if (res.width / res.height < 1 && that.liveRatio > 1) {
              that.ratioEqual = false
            }
          }, (msg) => {
            this.$message.error(msg)
          })
          this.videoWidth = this.$refs.videoArea.offsetHeight * this.liveRatio
        }, 100)
      }
    },
    mounted () {
      this.pgmEle = document.getElementById('pgm')
      this.pgmEle.muted = true
      this.programListInit()
    },
    beforeDestroy () {
      this.pgmEle.muted = false
      webrtcMgr.destroyAllPreview()
    }
  }
</script>

<style scoped lang="scss">
  .video-area {
    height: 540px;
    video {
      width:100%;
      height:100%;
      object-fit: contain;
      background-color: #000;
    }
    &.ratioEqual {
      video {
        object-fit: cover;
      }
    }
    //全屏按钮
    video::-webkit-media-controls-fullscreen-button {
      display: none;
    }
    //进度条
    video::-webkit-media-controls-timeline {
      display: none;
    }
    //观看的当前时间
    video::-webkit-media-controls-current-time-display{
      display: none;
    }
    //剩余时间
    video::-webkit-media-controls-time-remaining-display {
      display: none;
    }
    //音量的控制条
    video::-webkit-media-controls-volume-slider {
      display: none;
    }
    video::-webkit-media-controls-volume-control-hover-background {
      background-color: transparent;
    }
  }
</style>
