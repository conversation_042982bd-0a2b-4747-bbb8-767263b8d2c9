<template>
  <el-dialog title="上传音频" width="300px" top="70px" :visible.sync="showDialog" :close-on-click-modal="false"
             @close="$emit('close')">
    <el-upload
            :action="uploadUrl"
            :before-upload="beforeUpload"
            :multiple="true"
            :limit="10"
            :on-exceed="handleExceed"
            :on-success="handleSuccess"
            :on-remove="handleRemove"
            :on-error="handleError">
      <el-button slot="trigger" size="small" type="success">上传音频</el-button>
      <el-button style="margin-left: 10px;" size="small" type="primary" @click="submitUpload">确认添加</el-button>
      <div slot="tip" class="el-upload__tip" style="color: #B5BBCA;margin-bottom: 10px">支持上传,MP3/M4A/WAV文件</div>
    </el-upload>
  </el-dialog>
</template>

<script>
  import Upload from 'common/js/upload'
  import cloudApi from 'api/cloud'
import {mapGetters} from 'vuex'

  export default {
    name: 'upload-audio',
    props: {
      uploadFileType: {
        type: String,
        default: 'audio'
      }
    },
    computed: {
      ...mapGetters([
        'instanceId'
      ])
    },
    data() {
      return {
        fileList: [],
        uploadUrl: '/cloud/fullTimeLive/audio/upload?instanceId=',
        uploadDisable: true,
        showDialog: true
      }
    },
    methods: {
      submitUpload () {
        if (!this.fileList.length) {
          return this.$message.warning('请先上传音频')
        }
        let fileList = []
        this.fileList.forEach(item => {
          fileList.push({
            type: 0,
            fileName: item.name,
            visitUrl: item.visitUrl,
            duration: item.duration,
            format: item.format,
            fileSize: item.fileSize
          })
        })
        cloudApi.addAudioBatch({audios: fileList}, () => {
          this.$message.success('添加成功')
          this.$emit('uploadCallback')
          this.$emit('close')
        })
      },
      handleError () {
        this.$message.error('上传失败')
      },
      handleRemove (file) {
        let index = this.fileList.findIndex(item => item.uid === file.uid)
        this.fileList.splice(index, 1)
      },
      handleSuccess (res, file, fileList) {
        let fileInfo = {
          fileName: '',
          visitUrl: '',
          duration: 0,
          format: 'mp3',
          fileSize: 0
        }
        if (res) {
          fileInfo.duration = res.value.duration
          fileInfo.visitUrl = res.value.visitUrl
          fileInfo.name = file.name
          fileInfo.format = file.name.substring(file.name.lastIndexOf('.') + 1, file.name.length)
          fileInfo.fileSize = file.size
          fileInfo.uid = file.uid
          this.fileList.push(fileInfo)
        }
      },
      async beforeUpload (file) {
        let checkStorage = await this.getFileStorage(file)
        if (!checkStorage) {
          this.$message.warning('存储不足，无法上传')
          reject()
        }
        let fileType = file.name.substring(file.name.lastIndexOf('.') + 1, file.name.length)
        let uploadType = Upload.getUploadType(fileType)
        if (uploadType !== this.uploadFileType) {
          this.$message.error('上传文件格式不支持')
          reject()
        }
        var errorMsg = Upload.checkFileSize(uploadType, file.size)
        if (errorMsg !== '') {
          this.$message.error(errorMsg)
          reject()
        }
        this.uploadDisable = true
      },
      getFileStorage (file) {
        return new Promise(resolve => {
          cloudApi.getInstanceInfo({}, res => {
            if (file.size + res.currentVolume > res.maxVolume) {
              resolve(false)
            }
            resolve(true)
          }, () => {
            resolve(false)
          })
        })
      },
      handleExceed(files, fileList) {
        this.$message.warning(`当前限制选择 10 个文件，本次选择了 ${files.length} 个文件，共选择了 ${files.length + fileList.length} 个文件`)
      }
    },
    created () {
      this.uploadUrl = this.uploadUrl + this.instanceId
    }
  }
</script>

<style scoped lang="scss">
  ::v-deep .el-upload-list__item-name {
    color: #B5BBCA;
  }
</style>
