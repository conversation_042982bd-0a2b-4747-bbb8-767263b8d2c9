import store from 'store/index'

let moment = require('moment')
const CryptoJS = require('crypto-js')

let addDirective = function (Vue) {
  Vue.directive('title', {
    inserted: function (el, binding) {
      document.title = binding.value
    }
  })
}

let addFilter = function (Vue) {
  // 注册全局filter。 {{ time | formatDateCustom}} 或者 {{item.createTime | formatDate('YYYY-MM-DD')}} 可格式化
  Vue.filter('formatDateCustom', function (time) {
    if (!time) {
      return ''
    }
    return moment(time).format('YYYY-MM-DD HH:mm:ss')
  })
  Vue.filter('formatDateNoyear', function (time) {
    if (!time) {
      return ''
    }
    return moment(time).format('MM-DD HH:mm:ss')
  })
  Vue.filter('formatTimeCustom', function (time) {
    if (!time) {
      return ''
    }
    return moment(time).format('HH:mm')
  })
  Vue.filter('formatDate', function (time, format) {
    if (!time) {
      return ''
    }
    return moment(time).format(format)
  })
  Vue.filter('formatSize', function (size) {
    let sizeNum = Number(size)
    if (!sizeNum || sizeNum <= 0) {
      return '未知'
    }
    if (sizeNum < 1048576) {
      return (sizeNum / 1024).toFixed(2) + 'KB'
    }
    if (sizeNum < 1073741824) {
      return (sizeNum / 1048576).toFixed(2) + 'M'
    }
    return (sizeNum / 1073741824).toFixed(2) + 'G'
  })
  Vue.filter('formatDurationMs', function (duration) {
    let durationNum = Number(duration)
    if (!durationNum || durationNum <= 0) {
      return '未知'
    }
    durationNum = Math.floor(durationNum / 1000)
    let hour
    let minute
    let second
    if (durationNum >= 3600) {
      hour = Math.floor(durationNum / 3600)
      durationNum = durationNum - hour * 3600
      if (hour >= 10) {
        hour = hour + ''
      } else {
        hour = '0' + hour
      }
    } else {
      hour = '00'
    }
    if (durationNum >= 60) {
      minute = Math.floor(durationNum / 60)
      durationNum = durationNum - minute * 60
      if (minute >= 10) {
        minute = minute + ''
      } else {
        minute = '0' + minute
      }
    } else {
      minute = '00'
    }
    if (durationNum > 0) {
      second = durationNum
      if (second >= 10) {
        second = second + ''
      } else {
        second = '0' + second
      }
    } else {
      second = '00'
    }
    return hour + ':' + minute + ':' + second
  })
  Vue.filter('formatDuration', function (duration) {
    let durationNum = Number(duration)
    if (!durationNum || durationNum <= 0) {
      return '未知'
    }
    let hour
    let minute
    let second
    if (durationNum >= 3600) {
      hour = Math.floor(durationNum / 3600)
      durationNum = durationNum - hour * 3600
      if (hour >= 10) {
        hour = hour + ''
      } else {
        hour = '0' + hour
      }
    } else {
      hour = '00'
    }
    if (durationNum >= 60) {
      minute = Math.floor(durationNum / 60)
      durationNum = durationNum - minute * 60
      if (minute >= 10) {
        minute = minute + ''
      } else {
        minute = '0' + minute
      }
    } else {
      minute = '00'
    }
    if (durationNum > 0) {
      second = durationNum
      if (second >= 10) {
        second = second + ''
      } else {
        second = '0' + second
      }
    } else {
      second = '00'
    }
    return hour + ':' + minute + ':' + second
  })
  Vue.filter('formatConsumeFee', function (fee) {
    if (fee < 0) {
      return '+' + Math.abs(fee)
    }
    return '-' + fee
  })
  Vue.filter('formatCombineState', function (state) {
    if (state === -2 || state === 0) {
      return '未合并'
    }
    if (state === -1) {
      return '合并中'
    }
    if (state === -3) {
      return '合并失败'
    }
    if (state === 2) {
      return '合并成功'
    }
    return ''
  })
}

let addPrototypeMethod = function (Vue) {
  /* 将auth.js中的方法放到全局中，方便使用  */
  Vue.prototype.showTabControl = function (authIds, needAuthIds) {
    if (needAuthIds === undefined || needAuthIds.length === 0) {
      return true
    }
    for (let i in needAuthIds) {
      if (this.contains(authIds, needAuthIds[i])) {
        return true
      }
    }
    return false
  }
  /* ============= 分页page初始化 =========== */
  Vue.prototype.Page = function () {
    return {
      pageIndex: 1,
      amount: 0,
      pageSize: 20
    }
  }
  /* ================ page赋值 ============== */
  Vue.prototype.GetPage = function (val, res) {
    let amount = Number(res.amount)
    let pageIndex = Number(res.pageIndex)
    let pageSize = Number(res.pageSize)
    let pageTotal = Number(res.pageTotal)
    if (amount) {
      val.page.amount = amount
    } else {
      val.page.amount = 0
    }
    if (pageIndex) {
      val.page.pageIndex = pageIndex
    } else {
      val.page.pageIndex = 0
    }
    if (pageTotal) {
      val.page.pageTotal = pageTotal
    } else {
      val.page.pageTotal = 0
    }
    if (pageSize) {
      val.page.pageSize = pageSize
    } else {
      val.page.pageSize = 20
    }
  }
  // 设置cookie
  Vue.prototype.$_setCookie = function (name, value, day = 30) {
    let exp = new Date()
    exp.setTime(exp.getTime() + day * 24 * 60 * 60 * 1000)
    document.cookie = name + '=' + escape(value) + ';expires=' + exp.toGMTString()
  }
  // 得到cookie
  Vue.prototype.$_getCookie = function (name) {
    let reg = new RegExp('(^| )' + name + '=([^;]*)(;|$)')
    let arr = window.document.cookie.match(reg)
    if (arr != null) {
      return unescape(arr[2])
    } else {
      return null
    }
  }
  // 删除cookie
  Vue.prototype.$_deleteCookie = function (name) {
    let now = new Date()
    now.setTime(now.getTime() - 1)
    document.cookie = name + '=' + '' + '; expires = ' + now.toGMTString()
  }
  // 判断小数点的位数
  Vue.prototype.$_getNumber = function (num) {
    let str = num.toString()
    let result = str.split('.')
    if (result[1]) {
      return result[1].length
    } else {
      return 0
    }
  }
  Vue.prototype.$_createRandomId = function () {
    return (Math.random() * 10000000).toString(16).substr(0, 4) + '-' + (new Date()).getTime() + '-' + Math.random().toString().substr(2, 5)
  }
  /* ============== JS获取n至m随机整数 =============== */
  Vue.prototype.$_randomNum = function (n, m) {
    let c = m - n + 1
    return Math.floor(Math.random() * c + n)
  }
  /* ============== JS获取n至m随机整数 =============== */
  Vue.prototype.$_getAuthToken = function (timestamp) {
    let time = timestamp + store.getters.timeDifference
    let content = time + '_' + store.getters.liveId
    return time + '@' + CryptoJS.HmacSHA1(content, store.getters.secretKey).toString(CryptoJS.enc.Base64)
  }
  /* =============== 深拷贝 ============= */
  Vue.prototype.$_deepCopy = function (obj) {
    if (!obj) {
      return obj
    }
    let str
    let newobj = obj.constructor === Array ? [] : {}
    if (typeof obj !== 'object') {
      return
    } else if (window.JSON) {
      str = JSON.stringify(obj) // 系列化对象
      newobj = JSON.parse(str) // 还原
    } else {
      for (var i in obj) {
        newobj[i] = typeof obj[i] === 'object' ? this.$_deepCopy(obj[i]) : obj[i]
      }
    }
    return newobj
  }
  /* =============== videojs 不用视频格式的sources赋值 ============= */
  Vue.prototype.$_setVideo = function (videoUrl) {
    if (videoUrl === undefined || videoUrl === null) {
      return {}
    }
    let sources = {}
    sources.src = videoUrl.replace('http://', '//')
    if (videoUrl.indexOf('rtmp') === 0) {
      sources.type = 'rtmp/mp4'
    } else if (videoUrl.substring(videoUrl.length - 3, videoUrl.length) === 'flv') {
      sources.type = 'video/x-flv'
    } else if (videoUrl.substring(videoUrl.length - 4, videoUrl.length) === 'm3u8') {
      sources.type = 'application/x-mpegURL'
      sources.withCredentials = false
    } else {
      sources.type = 'video/mp4'
    }
    return sources
  }
  // 兼容https协议
  Vue.prototype.supportHttps = function (url) {
    if (url === undefined || url === null) {
      return ''
    }
    return url.replace('http://', '//')
  }
  Vue.prototype.$_getQueryString = function (name) {
    let reg = new RegExp('(^|&)' + name + '=([^&]*)(&|$)', 'i')
    let r = window.location.search.substr(1).match(reg)
    if (r != null) return unescape(decodeURI(r[2]))
    return null
  }
  Vue.prototype.$_toRGBA = function (color, opacity) {
    return 'rgba(' + parseInt(color.substring(1, 3), 16) + ',' + parseInt(color.substring(3, 5), 16) + ',' +
      parseInt(color.substring(5, 7), 16) + ',' + opacity / 100 + ')'
  }
  Vue.prototype.$_parseOpacity = function (opacity) {
    let temp = Math.round(Number(opacity) * 2.55).toString(16)
    if (temp.length === 1) {
      temp = '0' + temp
    }
    return temp
  }
  Vue.prototype.$_createRandomId = function () {
    return (Math.random() * 10000000).toString(16).substr(0, 4) + '-' + (new Date()).getTime() + '-' + Math.random().toString().substr(2, 5)
  }
  Vue.prototype.$_getTextAlign = function (position) {
    if ([1, 4, 7].includes(position)) return 'left'
    if ([2, 5, 8].includes(position)) return 'center'
    return 'right'
  }
  Vue.prototype.$_getVerticalAlign = function (position) {
    if ([1, 2, 3].includes(position)) return 'top'
    if ([4, 5, 6].includes(position)) return 'middle'
    return 'bottom'
  }
  Vue.prototype.$_getPosition = function (textAlign, verticalAlign) {
    let posVal = 2
    if (textAlign === 'left') {
      posVal = 1
    } else if (textAlign === 'right') {
      posVal = 3
    }
    if (verticalAlign === 'middle') {
      posVal = posVal + 3
    } else if (verticalAlign === 'bottom') {
      posVal = posVal + 6
    }
    return posVal
  }
  Vue.prototype.$_colorRgba = function (str) {
    let color = '#' + str.substring(0, 6)
    let opacity = Math.round(parseInt(str.substring(6, 8), 16) / 2.55) / 100
    let sColor = color.toLowerCase()
    let rgba = ''
    if (sColor.length === 4) {
      let sColorNew = '#'
      for (let i = 1; i < 4; i += 1) {
        sColorNew += sColor.slice(i, i + 1).concat(sColor.slice(i, i + 1))
      }
      sColor = sColorNew
    }
    // 处理六位的颜色值
    let sColorChange = []
    for (let i = 1; i < 7; i += 2) {
      sColorChange.push(parseInt('0x' + sColor.slice(i, i + 2)))
    }
    rgba = 'rgba(' + sColorChange.join(',') + ',' + opacity + ')'
    return rgba
  }
  Vue.prototype.$_compareVersion = function(version1, version2) {
    const newVersion = `${version1}`.split('.').length < 3 ? `${version1}`.concat('.0') : `${version1}`
    const currentVersion = `${version2}`.split('.').length < 3 ? `${version2}`.concat('.0') : `${version2}`
    function toNum(a) {
      const c = a.toString().split('.')
      const numPlace = ['', '0', '00', '000', '0000']
      const r = numPlace.reverse()
      for (let i = 0; i < c.length; i++) {
        const len = c[i].length
        c[i] = r[len] + c[i]
      }
      return c.join('')
    }
    function checkPlugin(a, b) {
      const numA = toNum(a)
      const numB = toNum(b)
      return numA > numB ? 1 : numA < numB ? -1 : 0
    }
    return checkPlugin(newVersion, currentVersion)
  }
  Vue.prototype.$_getRelativePath = function(url) {
    let arrUrl = url.split('//')

    let start = arrUrl[1].indexOf('/')
    let relUrl = arrUrl[1].substring(start)

    if (relUrl.indexOf('?') !== -1) {
      relUrl = relUrl.split('?')[0]
    }
    return relUrl
  }
  Vue.prototype.$_getPicture = function (url) {
    if (url.includes('http')) {
      return url
    } else if (!url) {
      return ''
    }
    return location.origin + url
  }
  // 设置本地localstorage
  Vue.prototype.$_setLocalStorage = function (name, content) {
    localStorage.setItem(name, JSON.stringify(content))
  }
  // 删除本地localstorage
  Vue.prototype.$_deleteLocalStorage = function (name) {
    localStorage.removeItem(name)
  }
  // 获取本地localstorage
  Vue.prototype.$_getLocalStorage = function (name) {
    let data = localStorage.getItem(name)
    if (data) {
      return JSON.parse(data)
    }
    return ''
  }
  Vue.prototype.$_getSizeFormat = function (size) {
    let sizeNum = Number(size)
    if (!sizeNum || sizeNum <= 0) {
      return '0'
    }
    if (sizeNum < 1048576) {
      return (sizeNum / 1024).toFixed(2) + 'KB'
    }
    if (sizeNum < 1073741824) {
      return (sizeNum / 1048576).toFixed(2) + 'MB'
    }
    return (sizeNum / 1073741824).toFixed(2) + 'GB'
  }
}
// let instance = null
export default {
  install (Vue, options) {
    addDirective(Vue)
    addFilter(Vue)
    addPrototypeMethod(Vue)
  }
}
