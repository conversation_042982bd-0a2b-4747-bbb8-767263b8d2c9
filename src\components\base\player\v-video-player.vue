<template>
  <div class="video-ma" v-if="showVideoPlayer">
    <div class="video-player-wrapper">
      <div class="header">
        <div class="name">{{name}}</div>
        <div class="close" @click="close">
          <i class="el-icon-close"></i>
        </div>
      </div>
      <div class="video-content-wrapper">
        <div class="video-content" :class="{'is-audio': isAudio}">
          <div class="play-error" v-show="!canPlay">
            <span>音频因格式不支持或者服务器或者网络的问题无法加载</span>
          </div>
<!--          <audio controls :src="playUrl" v-if="isAudio" class="audio-controls" ref="audioRef">-->
<!--          </audio>-->
          <video-player class="wh100 vjs-custom-skin" style="height: 100%" :options="playerOptions" :playsinline="true" @error="error"></video-player>
        </div>
      </div>
    </div>
  </div>
</template>

<script>

  export default {
    name: 'v-video-player',
    data () {
      return {
        playUrl: '',
        isAudio: false,
        errorIndex: 1,
        showVideoPlayer: false,
        dialogVisible: true,
        canPlay: true,
        name: '',
        playerOptions: {
          language: 'zh-CN',
          preload: 'auto',
          autoplay: true,
          sources: [],
          sourceOrder: true,
          techOrder: ['html5', 'flash'],
          playbackRates: [0.5, 1, 1.5, 2, 3],
          flash: {hls: {withCredentials: false}, swf: '/static/video-js.swf'},
          html5: {hls: {withCredentials: false}},
          notSupportedMessage: '音频因格式不支持或者服务器或者网络的问题无法加载'
        }
      }
    },
    methods: {
      error (e) {
        this.canPlay = false
        if (e.error_.code === 3 && this.errorIndex === 1) {
          this.errorIndex++
          this.playerOptions.techOrder = ['flash']
          this.playerOptions.autoplay = true
          console.log(this.playerOptions)
        }
      },
      close () {
        this.isAudio = false
        this.showVideoPlayer = false
        this.canPlay = true
      }
    },
    mounted () {
      this.$bus.$on('getVideoUrl', (data) => {
        if (data.type === 'audio') {
          setTimeout(() => {
            this.isAudio = true
          }, 200)
          this.name = data.name || '音频播放'
        } else {
          this.isAudio = false
          this.name = data.name || '视频播放'
        }
        let url = this.$_setVideo(data.videoUrl)
        this.playUrl = data.videoUrl
        console.log(data.videoUrl)
        this.playerOptions.sources = [url]
        this.showVideoPlayer = true
      })
    }
  }
</script>

<style scoped lang="scss">
  .video-ma {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 5000;
    display: flex;
    justify-content: center;
    background-color: rgba(0, 0, 0, .5);
    .video-player-wrapper {
      position: absolute;
      width: 50%;
      margin-top: 5%;
      ::v-deep .vjs-control-bar {
        display: flex;
      }
      .header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        width: 100%;
        height: 45px;
        padding: 0 10px;
        box-sizing: border-box;
        background-color: #444a5a;
        .name {
          color: #fff;
        }
        .close {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 30px;
          height: 30px;
          cursor: pointer;
          color: #fff;
        }
      }
      ::v-deep .video-js {
        height: 100%;
      }
      .video-content-wrapper {
        position: relative;
        width: 100%;
        padding-bottom: 56.25%;
        .video-content {
          position: absolute;
          left: 0;
          top: 0;
          width: 100%;
          height: 100%;
          .audio-controls {
            position: absolute;
            width: 100%;
            bottom: 0;
            border-radius: 0!important;
          }
          &.is-audio {
            background: url("./audio-porter.jpg") no-repeat center/cover !important;
            ::v-deep .vjs-tech {
              background: url("./audio-porter.jpg") no-repeat center/cover !important;
            }
          }
          ::v-deep .vjs-tech {
            background: none;
            &:focus {
              outline: none;
            }
          }
        }
      }
    }
  }
  ::v-deep .video-js {
    background: url("./audio-porter.jpg") no-repeat center/cover !important;
  }
  ::v-deep .vjs-tech {
    display: none;
  }
  ::v-deep .vjs-modal-dialog-content {
    display: none;
  }
  .play-error {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 100;
    text-align: center;
    span {
      display: inline-block;
      color: #ffffff;
      margin-top: 20px;
      font-size: 15px;
    }
  }
</style>
