const webpack = require('webpack')
const path = require('path')

function resolve (dir) {
  return path.join(__dirname, dir)
}

let publicPath = process.env.VUE_APP_BASE_URL || ''

module.exports = {
  css: {
    loaderOptions: {
      stylus: {
        'resolve url': true,
        'import': [
          './src/entry/H5/theme'
        ]
      },
      sass: {
        data: `
          @import "src/common/scss/mixin.scss";
          @import "src/common/scss/_global.scss";
        `
      }
    }
  },
  pluginOptions: {
    'element-ui': {
      postCompile: true,
      theme: true
    },
    'cube-ui': {
      postCompile: true,
      theme: true
    }
  },
  devServer: {
    port: 9092,
    proxy: {
      '/cloud': {
        target: 'http://172.16.10.184',
        changeOrigin: true
      },
      '/data': {
        target: 'http://172.16.10.184',
        changeOrigin: true
      },
      '/cli': {
        target: 'http://172.16.10.184',
        changeOrigin: true
      }
    }
  },
  configureWebpack: {
    devtool: 'source-map'
  },
  chainWebpack (config) {
    config.resolve.alias
      .set('@', resolve('src'))
      .set('components', resolve('src/components'))
      .set('componentsH5', resolve('src/componentsH5'))
      .set('common', resolve('src/common'))
      .set('api', resolve('src/api'))
      .set('store', resolve('src/store'))
      .set('entry', resolve('src/entry'))
      .set('plugins', resolve('src/plugins'))
    config.plugin('context')
      .use(webpack.ContextReplacementPlugin,
        [/moment[/\\]locale$/, /zh-cn/])
    const svgRule = config.module.rule('svg') // 找到svg-loader
    svgRule.uses.clear() // 清除已有的loader, 如果不这样做会添加在此loader之后
    svgRule.exclude.add(/node_modules/) // 正则匹配排除node_modules目录
    svgRule // 添加svg新的loader处理
        .test(/\.svg$/)
        .use('svg-sprite-loader')
        .loader('svg-sprite-loader')
        .options({
          symbolId: 'icon-[name]'
        })
    // 修改images loader 添加svg处理
    const imagesRule = config.module.rule('images')
    imagesRule.exclude.add(resolve('src/common/icons'))
    config.module
        .rule('images')
        .test(/\.(png|jpe?g|gif|svg)(\?.*)?$/)
  },
  indexPath: 'fulltimelive.html',
  publicPath: publicPath
}
