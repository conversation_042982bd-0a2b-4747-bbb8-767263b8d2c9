<template>
  <div
    class="program-renderer"
    :style="containerStyle"
  >
    <!-- 节目单内容区域 -->
    <div class="program-content" :style="contentStyle">
      <div class="program-placeholder">
        <i class="el-icon-document"></i>
        <span>未选择节目单</span>
      </div>
    </div>

  </div>
</template>

<script>
export default {
  name: 'ProgramRenderer',
  props: {
    component: {
      type: Object,
      required: true
    }
  },
  computed: {
    containerStyle() {
      let styles = {
      }

      // 应用不透明度
      if (this.component.opacity !== undefined && this.component.opacity < 100) {
        styles.opacity = this.component.opacity / 100
      }

      return styles
    },

    contentStyle() {
      const styles = {
        width: '100%',
        height: '100%',
        objectFit: this.component.objectFit || 'contain'
      }

      return styles
    },

    playModeIcon() {
      return this.component.playType === 'loop' ? 'el-icon-refresh' : 'el-icon-video-play'
    }
  }
}
</script>

<style lang="scss" scoped>
.program-renderer {
  width: 100%;
  height: 100%;
  overflow: hidden;
  background: rgba(255, 107, 53, 0.1);
  position: relative;

  .program-content {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    padding: 8px;
    box-sizing: border-box;

    .program-info {
      display: flex;
      align-items: center;
      width: 100%;
      gap: 8px;

      .program-icon {
        flex-shrink: 0;
        width: 32px;
        height: 32px;
        background: #ff6b35;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #ffffff;
        font-size: 16px;
      }

      .program-details {
        flex: 1;
        min-width: 0;

        .program-name {
          font-size: 12px;
          font-weight: 500;
          color: #ffffff;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          line-height: 16px;
        }

        .program-id {
          font-size: 10px;
          color: #cccccc;
          line-height: 14px;
        }
      }

      .play-mode {
        flex-shrink: 0;
        color: #ff6b35;
        font-size: 14px;
      }
    }

    .program-placeholder {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      color: #999999;
      text-align: center;

      i {
        font-size: 24px;
        margin-bottom: 4px;
        color: #666666;
      }

      span {
        font-size: 11px;
        line-height: 14px;
      }
    }
  }

  .volume-indicator {
    position: absolute;
    top: 4px;
    right: 4px;
    background: rgba(0, 0, 0, 0.7);
    color: #ffffff;
    padding: 2px 6px;
    border-radius: 2px;
    font-size: 10px;
    display: flex;
    align-items: center;
    gap: 2px;

    i {
      font-size: 10px;
    }
  }

  // 悬停效果
  &:hover {
    border-color: #ff5722;
    background: rgba(255, 107, 53, 0.15);
  }

  // 选中状态
  &.selected {
    border-color: #ff5722;
    border-style: solid;
    background: rgba(255, 107, 53, 0.2);
    box-shadow: 0 0 0 2px rgba(255, 107, 53, 0.3);
  }
}

// 小尺寸时的样式调整
.program-renderer[style*="width: 1"] {
  .program-content .program-info {
    flex-direction: column;
    gap: 4px;

    .program-icon {
      width: 24px;
      height: 24px;
      font-size: 12px;
    }

    .program-details {
      text-align: center;

      .program-name {
        font-size: 10px;
      }

      .program-id {
        font-size: 8px;
      }
    }
  }
}
</style>
