<template>
  <div class="image-properties">
    <!-- 填充 -->
    <div class="property-section">
      <div class="section-title">填充</div>
      <div class="property-group">
        <!-- 图片上传 -->
        <div class="image-upload">
          <v-upload-img
            :editImgUrl="component.url"
            tips="点击上传图片"
            @uploadImgUrl="handleUploadImgUrl"
          />
        </div>

      </div>
    </div>

    <!-- 位置大小 -->
    <PositionSizeProperties
      :component="component"
      :canvas-size="canvasSize"
      @update="handleUpdate"
    />

    <!-- 画面显示 -->
    <DisplayModeProperties
      :component="component"
      @update="handleUpdate"
    />

    <!-- 效果 -->
    <EffectProperties
      :component="component"
      @update="handleUpdate"
    />
  </div>
</template>

<script>
import PositionSizeProperties from './common/PositionSizeProperties.vue'
import EffectProperties from './common/EffectProperties.vue'
import DisplayModeProperties from './common/DisplayModeProperties.vue'
import VUploadImg from '@/components/base/v-upload-img.vue'

export default {
  name: 'ImageProperties',
  components: {
    PositionSizeProperties,
    EffectProperties,
    DisplayModeProperties,
    VUploadImg
  },
  props: {
    component: {
      type: Object,
      required: true
    },
    canvasSize: {
      type: Object,
      default: () => ({ width: 1920, height: 1080 })
    }
  },
  data() {
    return {
      localOpacity: 100,
      localGaussianBlur: false,
      localBlurRadius: 45
    }
  },
  watch: {
    component: {
      handler() {
        this.syncLocalValues()
      },
      immediate: true
    }
  },
  methods: {
    syncLocalValues() {
      this.localOpacity = this.component.opacity || 100
      this.localGaussianBlur = !!this.component.gaussianBlur
      this.localBlurRadius = this.component.gaussianBlurRadius || 10
    },
    handleUpdate(data) {
      this.$emit('update', data)
    },
    handleUploadImgUrl(imgUrl, file, uploadFileType, AuthData) {
      // v-upload-img 组件的回调，imgUrl 是上传后的图片地址
      this.$emit('update', { url: imgUrl })
    },
    handleOpacityChange() {
      this.$emit('update', { opacity: this.localOpacity })
    },
    handleBlurToggle() {
      this.$emit('update', {
        gaussianBlur: this.localGaussianBlur ? 1 : 0,
        gaussianBlurRadius: this.localGaussianBlur ? this.localBlurRadius : 0
      })
    },
    handleBlurRadiusChange() {
      this.$emit('update', { gaussianBlurRadius: this.localBlurRadius })
    }
  }
}
</script>

<style lang="scss" scoped>
// 样式与 BackgroundProperties 类似，这里简化处理
.image-properties {
  .property-section {
    margin-bottom: 20px;

    .section-title {
      font-size: 14px;
      font-weight: 500;
      margin-bottom: 12px;
      padding: 0 16px;
    }
  }

  .property-group {
    margin-bottom: 16px;
    padding: 0 16px;
  }

  // 复用背景组件的样式
  .image-upload {
    margin-bottom: 16px;
  }

  .opacity-control, .blur-control {
    margin-bottom: 16px;

    label {
      display: block;
      font-size: 12px;
      margin-bottom: 8px;
    }

    .opacity-slider, .blur-slider {
      display: flex;
      align-items: center;
      gap: 12px;

      :deep(.el-slider) {
        flex: 1;

        .el-slider__runway {
          background: #4a4a4a;
        }

        .el-slider__bar {
          background: #ff6b35;
        }

        .el-slider__button {
          border-color: #ff6b35;
        }
      }

      .opacity-value {
        font-size: 12px;
        color: #ffffff;
        min-width: 32px;
      }

      .opacity-input, .blur-input {
        width: 80px;

        :deep(.el-input-number) {
          width: 100%;

          .el-input__inner {
            background: #4a4a4a;
            border-color: #606060;
            color: #ffffff;
            height: 28px;
            line-height: 28px;
          }
        }
      }
    }
  }

}
</style>
