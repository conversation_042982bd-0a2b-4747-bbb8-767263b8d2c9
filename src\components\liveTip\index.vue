<template>
  <div class="tip-box">
    <i class="warning-icon"></i>
    <span class="text">正在直播中，请谨慎操作</span>
    <i class="close-icon" @click="$emit('close')"></i>
  </div>
</template>

<script>
export default {
  data () {
    return {
      timer: null
    }
  },

  components: {},

  methods: {},

  mounted () {
    this.timer = setTimeout(() => {
      this.$emit('close')
    }, 5000)
  },
  beforeDestroy () {
    clearTimeout(this.timer)
  }
}
</script>

<style scoped lang="scss">
  .tip-box {
    position: fixed;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    left: 50%;
    transform: translateX(-50%);
    width: 356px;
    height: 44px;
    background: #FFE3E3;
    border: 1px solid #CD7979;
    box-shadow: 0px 1px 8px 0px rgba(60, 70, 89, 0.26);
    border-radius: 6px;
    z-index: 9999;
  }
  .warning-icon {
    margin-left: 20px;
    display: inline-block;
    background: url('./warning-icon.png') no-repeat;
    width: 22px;
    height: 22px;
    background-size: 22px;
  }
  .close-icon {
    position: absolute;
    right: 12px;
    top: 15px;
    display: inline-block;
    background: url('./close-icon.png') no-repeat;
    width: 14px;
    height: 14px;
    background-size: 14px;
    cursor: pointer;
  }
  .text {
    margin-left: 7px;
    font-size: 16px;
    color: #171616;
  }
</style>
