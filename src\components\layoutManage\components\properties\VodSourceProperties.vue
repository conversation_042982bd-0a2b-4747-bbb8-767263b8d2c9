<template>
  <div class="vod-source-properties">
    <!-- 来源类型 -->
    <div class="property-section">
      <div class="section-title">点播源设置</div>
      <div class="property-group">
        <div class="source-type-tabs">
          <button
            class="source-tab"
            :class="{ active: component.source === 'online' }"
            @click="handleSourceTypeChange('online')"
          >
            点播地址
          </button>
          <button
            class="source-tab"
            :class="{ active: component.source === 'local' }"
            @click="handleSourceTypeChange('local')"
          >
            本地文件
          </button>
        </div>

        <!-- 点播地址 -->
        <div v-if="component.source === 'online'" class="url-section">
          <div class="supported-formats">
            支持FLV、MP4、TS格式
          </div>
          <el-input
            v-model="localUrl"
            placeholder="请输入点播视频地址"
            @input="handleUrlChange"
          />
        </div>

        <!-- 本地文件上传 -->
        <div v-else class="upload-section">
          <el-upload
            class="video-uploader"
            :action="actionUrl"
            :show-file-list="true"
            :before-upload="handleBeforeUpload"
            :on-success="handleSuccess"
            :on-error="handleError"
            accept=".mp4,.mov,.webm"
          >
            <div class="upload-area">
              <i class="el-icon-upload"></i>
              <div class="upload-text">点击上传视频文件</div>
              <div class="upload-hint">支持MP4、MOV、WEBM格式</div>
            </div>
          </el-upload>
        </div>
        <div style="text-align: right;margin-top: 10px;">
          <el-button type="info" @click="handleUrlConfirm" size="small">确认</el-button>
        </div>
      </div>
    </div>

    <!-- 位置大小 -->
    <PositionSizeProperties
      :component="component"
      :canvas-size="canvasSize"
      @update="handleUpdate"
    />

    <!-- 画面显示 -->
    <DisplayModeProperties
      :component="component"
      @update="handleUpdate"
    />

    <!-- 播放模式 -->
    <div class="property-section">
      <div class="section-title">播放模式</div>
      <div class="property-group">
        <el-radio-group v-model="localPlayType" @change="handlePlayTypeChange">
          <el-radio label="loop">循环播放</el-radio>
          <el-radio label="once">只播一次</el-radio>
        </el-radio-group>
      </div>
    </div>

    <!-- 音频调节 -->
    <VolumeProperties
      :component="component"
      @update="handleUpdate"
    />
  </div>
</template>

<script>
import PositionSizeProperties from './common/PositionSizeProperties.vue'
import DisplayModeProperties from './common/DisplayModeProperties.vue'
import VolumeProperties from './common/VolumeProperties.vue'
import { mapGetters } from 'vuex'

export default {
  name: 'VodSourceProperties',
  components: {
    PositionSizeProperties,
    DisplayModeProperties,
    VolumeProperties
  },
  props: {
    component: {
      type: Object,
      required: true
    },
    canvasSize: {
      type: Object,
      default: () => ({ width: 1920, height: 1080 })
    }
  },
  computed: {
    ...mapGetters([
      'instanceId'
    ])
  },
  data() {
    return {
      actionUrl: '/cloud/fullTimeLive/local/upload?instanceId=',
      localUrl: '',
      localVolume: 100,
      localPlayType: 'loop',
      displayModes: [
        { label: '适应', value: 'contain' },
        { label: '拉伸', value: 'fill' },
        { label: '裁剪', value: 'cover' }
      ]
    }
  },
  watch: {
    component: {
      handler() {
        this.syncLocalValues()
      },
      immediate: true
    }
  },
  methods: {
    syncLocalValues() {
      this.localUrl = this.component.url || ''
      this.localVolume = this.component.volume || 100
      this.localPlayType = this.component.playType || 'loop'
    },
    handleUpdate(data) {
      this.$emit('update', data)
    },
    handleSourceTypeChange(type) {
      this.$emit('update', { source: type })
    },
    handleUrlChange() {
      this.$emit('update', { url: this.localUrl })
    },
    handleDisplayModeChange(mode) {
      this.$emit('update', { objectFit: mode })
    },
    handlePlayTypeChange() {
      this.$emit('update', { playType: this.localPlayType })
    },
    handleVolumeChange() {
      this.$emit('update', { volume: this.localVolume })
    },
    handleBeforeUpload(file) {
      // 这里可以处理文件上传逻辑
      console.log('上传文件:', file)
      if (!['mp4', 'mov', 'webm'].includes(file.name.substring(file.name.lastIndexOf('.') + 1, file.name.length))) {
        this.$message.error('请上传正确的格式文件')
        return false
      }
      return true
    },
    handleUrlConfirm() {
      this.$emit('update', { url: this.localUrl })
    },
    handleSuccess(response, file, fileList) {
      this.$emit('update', { url: window.location.origin + response.value.visitUrl })
    },
    handleError(error, file, fileList) {
      this.$message.error('上传失败')
    }
  },
  mounted() {
    this.actionUrl = '/cloud/fullTimeLive/local/upload?instanceId=' + this.instanceId
  }
}
</script>

<style lang="scss" scoped>
.vod-source-properties {
  .property-section {
    margin-bottom: 20px;

    .section-title {
      font-size: 14px;
      font-weight: 500;
      margin-bottom: 12px;
      padding: 0 16px;
    }
  }

  .property-group {
    margin-bottom: 16px;
    padding: 0 16px;
  }

  .source-type-tabs {
    display: flex;
    gap: 2px;
    margin-bottom: 12px;
    background-color: #3F4552;
    border-radius: 6px;

    .source-tab {
      flex: 1;
      height: 32px;
      background: transparent;
      border: none;
      color: #B3BACD;
      border-radius: 4px;
      cursor: pointer;
      font-size: 12px;
      transition: all 0.2s ease;

      &:hover {
        background: #555D70;
        color: #fff;
      }

      &.active {
        background: #555D70;
        color: #fff;
      }
    }
  }

  .url-section {
    .supported-formats {
      font-size: 12px;
      color: #999999;
      margin-bottom: 8px;
    }

    :deep(.el-input) {
      .el-input__inner {
        background: #4a4a4a;
        border-color: #606060;
        color: #ffffff;

        &::placeholder {
          color: #999999;
        }
      }
    }
  }

  .upload-section {
    :deep(.video-uploader) {
      .el-upload {
        width: 100%;
      }
    }

    .upload-area {
      width: 100%;
      height: 80px;
      border: 2px dashed #606060;
      border-radius: 4px;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: all 0.2s ease;

      &:hover {
        border-color: #ff6b35;
      }

      i {
        font-size: 24px;
        color: #999999;
        margin-bottom: 4px;
      }

      .upload-text {
        font-size: 12px;
        color: #ffffff;
        margin-bottom: 2px;
      }

      .upload-hint {
        font-size: 10px;
        color: #999999;
      }
    }
  }

  :deep(.el-radio-group) {
    .el-radio {
      color: #ffffff;
      margin-right: 20px;

      .el-radio__input.is-checked .el-radio__inner {
        background: #ff6b35;
        border-color: #ff6b35;
      }

      .el-radio__label {
        color: #B0BBD6;
      }
    }
  }

  .volume-control {
    display: flex;
    align-items: center;
    gap: 12px;

    :deep(.el-slider) {
      flex: 1;

      .el-slider__runway {
        background: #4a4a4a;
      }

      .el-slider__bar {
        background: #ff6b35;
      }

      .el-slider__button {
        border-color: #ff6b35;
      }
    }

    .volume-input {
      width: 80px;

      :deep(.el-input-number) {
        width: 100%;

        .el-input__inner {
          background: #4a4a4a;
          border-color: #606060;
          color: #ffffff;
          height: 28px;
          line-height: 28px;
        }
      }
    }
  }
}
</style>
