/* 驱动应用的数据源 所有状态 */
const state = {
  instanceId: 0,
  roleType: -1, // 1：管理员；2：巡查员；0：实例操作员
  liveId: 0,
  pwd: '',
  secretKey: '',
  timeDifference: 0,
  httpEndPoint: '',
  liveWidth: 1920,
  liveHeight: 1080,
  warnSetting: {
    mailList: [],
    phoneList: []
  },
  output: {
    width: 0,
    height: 0,
    videoBitrate: 0,
    outputUrl: '',
    delay: 0,
    isLiving: false,
    outputUrls: []
  },
  audio: {
    programVolume: 100,
    programType: 0,
    programSingle: false,
    programMix: false,
    bgmVolume: 0,
    bgmSingle: false,
    bgmMix: false,
    audios: []
  },
  Emergency: {safeVideoUrl: '', inUrgency: false}, // 紧急切换
  programs: [],
  todayPrograms: [],
  loopPrograms: [],
  programsSn: '', // 节目单流水号
  cgs: [], // cg字幕列表信息
  loopStatus: 2,
  defaultPic: '',
  versionInfo: {
    deviceCode: '', // 设备编号
    currentVersion: '', // 当前版本号
    latestVersion: '', // 最新版本号
    upgradeDownloadStatus: '' // 升级下载状态，undo/downloading/finish
  },
  machineInfo: {
    instanceCount: 0,
    authorized: false,
    is4k: false
  },
  localTimeDifference: 0,
  background: '',
  localStorageInfo: {
    currentVolume: 0,
    maxVolume: 0
  },
  portRange: {
    portMin: 30000,
    portMax: 40000
  },
  schedules: [],
  currentScheduleId: '',
  cardNumber: '',
  mappedIp: '',
  hasParentLimit: true, // 用于限制组件是否超出画布范围
  layouts: [], // 布局列表
  currentLayoutId: null // 当前使用的布局ID
}
export default state
