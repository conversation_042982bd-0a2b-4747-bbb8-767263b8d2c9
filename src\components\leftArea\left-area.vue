<template>
  <div>
    <el-tabs v-model="activeName" type="card">
      <el-tab-pane label="播出单" name="first" key="first">
        <program-area :currentProgramId="currentProgramId"
          :nextProgramId="nextProgramId"></program-area>
      </el-tab-pane>
      <el-tab-pane label="节目编排" name="fifth" key="fifth">
        <program-schedule></program-schedule>
      </el-tab-pane>
      <el-tab-pane label="节目单管理" name="seventh" key="seventh">
        <PgmMenu />
      </el-tab-pane>
      <el-tab-pane label="节目管理" name="fourth" key="fourth">
        <program-manage-list></program-manage-list>
      </el-tab-pane>
      <el-tab-pane label="直播流管理" name="eighth" key="eighth">
        <LiveManage />
      </el-tab-pane>
      <el-tab-pane label="音频管理" name="second" key="second" v-if="roleType !== 2">
        <audio-manage @switchTab="switchTab"></audio-manage>
      </el-tab-pane>
      <el-tab-pane label="字幕管理" name="third" key="third" v-if="roleType !== 2">
        <cg-subtitle-list></cg-subtitle-list>
      </el-tab-pane>
      <el-tab-pane label="布局管理" name="sixth" key="sixth" v-if="roleType !== 2">
        <layout-manage></layout-manage>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import programArea from '../program-area'
import audioManage from './audio-manage'
import cgSubtitleList from '../cgTemplate/cg-subtitle-list'
import layoutManage from '../layoutManage'
import programManageList from '../programManage/program-manage-list'
import programSchedule from '../programSchedule'
import PgmMenu from '@/components/pgmMenu'
import LiveManage from '@/components/liveManage'

export default {
  name: 'left-area',
  components: {
    audioManage,
    cgSubtitleList,
    programManageList,
    programArea,
    programSchedule,
    layoutManage,
    PgmMenu,
    LiveManage
  },
  props: {
    nextProgramId: {
      type: String
    },
    currentProgramId: {
      type: String
    }
  },
  computed: {
    ...mapGetters(['roleType'])
  },
  methods: {
    switchTab (tab) {
      this.activeName = tab
    }
  },
  data() {
    return {
      activeName: 'first'
    }
  },
  mounted() {
    this.$bus.$on('goToSubtitle', () => {
      this.activeName = 'third'
    })
  }
}
</script>

<style scoped lang="scss">
::v-deep .el-tabs__item.is-active {
  background-color: #21252d !important;
  border-top: 1px solid #fc4f08 !important;
}
::v-deep .el-tabs__header {
  background-color: $color-content-bg !important;
  border-bottom: #21252d 1px solid !important;
  margin-bottom: 0 !important;
}
::v-deep .el-tabs__nav {
  border: none !important;
}
::v-deep .el-tabs__item {
  background-color: $color-content-bg !important;
  border: none !important;
  font-size: 18px;
  user-select: none;
}
::v-deep .el-tabs {
  height: 100%;
}
::v-deep .el-tab-pane {
  height: 100%;
}
::v-deep .el-tabs__content {
  height: calc(100% - 42px);
}
</style>
