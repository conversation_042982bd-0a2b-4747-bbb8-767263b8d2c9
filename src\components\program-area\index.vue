<template>
  <div class="wh100 program-area">
    <div class="left-area">
      <program-list :currentProgramId="currentProgramId" :nextProgramId="nextProgramId"></program-list>
    </div>
    <div class="right-area">
      <today-program :currentProgramId="currentProgramId" :nextProgramId="nextProgramId"></today-program>
    </div>
  </div>
</template>

<script>
import programList from '../programList/program-list'
import todayProgram from '../todayProgram'
export default {
  props: {
    nextProgramId: {
      type: String
    },
    currentProgramId: {
      type: String
    }
  },
  data () {
    return {
    }
  },

  components: {programList, todayProgram},

  methods: {}
}
</script>

<style scoped lang="scss">
  .program-area {
    display: flex;
  }
  .left-area,
  .right-area {
    width: 50%;
    box-sizing: border-box;
    border-top: 3px solid transparent;
    border-right: 3px solid transparent;
  }

</style>
