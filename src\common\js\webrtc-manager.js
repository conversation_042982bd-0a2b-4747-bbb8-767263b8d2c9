import cloudApi from 'api/cloud'
let pcList = []
let videoPcList = []
let previewPcList = []
export default {
  register: function (pc) {
    pcList.push(pc)
  },
  destroyAll: function () {
    pcList.forEach(pc => {
      try {
        pc.close()
        pc = null
      } catch (e) {
      }
    })
    pcList = []
  },
  registerVideo: function (pc) {
    videoPcList.push(pc)
  },
  destroyAllVideo: function () {
    videoPcList.forEach(pc => {
      try {
        pc.close()
        pc = null
      } catch (e) {
      }
    })
    videoPcList = []
  },
  registerPreview: function (pc) {
    previewPcList.push(pc)
  },
  destroyAllPreview: function () {
    for (let pc of previewPcList) {
      try {
        if (pc.previewId) {
          cloudApi.setPreviewExitt({previewId: pc.previewId}, () => {})
        }
        pc.close()
        pc = null
      } catch (e) {
      }
    }
    // previewPcList.forEach(pc => {
    //   try {
    //     pc.close()
    //     pc = null
    //   } catch (e) {
    //   }
    // })
    previewPcList = []
  }
}
