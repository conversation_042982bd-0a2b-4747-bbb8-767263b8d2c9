let path = require('path')
let co = require('co')
let OSS = require('ali-oss')
let fs = require('fs')

let ossAuth = {
  region: 'oss-cn-hangzhou',
  accessKeyId: 'btX9Qks0VQvmAo3Z',
  accessKeySecret: 'C16VRLu2Ydse1f702Hz3aPy42nbRhX',
  bucket: 'q4-record'
}
let pathPrefix = 'ui/ft-live/'
let client
if (ossAuth !== undefined) {
  client = new OSS(ossAuth)
}
let cos
let version = '1.0'

function resolve (dir) {
  return path.join(__dirname, '.', dir)
}

let distPath = resolve('dist')

readDirSync(distPath)

function readDirSync (dir) {
  let pa = fs.readdirSync(dir)
  pa.forEach(function (ele, index) {
    let info = fs.statSync(dir + '/' + ele)
    if (info.isDirectory()) {
      readDirSync(dir + '/' + ele)
    } else {
      if (!ele.endsWith('.map')) {
        co(function * () {
          let filePath = path.join('', '', dir + '/' + ele)
          let objKey = pathPrefix + version + dir.replace(distPath, '') + '/' + ele
          if (client !== undefined) {
            let stream = fs.createReadStream(filePath)
            let result = yield client.putStream(objKey, stream)
            if (result.res.status === 200) {
              console.log(objKey)
            }
          }
          if (cos !== undefined) {
            putCosObject(filePath, objKey)
          }
        }).catch(function (err) {
          console.log('=====================================================================================')
          console.log('=====================================================================================')
          console.log('=====================================================================================')
          console.log('=====================================================================================')
          console.log('=====================================================================================')
          console.log('=====================================================================================')
          console.log('=====================================================================================')
          console.log('=====================================================================================')
          console.log('=====================================================================================')
          console.log('=====================================================================================')
          console.log('=======================' + err)
        })
      }
    }
  })
}

function putCosObject (filePath, key) {
  cos.sliceUploadFile({
    Bucket: env.CONFIG.cosAuth.bucket,
    Region: env.CONFIG.cosAuth.region,
    Key: key,
    FilePath: filePath
  }, function (err, data) {
    if (data.statusCode != 200) {
      console.log(err)
    }
    // console.log(err, data);
  })
}
