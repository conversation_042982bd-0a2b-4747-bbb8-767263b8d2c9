<template>
  <div class="wrapper">
    <el-form ref="form" label-width="100px">
      <el-form-item label="实例编号：">
        <el-row>
          <el-col :span="8" style="margin-right: 10px;">
            <el-input v-model="instanceId"></el-input>
          </el-col>
          <el-col :span="15">
            <el-button type="primary" @click="resetInstance">清空实例配置</el-button>
            <el-button type="primary" @click="restartInstance">重启实例</el-button>
          </el-col>
        </el-row>
      </el-form-item>
      <el-divider></el-divider>
      <el-form-item>
        <el-button type="primary" @click="rebootSever">重启服务器</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import ctrlApi from 'api/ctrl'
export default {
  name: 'admin',
  components: {},
  props: {},
  computed: {},
  data() {
    return {
      instanceId: 1
    }
  },
  methods: {
    resetInstance() {
      if (!this.instanceId) return this.$message.warning('请输入实例编号')
      this.$confirm('确定要重置实例配置吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        let param = {
          instanceId: this.instanceId
        }
        ctrlApi.resetInstance(param, (res) => {
          this.$message.success('重置成功')
        })
      })
    },
    restartInstance() {
      if (!this.instanceId) return this.$message.warning('请输入实例编号')
      this.$confirm('确定要重启实例吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        let param = {
          instanceId: this.instanceId
        }
        ctrlApi.restartInstance(param, (res) => {
          this.$message.success('重启实例成功')
        })
      })
    },
    rebootSever() {
      this.$confirm('确定要重启服务器吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        let param = {}
        ctrlApi.rebootSever(param, (res) => {
          this.$message.success('重启服务器成功')
        })
      })
    }
  },
  created() {},
  mounted() {},
  watch: {}
}
</script>
<style lang="scss" scoped>
.wrapper {
  padding: 20px 30px;
  background-color: $color-content-bg;
  .el-form {
  }
}
</style>
