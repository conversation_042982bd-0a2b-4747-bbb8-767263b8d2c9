<template>
  <div class="instance-video wh100">
    <video :id="'pgm_' + instanceId" class="video wh100" autoplay :poster="getPoster" muted :controls="false"></video>
  </div>
</template>

<script>
import ChannelVideo from 'common/js/channel-video'
export default {
  props: {
    instanceId: {
      type: Number,
      default: 0
    }
  },
  data () {
    return {
      video: null,
      videoEle: null
    }
  },

  components: {},

  methods: {
    getPoster () {
      return '@/common/img/video-placeholder.png'
    },
    instanceLoadingHandle () {
      this.$emit('instanceLoading', false)
    }
  },
  mounted () {
    this.video = new ChannelVideo('pgm_' + this.instanceId, 'pgm', this.instanceId)
    this.videoEle = document.getElementById('pgm_' + this.instanceId)
    this.videoEle.addEventListener('play', this.instanceLoadingHandle)
  },
  beforeDestroy () {
    this.videoEle.removeEventListener('play', this.instanceLoadingHandle)
  }
}
</script>

<style scoped>
</style>
