{"name": "studio", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build-staging": "vue-cli-service build --mode staging", "build-local": "vue-cli-service build --mode local", "deploy-staging": "node deploy-staging.js", "build-prod": "vue-cli-service build --mode prod", "deploy-prod": "node deploy-prod.js", "lint": "vue-cli-service lint"}, "dependencies": {"@xkeshi/vue-qrcode": "^0.3.0", "a-big-triangle": "^1.0.3", "apply-cube-lut": "^1.0.0", "axios": "^0.19.0", "canvas-from-ndarray": "^1.0.0", "clipboard": "^1.7.1", "core-js": "^2.6.5", "cropperjs": "^1.5.5", "crypto-js": "^3.1.8", "cube-ui": "^1.12.15", "element-ui": "^2.11.0", "gl-context": "^0.1.1", "gl-shader": "^4.3.1", "gl-texture2d": "^2.1.0", "html2canvas": "^1.4.1", "moment": "^2.24.0", "ndarray-from-image": "^1.0.1", "ndarray-linear-interpolate": "^1.0.0", "ndarray-ops": "^1.2.2", "parse-cube-lut": "^1.0.1", "pixi.js": "5.2.5", "videojs-contrib-hls": "^5.12.2", "videojs-flash": "^2.1.0", "vue": "^2.6.10", "vue-draggable-resizable": "^2.3.0", "vue-fullscreen": "^2.1.5", "vue-router": "^2.6.0", "vue-shortkey": "^3.1.7", "vue-video-player": "^4.0.6", "vuedraggable": "^2.17.0", "vuex": "^3.1.1"}, "devDependencies": {"@vue/cli-plugin-babel": "^3.10.0", "@vue/cli-plugin-eslint": "^3.10.0", "@vue/cli-service": "^3.10.0", "@vue/eslint-config-standard": "^4.0.0", "ali-oss": "^4.11.3", "babel-eslint": "^10.0.1", "babel-plugin-component": "^1.1.1", "eslint": "^5.16.0", "eslint-plugin-vue": "^5.0.0", "mockjs": "^1.0.1-beta3", "node-sass": "^4.9.0", "sass-loader": "^7.1.0", "stylus": "^0.54.5", "stylus-loader": "^3.0.2", "svg-sprite-loader": "^5.0.0", "vue-cli-plugin-cube-ui": "^0.2.5", "vue-cli-plugin-element": "^1.0.1", "vue-template-compiler": "^2.6.10", "xlsx": "^0.16.9"}, "transformModules": {"cube-ui": {"transform": "cube-ui/src/modules/${member}", "kebabCase": true}}}