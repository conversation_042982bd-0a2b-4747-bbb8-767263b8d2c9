/* 组装模块并导出 store 的地方 */
import Vue from 'vue'
import Vuex from 'vuex'
import * as actions from './actions'
import * as getters from './getters'
import state from './state'
import mutations from './mutations'
import createLogger from 'vuex/dist/logger' // Vuex 自带的日志插件用于一般的调试

Vue.use(Vuex)
const debug = process.env.NODE_ENV !== 'production' // 只在开发环境下使用某些校验、调试工具等
export default new Vuex.Store({
  actions,
  getters,
  mutations,
  state,
  strict: debug // 严格模式，监测状态树来检测不合规的状态变更
  // plugins: debug ? [createLogger()] : []
})
