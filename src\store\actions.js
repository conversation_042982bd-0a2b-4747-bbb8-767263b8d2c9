// 异步操作和或者对mutation做一些封装
import * as types from './mutation-types'
import liveApi from 'api/live'
import Vue from 'vue'
import cloudApi from 'api/cloud'
import store from '@/store/index'

export const setLiveInfo = ({commit}, instanceInfo) => {
  commit(types.SET_LIVE_ID, instanceInfo.liveId)
  commit(types.SET_LIVE_SECRET_KEY, instanceInfo.secretKey)
  commit(types.SET_HTTP_END_POINT, instanceInfo.instanceIp)
  commit(types.SET_TIME_DIFFERENCE, instanceInfo.timeDifference)
  commit(types.SET_WARN_SETTING, instanceInfo.warnSetting)
}

export const setLiveConfig = ({commit}, config) => {
  commit(types.INT_CGS, config.cgs)
  commit(types.SET_AUDIO, config.audio)
  commit(types.SET_EMERGENCY, config.Emergency)
  commit(types.SET_OUTPUT, config.output)
  commit(types.SET_LIVE_WIDTH, config.output.width)
  commit(types.SET_LIVE_HEIGHT, config.output.height)
  commit(types.SET_PROGRAMS_SN, config.programsSn)
  commit(types.SET_LOOPSTATUS, config.repeat)
  commit(types.SET_DEFAULTPIC, config.defaultPic)
  commit(types.SET_BACKGROUND, config.background)
  commit(types.SET_PPOR_INFO, config.udpPortRange)
  commit(types.SET_SCHEDULES, config.schedules)
}
export const getPrograms = ({getters, commit}) => {
  return new Promise((resolve, reject) => {
    liveApi.queryPrograms({programsSn: getters.programsSn}, res => {
      let todayPrograms = res.todayPrograms || []
      let loopPrograms = res.loopPrograms || []
      let programs = []
      if (todayPrograms.length || loopPrograms.length) {
        programs = todayPrograms.concat(loopPrograms)
      }
      commit(types.SET_TODAY_PROGRAMS, todayPrograms)
      commit(types.SET_LOOP_PROGRAMS, loopPrograms)
      commit(types.SET_PROGRAMS, programs)
      commit(types.SET_PROGRAMS_SN, res.programsSn)
      resolve(res)
    }, (code, msg) => {
      reject(msg, code)
    })
  })
}
export const setRepeat = ({commit}, repeat) => {
  commit(types.SET_LOOPSTATUS, repeat)
}
export const addProgram = ({getters, dispatch, commit}, param) => {
  param.programsSn = getters.programsSn
  liveApi.addProgram(param, res => {
    commit(types.SET_PROGRAMS_SN, res.programsSn)
    dispatch('getPrograms')
  }, (code, msg, value) => {
    if (code === -60004) {
      commit(types.SET_PROGRAMS_SN, value.programsSn)
      dispatch('addProgram', param)
      return
    }
    Vue.prototype.$message.error(msg)
  })
}
export const delProgram = ({getters, dispatch, commit}, param) => {
  param.programsSn = getters.programsSn
  liveApi.deleteProgram(param, res => {
    commit(types.SET_PROGRAMS_SN, res.programsSn)
    dispatch('getPrograms')
  }, (code, msg, value) => {
    if (code === -60004) {
      commit(types.SET_PROGRAMS_SN, value.programsSn)
      dispatch('delProgram', param)
      return
    }
    Vue.prototype.$message.error(msg)
  })
}
export const cutProgram = ({getters, dispatch, commit}, id) => {
  liveApi.cutProgram({id: id, programsSn: getters.programsSn}, res => {
    commit(types.SET_PROGRAMS_SN, res.programsSn)
    dispatch('getPrograms')
  }, (code, msg, value) => {
    if (code === -60004) {
      commit(types.SET_PROGRAMS_SN, value.programsSn)
      dispatch('delProgram', id)
      return
    }
    Vue.prototype.$message.error(msg)
  })
}
export const modProgram = ({getters, dispatch, commit}, param) => {
  param.programsSn = getters.programsSn
  liveApi.modProgram(param, res => {
    commit(types.SET_PROGRAMS_SN, res.programsSn)
    dispatch('getPrograms')
  }, (code, msg, value) => {
    if (code === -60004) {
      commit(types.SET_PROGRAMS_SN, value.programsSn)
      dispatch('modProgram', param)
      return
    }
    Vue.prototype.$message.error(msg)
  })
}

export const getFileStorage = ({commit}) => {
  cloudApi.getInstanceInfo({}, res => {
    commit(types.SET_LOCAL_STORAGE_INFO, {currentVolume: res.currentVolume, maxVolume: res.maxVolume})
  })
}

// 布局相关actions
export const loadLayouts = ({commit}) => {
  return new Promise((resolve, reject) => {
    liveApi.getLayouts({}, (response) => {
      if (response && response.layouts) {
        // 导入数据转换函数
        const { convertFromBackendLayout } = require('@/components/layoutManage/layoutDataConverter')
        response.layouts = response.layouts.filter(layout => layout.userData)
        // 将后端数据转换为前端格式
        const layouts = response.layouts.map(backendLayout => {
          return convertFromBackendLayout(backendLayout)
        })
        commit(types.SET_LAYOUTS, layouts)
        resolve(layouts)
      } else {
        commit(types.SET_LAYOUTS, [])
        resolve([])
      }
    }, (error) => {
      console.error('获取布局列表失败:', error)
      reject(error)
    })
  })
}

export const addLayoutToStore = ({commit}, layout) => {
  commit(types.ADD_LAYOUT, layout)
}

export const updateLayoutInStore = ({commit}, layout) => {
  commit(types.UPDATE_LAYOUT, layout)
}

export const deleteLayoutFromStore = ({commit}, layoutId) => {
  commit(types.DELETE_LAYOUT, layoutId)
}

export const setCurrentLayoutId = ({commit}, layoutId) => {
  commit(types.SET_CURRENT_LAYOUT_ID, layoutId)
}
