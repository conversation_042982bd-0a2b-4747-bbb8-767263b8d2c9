<?xml version="1.0" encoding="UTF-8"?>
<svg width="22.0000442px" height="22px" viewBox="0 0 22.0000442 22" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>文字</title>
    <g id="25年7月30日" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="布局管理-新建布局-主画面" transform="translate(-225, -509)">
            <g id="新增节目-日播" transform="translate(169, 86)">
                <g id="编组-5" transform="translate(27, 112)">
                    <g id="编组-12" transform="translate(10, 301)">
                        <g id="文字" transform="translate(17, 8)">
                            <rect id="矩形" x="0" y="0" width="26" height="26"></rect>
                            <path d="M20.6999989,2 C22.522491,2 24.0000442,3.47743978 24.0000442,5.30003863 L24.0000442,20.6999614 C24.0000442,22.5225602 22.522491,24 20.6999989,24 L5.29993488,24 C3.47744275,24 2,22.5225602 2,20.6999614 L2,5.30003863 C2,3.47743978 3.47744275,2 5.29993488,2 Z M14.0620903,5.9798995 L12.0925282,5.9798995 L6.28337747,19.340365 L5.9798995,20.0201005 L8.545235,20.0201005 L9.97958637,16.5881956 L16.1032869,16.5881956 L17.5487101,20.0201005 L20.1306533,20.0201005 L14.0620903,5.9798995 Z M12.9917748,9.18592965 L15.1557789,14.3819095 L10.8442211,14.3819095 L12.9917748,9.18592965 Z" id="形状结合" fill="currentColor"></path>
                        </g>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>