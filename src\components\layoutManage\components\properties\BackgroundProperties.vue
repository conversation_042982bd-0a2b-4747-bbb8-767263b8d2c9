<template>
  <div class="background-properties">
    <!-- 填充 -->
    <div class="property-section">
      <div class="section-title">填充</div>
      <div class="property-group">
        <!-- 图片上传 -->
        <div class="image-upload">
          <v-upload-img
            :editImgUrl="component.url"
            tips="点击上传背景图片"
            @uploadImgUrl="handleUploadImgUrl"
          />
        </div>

      </div>
    </div>

    <!-- 画面显示 -->
    <DisplayModeProperties
      :component="component"
      @update="handleUpdate"
    />

    <!-- 效果 -->
    <EffectProperties
      :component="component"
      :default-opacity="85"
      @update="handleUpdate"
    />
  </div>
</template>

<script>
import EffectProperties from './common/EffectProperties.vue'
import DisplayModeProperties from './common/DisplayModeProperties.vue'
import VUploadImg from '@/components/base/v-upload-img.vue'

export default {
  name: 'BackgroundProperties',
  components: {
    EffectProperties,
    DisplayModeProperties,
    VUploadImg
  },
  props: {
    component: {
      type: Object,
      required: true
    },
    canvasSize: {
      type: Object,
      default: () => ({ width: 1920, height: 1080 })
    }
  },
  methods: {
    handleUpdate(data) {
      this.$emit('update', data)
    },
    handleUploadImgUrl(imgUrl, file, uploadFileType, AuthData) {
      // v-upload-img 组件的回调，imgUrl 是上传后的图片地址
      this.$emit('update', { url: imgUrl })
    }
  }
}
</script>

<style lang="scss" scoped>
.background-properties {
  .property-section {
    margin-bottom: 20px;

    .section-title {
      font-size: 14px;
      font-weight: 500;
      margin-bottom: 12px;
      padding: 0 16px;
    }
  }

  .property-group {
    margin-bottom: 16px;
    padding: 0 16px;
  }

  .image-upload {
    margin-bottom: 16px;
  }

  .opacity-control, .blur-control {
    margin-bottom: 16px;

    label {
      display: block;
      font-size: 12px;
      margin-bottom: 8px;
    }

    .opacity-slider, .blur-slider {
      display: flex;
      align-items: center;
      gap: 12px;

      :deep(.el-slider) {
        flex: 1;

        .el-slider__runway {
          background: #4a4a4a;
        }

        .el-slider__bar {
          background: #ff6b35;
        }

        .el-slider__button {
          border-color: #ff6b35;
        }
      }

      .opacity-value {
        font-size: 12px;
        color: #ffffff;
        min-width: 32px;
      }

      .opacity-input, .blur-input {
        width: 80px;

        :deep(.el-input-number) {
          width: 100%;

          .el-input__inner {
            background: #4a4a4a;
            border-color: #606060;
            color: #ffffff;
            height: 28px;
            line-height: 28px;
          }
        }
      }
    }
  }

}
</style>
