<template>
  <div class="text-properties">
    <!-- 文字内容 -->
    <div class="property-section">
      <div class="section-title">文字内容</div>
      <div class="property-group">
        <el-input v-model="localText" type="textarea" :rows="3" placeholder="输入文字内容"
          @input="handleTextChange" />
      </div>
    </div>

    <!-- 字体样式 -->
    <div class="property-section">
      <div class="section-title">字体样式</div>
      <div class="property-group">
        <!-- 字体选择 -->
        <div class="font-select">
          <label>字体</label>
          <el-select v-model="localFont" @change="handleFontChange">
            <el-option v-for="font in fontOptions" :key="font.value" :label="font.label"
              :value="font.value" />
          </el-select>
          <div class="size-input-group">
            <div class="size-controls">
              <el-input-number v-model="localSize" :min="8" :max="200" size="small"
                style="width: 242px" @change="handleSizeChange" />
            </div>
          </div>
        </div>

        <!-- 颜色设置 -->
        <div class="color-controls">
          <div class="color-item">
            <label>填充</label>
            <div class="color-input-group">
              <el-color-picker v-model="localColor" show-alpha size="small"
                @change="handleColorChange" />
            </div>
          </div>

          <div class="color-item">
            <label>描边</label>
            <div class="color-input-group">
              <el-color-picker v-model="localStrokeColor" show-alpha size="small"
                @change="handleStrokeColorChange" />
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 位置大小 -->
    <PositionSizeProperties :component="component" :canvas-size="canvasSize"
      @update="handleUpdate" />
  </div>
</template>

<script>
import PositionSizeProperties from './common/PositionSizeProperties.vue'

export default {
  name: 'TextProperties',
  components: {
    PositionSizeProperties
  },
  props: {
    component: {
      type: Object,
      required: true
    },
    canvasSize: {
      type: Object,
      default: () => ({ width: 1920, height: 1080 })
    }
  },
  data() {
    return {
      localText: '输入文字内容',
      localFont: 'Microsoft YaHei',
      localSize: 26,
      localColor: '#FF8300',
      localColorOpacity: 100,
      localStrokeColor: '#FFFFFF',
      localStrokeOpacity: 100,
      sizePreset: 'normal',
      fontOptions: [
        { label: '微软雅黑', value: 'Microsoft YaHei' },
        { label: '黑体', value: 'SimHei' }
      ]
    }
  },
  watch: {
    component: {
      handler() {
        this.syncLocalValues()
      },
      immediate: true
    }
  },
  methods: {
    syncLocalValues() {
      this.localText = this.component.text || '输入文字内容'
      this.localFont = this.component.font || 'Microsoft YaHei'
      this.localSize = this.component.size || 26
      this.localColor = this.component.color || '#FF8300'
      this.localColorOpacity = 100

      if (this.component.textShadow) {
        this.localStrokeColor = this.component.textShadow.color || '#FFFFFF'
        this.localStrokeOpacity = this.component.textShadow.opacity || 100
      }
    },
    handleUpdate(data) {
      this.$emit('update', data)
    },
    handleTextChange() {
      this.$emit('update', { text: this.localText })
    },
    handleFontChange() {
      this.$emit('update', { font: this.localFont })
    },
    handleSizeChange() {
      this.$emit('update', { size: this.localSize })
    },
    handleSizePresetChange() {
      if (this.sizePreset === 'normal') {
        this.localSize = 26
        this.handleSizeChange()
      }
    },
    adjustSize(delta) {
      this.localSize = Math.max(8, Math.min(200, this.localSize + delta))
      this.sizePreset = 'custom'
      this.handleSizeChange()
    },
    handleColorChange() {
      this.$emit('update', { color: this.localColor })
    },
    handleColorOpacityChange() {
      // 这里可以处理颜色透明度
      console.log('颜色透明度:', this.localColorOpacity)
    },
    handleStrokeColorChange() {
      this.$emit('update', {
        textShadow: {
          ...this.component.textShadow,
          color: this.localStrokeColor
        }
      })
    },
    handleStrokeOpacityChange() {
      this.$emit('update', {
        textShadow: {
          ...this.component.textShadow,
          opacity: this.localStrokeOpacity
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.text-properties {
  .property-section {
    margin-bottom: 20px;

    .section-title {
      font-size: 14px;
      font-weight: 500;
      margin-bottom: 12px;
      padding: 0 16px;
    }
  }

  .property-group {
    margin-bottom: 16px;
    padding: 0 16px;
  }

  :deep(.el-textarea) {
    .el-textarea__inner {
      background: #1c1e23;
      color: #ffffff;
      resize: none;

      &::placeholder {
        color: #999999;
      }
    }
  }

  .font-select {
    margin-bottom: 16px;

    label {
      display: block;
      font-size: 12px;
      color: #cccccc;
      margin-bottom: 4px;
    }

    :deep(.el-select) {
      width: 100%;

      .el-input__inner {
        background: #4a4a4a;
        border-color: #606060;
        color: #ffffff;
      }
    }
  }

  .size-input-group {
    display: flex;
    gap: 8px;
    align-items: center;
    margin-top: 10px;

    :deep(.el-select) {
      width: 80px;

      .el-input__inner {
        background: #4a4a4a;
        border-color: #606060;
        color: #ffffff;
      }
    }

    .size-controls {
      display: flex;
      align-items: center;
      gap: 4px;
    }
  }

  .color-controls {
    display: flex;
    align-items: center;
    gap: 10px;
    .color-item {
      margin-bottom: 12px;

      label {
        display: block;
        font-size: 12px;
        color: #cccccc;
        margin-bottom: 4px;
      }

      .color-input-group {
        display: flex;
        align-items: center;
        gap: 8px;

        :deep(.el-color-picker) {
          .el-color-picker__trigger {
            width: 116px;
            height: 30px;
            border-radius: 4px;
            border: 1px solid #606060;

            &:hover {
              border-color: #ff6b35;
            }
          }
        }

        :deep(.el-input) {
          flex: 1;

          .el-input__inner {
            background: #4a4a4a;
            border-color: #606060;
            color: #ffffff;
            height: 24px;
            line-height: 24px;
          }
        }

        :deep(.el-input-number) {
          width: 60px;

          .el-input__inner {
            background: #4a4a4a;
            border-color: #606060;
            color: #ffffff;
            height: 24px;
            line-height: 24px;
          }
        }
      }
    }
  }
}
</style>
