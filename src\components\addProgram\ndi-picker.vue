<template>
  <el-dialog class="ndi-dialog" title="选择NDI网络流" :visible.sync="visible" width="800px" top="15vh"
    append-to-body :close-on-click-modal="false" @close="close()">
    <div class="ndi-box">
      <div v-for="(item, index) in dataList" :key="item" @click="pickItem(index)"
        class="each-ndi" :class="{ active: activeIndex === index }">
        <div class="view-box">
          <img src="" alt="" v-if="false">
        </div>
        <div class="ndi-name">{{ item }}</div>
      </div>
    </div>
    <span slot="footer" class="dialog-footer">
      <span class="g-option-btn-1" style="width: 84px" @click="confirm">确定</span>
      <span class="g-option-btn-2" style="width: 84px" @click="close">取消</span>
    </span>
  </el-dialog>
</template>

<script>
import liveApi from 'api/live'

export default {
  name: 'ndiPicker',
  components: {},
  props: {},
  computed: {},
  data() {
    return {
      visible: true,
      dataList: [],
      activeIndex: -1
    }
  },
  methods: {
    getList() {
      liveApi.getNdiList({}, (res) => {
        this.dataList = Array.isArray(res.ndiSources) ? res.ndiSources : []
      }, (code, msg) => {
        this.$message.error(msg)
      })
    },
    pickItem(index) {
      this.activeIndex = index
    },
    close() {
      this.$emit('close')
    },
    confirm() {
      if (this.activeIndex < 0) {
        this.$message.warning('请选择NDI网络流')
        return
      }
      this.$emit('confirm', this.dataList[this.activeIndex])
      this.$emit('close')
    }
  },
  created() {
    this.getList()
  },
  mounted() {}
}
</script>

<style scoped lang="scss">
.ndi-dialog {
  height: 100%;
}
::v-deep .el-dialog__body {
  padding: 16px 20px 0 20px;
}
.ndi-box {
  height: 50vh;
  display: flex;
  flex-wrap: wrap;
  align-items: flex-start;
  overflow: auto;
  .each-ndi {
    flex: 0 0 30%;
    margin: 5px 1.5% 10px;
    cursor: pointer;
    .view-box {
      position: relative;
      width: 100%;
      padding-top: 56.25%;
      border-radius: 5px 5px 0 0;
      background-color: #000;
      img {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        border-radius: 5px 5px 0 0;
      }
    }
    .ndi-name {
      height: 30px;
      line-height: 30px;
      text-align: center;
      font-size: 12px; 
      color: #BEC9E5;
      background-color: #2d333e;
      padding: 0 5px;
      @include elliptical();
    }
    &.active {
      outline: 2px solid #E95C2B;
    }
  }
}
</style>
