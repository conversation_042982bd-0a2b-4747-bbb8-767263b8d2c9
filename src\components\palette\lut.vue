<template>
  <div class="lut">
    <div class="flex-v-c" style="margin-bottom: 15px">
      <el-upload :action="uploadUrl" :before-upload="beforeUpload" multiple accept=".cube" :on-success="handleSuccess"
        :on-error="handleError" :show-file-list="false">
        <span class="my-grey-btn flex-v-c" slot="trigger">
          <i class="el-icon-upload2"></i>
          上传
        </span>
      </el-upload>
      <span class="tips">支持cube格式</span>
    </div>
    <el-scrollbar class="list-scrollbar" wrap-class="scrollbar-wrapper" v-loading="pageList.loading">
      <ul class="lut-list">
        <li @click="onChangeLutPath({})" :class="{ active: !lutData.path }">
          不使用LUT
        </li>
        <li v-for="item in pageList.data" :class="{ active: lutData.path === item.path }"
          @click="onChangeLutPath(item)" :key="item.path">
          {{ item.fileName }}
        </li>
      </ul>
    </el-scrollbar>
    <!-- <div class="lut-strength">
      <div class="title-strength">数值调整：</div>
      <div class="flex-v-c" style="width: 100%">
        <el-slider v-model="lutStrength" @change="onChangeStrength" :step="0.1" :min="0" :max="1"
          style="padding: 0 12px; flex-grow: 1"></el-slider>
        <span class="strength-value">{{ lutStrength }}</span>
      </div>
    </div> -->
    <div class="footer-bar" style="text-align: right; margin-top: 30px">
      <el-button type="primary" size="medium" @click="confirm()">应用</el-button>
      <el-button type="info" plain size="medium" @click="$emit('close')">取消</el-button>
    </div>
  </div>
</template>

<script>
import Upload from 'common/js/upload'
import cloudApi from 'api/cloud'
import { mapGetters } from 'vuex'
import cube2png from './cube2png'
var triangle = require('a-big-triangle')
var createContext = require('gl-context')
var createTex2d = require('gl-texture2d')
const DEFAULT_LUT_LIST = [
  { fileName: 'Cinema_Look.cube', path: '/data/lut/Cinema_Look_18.cube', transformFilePath: '/data/lut/Cinema_Look_18.png'},
  { fileName: 'Cinema_portrait.cube', path: '/data/lut/Cinema_portrait.cube', transformFilePath: '/data/lut/Cinema_portrait.png' },
  { fileName: 'Cinematic.cube', path: '/data/lut/Cinematic_6.cube', transformFilePath: '/data/lut/Cinematic_6.png' },
  { fileName: 'Commercial.cube', path: '/data/lut/Commercial_8.cube', transformFilePath: '/data/lut/Commercial_8.png' },
  { fileName: 'Film.cube', path: '/data/lut/Film_5.cube', transformFilePath: '/data/lut/Film_5.png' },
  { fileName: 'Forest.cube', path: '/data/lut/Forest_1.cube', transformFilePath: '/data/lut/Forest_1.pngg' },
  { fileName: 'Hallucination_Color_Grade.cube', path: '/data/lut/Hallucination_Color_Grade_07.cube', transformFilePath: '/data/lut/Hallucination_Color_Grade_07.png' },
  { fileName: 'LD_Canyon_Red.cube', path: '/data/lut/LD_Canyon_Red_1.cube', transformFilePath: '/data/lut/LD_Canyon_Red_1.png' },
  { fileName: 'Movie_21Century.cube', path: '/data/lut/Movie_21Century_21.cube', transformFilePath: '/data/lut/Movie_21Century_21.png' },
  { fileName: 'Wedding.cube', path: '/data/lut/Wedding_8.cube', transformFilePath: '/data/lut/Wedding_8.png' }
]

function getVert () {
  return `
    #define GLSLIFY 1

    precision mediump float;

    attribute vec2 position;
    varying vec2 vUv;

    void main() {
      vUv = (position + vec2(1.0)) / 2.0;
      vUv.y = 1.0 - vUv.y;
      gl_Position = vec4(position, 1.0, 1.0);
    }
  `
}

function getFrag () {
  return `
    #define GLSLIFY 1

    precision mediump float;

    uniform sampler2D uTexture;
    uniform sampler2D uLookup;
    uniform int textureSample;
    varying vec2 vUv;

    vec4 lookup_1_0(in vec4 textureColor, in sampler2D lookupTable) {
        #ifndef LUT_NO_CLAMP
            textureColor = clamp(textureColor, 0.0, 1.0);
        #endif

        mediump float blueColor = textureColor.b * 63.0;

        mediump vec2 quad1;
        quad1.y = floor(floor(blueColor) / 8.0);
        quad1.x = floor(blueColor) - (quad1.y * 8.0);

        mediump vec2 quad2;
        quad2.y = floor(ceil(blueColor) / 8.0);
        quad2.x = ceil(blueColor) - (quad2.y * 8.0);

        highp vec2 texPos1;
        texPos1.x = (quad1.x * 0.125) + 0.5/512.0 + ((0.125 - 1.0/512.0) * textureColor.r);
        texPos1.y = (quad1.y * 0.125) + 0.5/512.0 + ((0.125 - 1.0/512.0) * textureColor.g);

        #ifdef LUT_FLIP_Y
            texPos1.y = 1.0-texPos1.y;
        #endif

        highp vec2 texPos2;
        texPos2.x = (quad2.x * 0.125) + 0.5/512.0 + ((0.125 - 1.0/512.0) * textureColor.r);
        texPos2.y = (quad2.y * 0.125) + 0.5/512.0 + ((0.125 - 1.0/512.0) * textureColor.g);

        #ifdef LUT_FLIP_Y
            texPos2.y = 1.0-texPos2.y;
        #endif

        lowp vec4 newColor1 = texture2D(lookupTable, texPos1);
        lowp vec4 newColor2 = texture2D(lookupTable, texPos2);

        lowp vec4 newColor = mix(newColor1, newColor2, fract(blueColor));
        return newColor;
    }



    void main() {
      vec4 color = texture2D(uTexture, vUv);
      if (textureSample == 1) {
        gl_FragColor = lookup_1_0(color, uLookup);
      } else {
        gl_FragColor = color;
      }
    }
  `
}
export default {
  name: 'lut',
  props: {
    lutData: {
      type: Object,
      default: () => { }
    },
    videoDom: {
      type: Element,
      default: () => null
    },
    canvasDom: {
      type: Element,
      default: () => null
    }
  },
  computed: {
    ...mapGetters(['instanceId'])
  },
  data () {
    return {
      uploadUrl: '/cloud/fullTimeLive/lut/upload?instanceId=',
      pageList: {
        data: [],
        page: this.Page(), // 对page初始化 page={pageIndex: 1,amount: 0,pageSize: 20}
        loading: false
      },
      lutStrength: this.lutData.factor,
      tempCanvas: null,
      tempCtx: null,
      gl: null,
      lutTex: null,
      shader: null,
      uploadQueue: {}
    }
  },
  methods: {
    onChangeLutPath (item) {
      this.lutData.path = item.path
      this.lutData.transformFilePath = item.transformFilePath
      this.getLutTex()
      this.changeLut()
    },
    onChangeStrength () {
      this.changeLut()
    },
    changeLut () {
      this.$emit('change', {
        path: this.lutData.path,
        factor: this.lutStrength
      })
    },
    handleSuccess (res, file) {
      if (res) {
        this.uploadCubeAndPng(file, res.value.visitUrl)
        // this.uploadFile({ fileName: file.name, path: res.value.visitUrl })
      }
    },
    handleError () {
      this.$message.error('上传失败')
    },
    async beforeUpload (file) {
      let fileType = file.name.substring(
        file.name.lastIndexOf('.') + 1,
        file.name.length
      )
      let uploadType = Upload.getUploadType(fileType)
      if (!['cube'].includes(fileType.toLowerCase())) {
        this.$message.error('请上传正确的格式文件')
        reject()
      }
      var errorMsg = Upload.checkFileSize(uploadType, file.size)
      if (errorMsg !== '') {
        this.$message.error(errorMsg)
        reject()
      }
      this.uploadFlag = true
      this.doCubeToPng(file)
    },
    doCubeToPng (file) {
      try {
        cube2png(file, (canvas) => {
          if (!canvas) return this.$message.error(file.name + '解析失败，请尝试更换其他lut文件重新上传')
          let dataUrl = canvas.toDataURL('image/png')
          let fileName = file.name.substring(0, file.name.lastIndexOf('.'))

          this.dataUrlToFile(dataUrl, fileName + '.png', file.uid)
        })
      } catch (e) {
      }
    },
    dataUrlToFile (dataUrl, fileName, uid) {
      let arr = dataUrl.split(','); let mime = arr[0].match(/:(.*?);/)[1]
      let bstr = atob(arr[1])
      let n = bstr.length
      let u8arr = new Uint8Array(n)
      while (n--) {
        u8arr[n] = bstr.charCodeAt(n)
      }
      let file = new File([u8arr], fileName, { type: mime })
      file.uid = uid
      Upload.uploadFile(this.uploadUrl, file, this.uploadCubeAndPng, () => { this.$message.error('上传失败') })
    },
    uploadCubeAndPng (file, path) {
      let fileType = file.name.substring(
        file.name.lastIndexOf('.') + 1,
        file.name.length
      )
      let cubeAndPng = this.uploadQueue[file.uid]
      if (cubeAndPng) {
        cubeAndPng[fileType] = { fileName: file.name, path: path }
        // 查询cube和png是否都上传完成
        let canUpload = true
        for (let key in cubeAndPng) {
          if (!cubeAndPng[key]) {
            canUpload = flase
          }
        }
        // 如果上传完成则一起发送给后台
        if (canUpload) {
          this.uploadFile({ fileName: cubeAndPng.cube.fileName, path: cubeAndPng.cube.path, transformFilePath: cubeAndPng.png.path })
        }
      } else {
        cubeAndPng = {
          cube: null,
          png: null
        }
        cubeAndPng[fileType] = { fileName: file.name, path: path }
        this.uploadQueue[file.uid] = cubeAndPng
      }
    },
    uploadFile (param) {
      cloudApi.uploadLut(param, () => {
        this.getLutPage()
      })
    },
    confirm () {
      this.$emit('set', {type: 'lut'})
    },
    getLutPage (page) {
      let param = {}
      param.page = page || this.pageList.page.pageIndex
      param.pageSize = 1000
      cloudApi.getLutList(param, (res) => {
        if (res) {
          this.pageList.data = [...DEFAULT_LUT_LIST, ...res.data]
        }
        this.GetPage(this.pageList, res)
      })
    },
    render () {
      if (!this.videoDom.paused) {
        this.tempCanvas.width = this.videoDom.videoWidth
        this.tempCanvas.height = this.videoDom.videoHeight
        this.tempCtx.drawImage(this.videoDom, 0, 0, this.videoDom.videoWidth, this.videoDom.videoHeight)
        var videoTex = createTex2d(this.gl, this.tempCanvas)
        if (!this.shader) return
        this.shader.bind()
        this.shader.uniforms.uTexture = videoTex.bind(0)
        if (this.lutTex) {
          this.shader.uniforms.uLookup = this.lutTex.bind(1)
          this.shader.uniforms.textureSample = 1
        } else {
          this.shader.uniforms.uLookup = null
          this.shader.uniforms.textureSample = 0
        }
        triangle(this.gl)
      }
    },
    getLutTex () {
      this.lutTex = null
      if (!this.lutData) return
      if (this.lutData.transformFilePath) {
        this.getImageCanvas(`${window.location.origin}${this.lutData.transformFilePath}`, (canvas) => {
        // this.getImageCanvas(`http://172.16.30.25/data/lut/a5d39ee3e695753c80f7319154ef9b9f.png`, (canvas) => {
          this.lutTex = createTex2d(this.gl, canvas)
          this.lutTex.minFilter = this.lutTex.magFilter = this.gl.LINEAR
        })
      } else if (this.lutData.path) {
 cube2png(`${window.location.origin}${this.lutData.path}`, (canvas) => {
        // cube2png(`http://172.16.30.25/data/lut/Cinema_Look_18.cube`, (canvas) => {
          this.lutTex = createTex2d(this.gl, canvas)
          this.lutTex.minFilter = this.lutTex.magFilter = this.gl.LINEAR
        })
}
    },
    getImageCanvas (path, callback) {
      let img = new Image()
      img.src = path
      img.onload = () => {
        var canvas = document.createElement('canvas')
        canvas.width = 512
        canvas.height = 512
        const ctx = canvas.getContext('2d')
        ctx.drawImage(img, 0, 0)
        callback(canvas)
      }
    },
    init () {
      this.tempCanvas = document.createElement('canvas')
      this.tempCtx = this.tempCanvas.getContext('2d')
      this.gl = createContext(this.canvasDom, this.render)
      this.getLutTex()
      this.shader = require('gl-shader')(this.gl, getVert(), getFrag())
    }
  },
  mounted () {
    this.init()
  },
  created () {
    this.uploadUrl = this.uploadUrl + this.instanceId
    this.getLutPage(1)
  },
  beforeDestroy () {
    this.shader && this.shader.dispose()
    this.shader = null
  }
}
</script>

<style scoped lang="scss">
.lut {
  padding-top: 20px;

  .tips {
    margin-left: 15px;
    color: rgba(118, 128, 149, 1);
  }

  .list-scrollbar {
    height: 288px;
    background-color: rgba(41, 45, 54, 1);
  }

  .lut-list li {
    height: 38px;
    color: rgba(180, 190, 213, 1);
    line-height: 38px;
    padding: 0 19px;
    @include elliptical();
    font-size: 14px;
    cursor: pointer;

    &:hover {
      background-color: rgba(59, 65, 79, 1);
    }

    &.active {
      background-color: rgba(63, 128, 226, 1);
      color: #fff;
    }
  }

  .lut-strength {
    margin-top: 16px;

    .title-strength {
      font-size: 14px;
      color: #b4bed5;
    }

    .strength-value {
      display: inline-block;
      width: 30px;
      text-align: center;
      color: #b4bed5;
    }
  }
}

.el-icon-upload2 {
  margin-right: 5px;
}
</style>
