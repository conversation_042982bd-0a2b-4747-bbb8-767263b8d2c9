<template>
  <div id="app" style="position: relative;height: 100%">
    <router-view style="height: 100%;"></router-view>
  </div>
</template>

<script>
export default {
  name: 'app',
  data () {
    return {
      time: 0
    }
  },

  components: {},

  methods: {},
  created () {
    // 用于定时刷新页面,防止页面宕机
    this.time = new Date().getTime()
    setInterval(() => {
      if (new Date().getTime() - 6 * 60 * 60 * 1000 > this.time) {
        location.reload(true)
      }
    }, 5 * 60 * 1000)
  }
}
</script>

<style scoped>
</style>
