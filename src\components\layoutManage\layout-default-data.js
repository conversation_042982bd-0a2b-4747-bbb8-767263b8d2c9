// 主画面
export function DefaultLayout () {
  return {
    id: parseInt(Math.random() * 1000000),
    type: 'main', // 布局类型
    name: '主画面', // 名称，用于设置区域的title显示
    previewImg: '', // 预览区中只展示，预览图
    objectFit: 'contain', // 预览图填充方式
    disableDelete: true, // 是否禁用删除
    height: 100, // 高度，需要后面根据ratio和预览区的大小计算出实际高度
    width: 100, // 宽度，需要后面根据ratio和预览区的大小计算出实际宽度
    dx: 0, // 距离预览区左边的距离，需要后面根据宽高和预览区的大小计算出实际距离
    dy: 0, // 距离预览区顶部的距离，需要后面根据宽高和预览区的大小计算出实际距离
    ratio: '16:9', // 宽高比，如果为0则表示自定义，如果为其他值则whLocked为true
    whLocked: true, // 宽高是否锁定，如果为true则表示锁定
    zIndex: 1 // 层级
  }
}
// 直播源
export function DefaultStream () {
  return {
    id: parseInt(Math.random() * 1000000),
    type: 'stream', // 布局类型
    name: '主画面', // 名称，用于设置区域的title显示
    url: '', // 直播源地址
    previewImg: '', // 预览区中只展示，预览图
    previewImgWidth: 100, // 预览图宽度
    previewImgHeight: 100, // 预览图高度
    objectFit: 'contain', // 预览图填充方式
    height: 100, // 初始高度，需要后面根据ratio和预览区的大小计算出实际高度
    width: 100, // 初始宽度，需要后面根据ratio和预览区的大小计算出实际宽度
    dx: 0, // 距离预览区左边的距离，需要后面根据宽高和预览区的大小计算出实际距离
    dy: 0, // 距离预览区顶部的距离，需要后面根据宽高和预览区的大小计算出实际距离
    ratio: '16:9', // 宽高比，如果为0则表示自定义，如果为其他值则whLocked为true
    whLocked: false, // 宽高是否锁定，如果为true则表示锁定
    volume: 100, // 音量0到100
    zIndex: 1 // 层级
  }
}
// 点播源
export function DefaultVideo () {
  return {
    id: parseInt(Math.random() * 1000000),
    type: 'video', // 布局类型
    name: '点播源', // 名称，用于设置区域的title显示
    source: 'online', // 来源，online在线，local本地
    url: '', // 点播源地址
    previewImg: '', // 预览区中只展示预览图
    previewImgWidth: 100, // 预览图宽度
    previewImgHeight: 100, // 预览图高度
    objectFit: 'contain', // 预览图填充方式
    height: 100, // 初始高度，需要后面根据ratio和预览区的大小计算出实际高度
    width: 100, // 初始宽度，需要后面根据ratio和预览区的大小计算出实际宽度
    dx: 0, // 距离预览区左边的距离，需要后面根据宽高和预览区的大小计算出实际距离
    dy: 0, // 距离预览区顶部的距离，需要后面根据宽高和预览区的大小计算出实际距离
    ratio: '16:9', // 宽高比，如果为0则表示自定义，如果为其他值则whLocked为true
    playType: 'loop', // 播放模式，loop循环，once一次
    whLocked: false, // 宽高是否锁定，如果为true则表示锁定
    volume: 100, // 音量0到100
    zIndex: 1 // 层级
  }
}
// 背景
export function DefaultBg () {
  return {
    id: parseInt(Math.random() * 1000000),
    type: 'bg', // 布局类型
    name: '背景', // 名称，用于设置区域的title显示
    dx: 0, // 距离预览区左边的距离
    dy: 0, // 距离预览区顶部的距离
    height: 200, // 初始高度
    width: 200, // 初始宽度
    url: '', // 图片地址
    gaussianBlur: 0, // 高斯模糊开关
    gaussianBlurRadius: 10, // 高斯模糊半径，默认10px
    previewImgWidth: 100, // 预览图宽度
    previewImgHeight: 100, // 预览图高度
    objectFit: 'contain', // 背景图填充方式
    opacity: 100, // 不透明度0-100
    zIndex: 0 // 层级，背景应该在最底层
  }
}
// 节目单
export function DefaultProgram () {
  return {
    id: parseInt(Math.random() * 1000000),
    type: 'program', // 布局类型
    programId: 0, // 节目单id
    name: '节目单', // 名称，用于设置区域的title显示
    dx: 0, // 距离预览区左边的距离
    dy: 0, // 距离预览区顶部的距离
    height: 200, // 初始高度
    width: 200, // 初始宽度
    previewImgWidth: 100, // 预览图宽度
    previewImgHeight: 100, // 预览图高度
    objectFit: 'contain', // 画面填充方式
    opacity: 100, // 不透明度0-100
    zIndex: 0, // 层级，背景应该在最底层
    whLocked: false, // 宽高是否锁定，如果为true则表示锁定
    playType: 'loop', // 播放模式，loop循环，once一次
    volume: 100 // 音量0到100
  }
}
// 图片
export function DefaultImage (width, height) {
  return {
    id: parseInt(Math.random() * 1000000),
    type: 'logo',
    name: '图片',
    height: 100, // 初始高度，需要后面根据实际的图片宽高和预览区的大小计算出预览高度
    width: 100, // 初始宽度，需要后面根据实际的图片宽高和预览区的大小计算出预览宽度
    dx: 0, // 距离预览区左边的距离，需要后面根据宽高和预览区的大小计算出预览距离
    dy: 0, // 距离预览区顶部的距离，需要后面根据宽高和预览区的大小计算出预览距离
    whLocked: false, // 宽高是否锁定，如果为true则表示锁定
    url: '',
    objectFit: 'contain', // 图片填充方式
    gaussianBlur: 0,
    gaussianBlurRadius: 10, // 高斯模糊半径，默认10px
    opacity: 100, // 不透明度0-100
    zIndex: 1 // 层级
  }
}
// 文字
export function DefaultSubtitle () {
  return {
    id: parseInt(Math.random() * 1000000),
    type: 'subtitle',
    name: '文字',
    dx: 100,
    dy: 100,
    height: 60,
    width: 200,
    whLocked: false, // 宽高是否锁定，如果为true则表示锁定
    color: '#ffffff', // 字体颜色
    font: 'Microsoft YaHei', // 字体
    size: 27, // 字体大小
    text: '默认文字',
    zIndex: 1,
    textShadow: { // 文字阴影
      color: '#000000', // 颜色
      blur: 10, // 模糊半径
      offsetX: 0, // 水平偏移
      offsetY: 0 // 垂直偏移
    }
  }
}
// 网页
export function DefaultWeb () {
  return {
    id: parseInt(Math.random() * 1000000),
    type: 'web',
    name: '网页',
    height: 100, // 初始高度，需要后面根据ratio和预览区的大小计算出实际高度
    width: 100, // 初始宽度，需要后面根据ratio和预览区的大小计算出实际宽度
    dx: 0, // 距离预览区左边的距离，需要后面根据宽高和预览区的大小计算出实际距离
    dy: 0, // 距离预览区顶部的距离，需要后面根据宽高和预览区的大小计算出实际距离
    whLocked: false, // 宽高是否锁定，如果为true则表示锁定
    url: '',
    gaussianBlur: 0,
    zIndex: 1, // 层级
    crop: { // 裁剪信息
      dx: 0,
      dy: 0,
      width: 1,
      height: 1
    }
  }
}
