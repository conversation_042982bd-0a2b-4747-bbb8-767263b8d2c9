<template>
  <remote-js :src="this.url" v-if="type === 'js'" @load-remote-finish="this.loadCallBack"></remote-js>
  <remote-css :src="this.url" v-else-if="type === 'css'"></remote-css>
</template>

<script>
  export default {
    components: {
      'remote-js': {
        render (createElement) {
          let self = this
          return createElement('script', {
            attrs: {type: 'text/javascript', src: this.src},
            on: {
              load: function () {
                self.$emit('load-remote-finish')
              }
            }
          })
        },
        props: {
          src: {type: String, required: true}
        }
      },
      'remote-css': {
        render (createElement) {
          let self = this
          return createElement('link', {
            attrs: {rel: 'stylesheet', type: 'text/css', href: this.src},
            on: {
              load: function () {
                self.$emit('load-remote-finish')
              }
            }
          })
        },
        props: {
          src: {type: String, required: true}
        }
      }
    },
    props: {
      url: {type: String, required: true}, // 需要加载的外部url
      type: {type: String, required: true, default: 'js'},
      loadCallBack: {type: Function, default: () => {}}
    }
  }
</script>
