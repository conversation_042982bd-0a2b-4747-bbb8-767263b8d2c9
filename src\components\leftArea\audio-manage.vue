<template>
  <div class="audio-manage">
    <div class="audio-left">
      <div class="list-top-title">音频管理</div>
      <div class="list-item" :class="{'active': activeIndex === -1}"
        style="margin-top: 22px;" @click="switchPlayItem(-1)">
        <div class="item-name">
          <i class="el-icon-plus"></i>
          全部音频
        </div>
      </div>
      <el-divider></el-divider>
      <div class="list-top-title">
        自建播单
        <div class="btn-like" @click="showPlayDialog(null)">
          <i class="el-icon-plus add-icon"></i>
          新增播单
        </div>
      </div>
      <div class="list-wrapper">
        <draggable v-model="playList" :options="{handle: '.sortable-item'}" @update="updateOrder">
          <div class="list-item" :class="{ 'sortable-item': index > 0, 'active': activeIndex === index }"
              v-for="(item, index) in playList" :key="item.id" @click="switchPlayItem(index)">
            <div class="item-name">
              {{ item.name }}
            </div>
            <div class="iten-operation" v-if="index !== 0">
              <svg-icon class="mr-icon" icon-class="edit"
                @click.native="showPlayDialog(item)"></svg-icon>
              <svg-icon icon-class="delete" @click.native="delPlayItem(item)"></svg-icon>
            </div>
          </div>
        </draggable>
      </div>
    </div>
    <div class="audio-right">
      <div class="top-wrapper">
        <el-input placeholder="请输入名称" class="top-search-input" v-model.trim="searchWords">
          <el-button class="search-button" slot="append" icon="search" @click="getPackageInfo(1)">
            <i class="el-icon-search"></i>
            搜索</el-button>
        </el-input>
        <div v-show="activeIndex === -1">
          <span class="g-option-btn-2" @click="showAddMusic" style="width: 128px;">
            <svg-icon icon-class="music" style="margin-right: 5px;font-size: 14px"></svg-icon>
            <span>添加音频流</span>
          </span>
          <span class="g-option-btn-1" @click="showUploadAudio" style="width: 84px;">
            <i class="icon el-icon-upload2"></i>
            <span>上传</span>
          </span>
        </div>
      </div>
      <div class="audio-wrapper">
        <div class="audio-header">
          <span class="audio-checked">
            <el-checkbox :indeterminate="isIndeterminate" v-model="checkAll"
              @change="handleCheckAllChange"></el-checkbox>
          </span>
          <span class="audio-name">音频名</span>
          <span class="audio-type">类型</span>
          <span class="audio-attribute">音频属性</span>
          <span class="upload-time">上传时间</span>
          <span class="audio-operation">操作
            <el-tooltip effect="light" content="部分音频格式文件以及音频流不支持查看，请关联节目单后预览查看" placement="top">
              <span class="el-icon-warning" style="margin-left: 5px"></span>
            </el-tooltip>
          </span>
        </div>
        <ul class="audio-ul">
          <el-checkbox-group v-model="checkedIds" style="font-size: 16px" @change="handleChecked">
            <li class="audio-li" v-for="item in packageInfo.data" :key="item.id">
              <span class="audio-checked">
                <el-checkbox :label="item.id"><br></el-checkbox>
              </span>
              <span class="audio-name">
                <span class="name" :title="item.fileName">{{item.fileName}}</span>
              </span>
              <span class="audio-type">
                <span style="white-space: nowrap;" v-if="item.type">音频流</span>
                <span style="white-space: nowrap;" v-else>点播</span>
              </span>
              <span class="audio-attribute">
                <template v-if="!item.type">
                  <span style="white-space: nowrap;">格式：{{getFileType(item.fileName)}}</span>
                  <span
                    style="white-space: nowrap;">大小：{{(item.fileSize / 1024 / 1024).toFixed(2)}}MB</span>
                  <span
                    style="white-space: nowrap;">时长：{{parseInt(item.duration / 1000) | formatDuration}}</span>
                </template>
                <span v-else class="audio-path" :title="item.path">{{item.path}}</span>
              </span>
              <span class="upload-time">{{item.createTime | formatDateCustom}}</span>
              <span class="audio-operation">
                <el-button type="text" class="text-btn" @click="watchAudio(item)">查看</el-button>
                <el-dropdown style="margin-left: 10px" v-if="activeIndex >= 0">
                  <el-button type="text" class="text-btn">更多</el-button>
                  <el-dropdown-menu slot="dropdown">
                    <el-dropdown-item @click.native="showJoinPlay(item)">添加到</el-dropdown-item>
                    <el-dropdown-item @click.native="showTransferPlay(item)">移动到</el-dropdown-item>
                    <el-dropdown-item @click.native="delItemFromPlay(item)">删除</el-dropdown-item>
                  </el-dropdown-menu>
                </el-dropdown>
                <el-dropdown style="margin-left: 10px" v-else>
                  <el-button type="text" class="text-btn">更多</el-button>
                  <el-dropdown-menu slot="dropdown">
                    <el-dropdown-item @click.native="showJoinPlay(item)">添加到</el-dropdown-item>
                    <el-dropdown-item @click.native="deleteAudio([item.id])">删除</el-dropdown-item>
                  </el-dropdown-menu>
                </el-dropdown>
              </span>
            </li>
          </el-checkbox-group>
          <div class="empty-box wh100" v-if="packageInfo.data && !packageInfo.data.length">
            <no-data tips="暂无音频"></no-data>
          </div>
        </ul>
      </div>
      <div class="audio-bottom">
        <div>
          <el-button type="text" @click="deleteAudio(checkedIds)" class="text-btn">删除</el-button>
          <span style="margin-left: 30px">存储量：</span>
          <span
            style="color: #FF5712">{{$_getSizeFormat(Number(localStorageInfo.currentVolume))}}</span>
          <span>{{'/' + Number(localStorageInfo.maxVolume) / (1024 * 1024 * 1024)}}GB</span>
        </div>
        <div>
          <footer-pagination small :page="packageInfo.page"
            @getPageInfo="getPackageInfo"></footer-pagination>
        </div>
      </div>
    </div>

    <upload-audio v-if="uploadAudioDialog" @close="uploadAudioDialog=false"
      @uploadCallback="uploadCallback"></upload-audio>
    <add-music-dialog v-if="showAddMusicDialog" @close="showAddMusicDialog = false"
      @uploadCallback="uploadCallback"></add-music-dialog>

    <el-dialog :title="editPlayItem?'编辑播单':'新增播单'" :visible.sync="playDialog.show" width="30%">
      <el-form label-width="80px">
        <el-form-item label="活动名称">
          <el-input v-model.trim="playDialog.name" maxlength="18" placeholder="请输入播单名称"
            show-word-limit></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <span class="g-option-btn-1" style="width: 84px" @click="confirmPlayItem">确定</span>
        <span class="g-option-btn-2" style="width: 84px" @click="playDialog.show = false">取消</span>
      </span>
    </el-dialog>

    <el-dialog :title="playSelectDialogTitle" :visible.sync="playSelectDialog.show" width="20%">
      <div>
        <el-select v-model="playSelectDialog.value" placeholder="请选择播单">
          <el-option v-for="item in playList" :label="item.name" :value="item.id" :key="item.id"></el-option>
        </el-select>
      </div>
      <span slot="footer" class="dialog-footer">
        <span class="g-option-btn-1" style="width: 84px" @click="confirmPlaySelect">确定</span>
        <span class="g-option-btn-2" style="width: 84px" @click="playSelectDialog.show = false">取消</span>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import footerPagination from '@/components/pagination/footer-pagination'
import uploadAudio from '@/components/base/upload-audio'
import cloudApi from 'api/cloud'
import liveApi from 'api/live'
import { mapGetters, mapMutations, mapActions } from 'vuex'
import noData from 'components/base/no-data'
import addMusicDialog from './add-music-dialog'
import draggable from 'vuedraggable'

export default {
  name: 'audio-manage',
  components: { footerPagination, uploadAudio, noData, addMusicDialog, draggable },
  data() {
    return {
      playList: [],
      activeIndex: -1,
      editPlayItem: null,
      playDialog: {
        show: false,
        name: ''
      },
      playSelectDialog: {
        show: false,
        type: 'copy', // copy 添加 trasnfer 移动
        value: '',
        audioId: '',
        srcMenuId: ''
      },
      searchWords: '',
      packageInfo: {
        data: [],
        page: this.Page()
      },
      checkedIds: [],
      checkAll: false,
      isIndeterminate: false,
      uploadAudioDialog: false,
      allCheckedIds: [],
      programList: [],
      showAddMusicDialog: false
    }
  },
  computed: {
    playSelectDialogTitle() {
      return this.playSelectDialog.type === 'copy' ? '添加到播单' : '移动到播单'
    },
    ...mapGetters([
      'todayPrograms',
      'loopPrograms',
      'localStorageInfo',
      'programs',
      'audio',
      'instanceId'
    ])
  },
  methods: {
    getPlayList() {
      liveApi.getAudioPlayList({ instanceId: this.$store.getters.instanceId }, (res) => {
        this.playList = Array.isArray(res) ? res : []
      })
    },
    switchPlayItem(index) {
      this.activeIndex = index
      this.getPackageInfo(1)
    },
    showPlayDialog(item) {
      this.editPlayItem = item
      this.playDialog.name = item ? item.name : ''
      this.playDialog.show = true
    },
    confirmPlayItem() {
      if (this.playDialog.name === '') {
        this.$message.error('请输入播单名称')
        return
      }
      const param = {
        name: this.playDialog.name
      }
      const api = this.editPlayItem ? liveApi.updAudioPlay : liveApi.addAudioPlay
      if (this.editPlayItem) {
        param.id = this.editPlayItem.id
      } else {
        param.instanceId = this.instanceId
      }

      api(
        param,
        () => {
          this.$message.success('操作成功')
          this.getPlayList()
          this.playDialog.show = false
        },
        (code, msg) => {
          this.$message.error(msg)
        }
      )
    },
    delPlayItem(item) {
      this.$confirm('确定删除吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          liveApi.delAudioPlay({ ids: [item.id] }, () => {
            this.$message.success('删除成功')
            this.getPlayList()
          }, (code, msg) => {
            this.$message.error(msg)
          })
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
    },
    updateOrder(e) {
      const { newDraggableIndex } = e
      if (newDraggableIndex === 0) {
        this.$message.warning('不能将第一个播单拖动到其他位置')
        this.activeIndex = -1
        this.getPlayList()
        return
      }
      const param = {
        items: this.playList.map((v, k) => {
          return {
            id: v.id,
            orderNum: this.playList.length - k
          }
        })
      }
      liveApi.sortAudioPlay(param, () => {
        this.$message.success('操作成功')
        this.activeIndex = -1
        this.getPlayList()
      }, (code, msg) => {
        this.$message.error(msg)
        this.getPlayList()
      })
    },
    watchAudio(item) {
      this.$bus.$emit('getVideoUrl', {
        videoUrl: item.visitUrl,
        name: item.fileName,
        type: 'audio'
      })
    },
    deleteAudio(ids) {
      let hadRelateBgm = false
      if (this.audio.audios && this.audio.audios.length) {
        this.audio.audios.forEach((item) => {
          if (ids.includes(item.audioId)) {
            hadRelateBgm = true
          }
        })
      }
      this.$confirm('删除音频将导致已关联的音频文件失效，请确认是否删除?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          cloudApi.deleteAudio({ ids: ids }, () => {
            if (hadRelateBgm) {
              let currentAudios = this.$_deepCopy(this.audio.audios) || []
              for (let i = this.audio.audios.length - 1; i >= 0; i--) {
                if (ids.includes(this.audio.audios[i].audioId)) {
                  currentAudios.splice(i, 1)
                }
              }
              let param = {
                audios: currentAudios
              }
              liveApi.setBgm(param, () => {
                this.setSoundMixerData({ audios: param.audios })
              })
            }
            if (
              this.packageInfo.data.length - ids.length === 0 &&
              this.packageInfo.page.pageIndex !== 1
            ) {
              this.getPackageInfo(this.packageInfo.page.pageIndex - 1)
            } else {
              this.getPackageInfo()
            }
            this.getFileStorage()
          })
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
    },
    showJoinPlay(item) {
      this.playSelectDialog.type = 'copy'
      this.playSelectDialog.value = ''
      this.playSelectDialog.audioId = item.id
      this.playSelectDialog.srcMenuId = this.playList[this.activeIndex] ? this.playList[this.activeIndex].id : 0
      this.playSelectDialog.show = true
    },
    showTransferPlay(item) {
      this.playSelectDialog.type = 'move'
      this.playSelectDialog.value = ''
      this.playSelectDialog.audioId = item.id
      this.playSelectDialog.srcMenuId = this.playList[this.activeIndex].id
      this.playSelectDialog.show = true
    },
    delItemFromPlay(item) {
      this.$confirm('确定删除吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        liveApi.removeItemFromPlay({
          audioId: item.id,
          playlistId: this.playList[this.activeIndex].id
        }, () => {
          this.$message.success('操作成功')
          this.getPackageInfo()
        }, (code, msg) => {
          this.$message.error(msg)
        })
      }).catch(() => {
        this.$message('已取消')
      })
    },
    confirmPlaySelect() {
      if (this.playSelectDialog.value === '') {
        this.$message.error('请选择播单')
        return
      }
      const api = this.playSelectDialog.type === 'copy'
        ? cloudApi.copyAudioToPlay
        : cloudApi.moveAudioToPlay
      const param = {
        audioId: this.playSelectDialog.audioId,
        sourcePlaylistId: this.playSelectDialog.srcMenuId
      }
      if (this.playSelectDialog.type === 'copy') {
        param.playlistId = this.playSelectDialog.value
      } else {
        param.targetPlaylistId = this.playSelectDialog.value
      }
      api(param, () => {
        this.$message.success('操作成功')
        this.getPackageInfo()
        this.playSelectDialog.show = false
      }, (code, msg) => {
        this.$message.error(msg)
      })
    },
    uploadCallback() {
      this.getFileStorage()
      this.getPackageInfo()
    },
    getFileType(name) {
      return name.substring(name.lastIndexOf('.') + 1, name.length)
    },
    getPackageInfo(page) {
      this.packageInfo.data = []
      let param = {
        name: this.searchWords,
        page: page || this.packageInfo.page.pageIndex,
        pageSize: 20
      }
      if (this.activeIndex >= 0) {
        param.playlistId = this.activeIndex === 0 ? 0 : this.playList[this.activeIndex].id
      }
      cloudApi.getAudioList(
        param,
        (res) => {
          if (res) {
            this.allCheckedIds = []
            this.packageInfo.data = res.data
            res.data.forEach((item) => {
              this.allCheckedIds.push(item.id)
            })
          }
          this.GetPage(this.packageInfo, res)
        },
        (code, msg) => {}
      )
    },
    showAddMusic() {
      this.showAddMusicDialog = true
    },
    showUploadAudio() {
      this.uploadAudioDialog = true
    },
    handleCheckAllChange(val) {
      this.checkedIds = val ? this.allCheckedIds : []
      this.isIndeterminate = false
    },
    handleChecked(value) {
      let checkedCount = value.length
      this.checkAll = checkedCount === this.allCheckedIds.length
      this.isIndeterminate = checkedCount > 0 && checkedCount < this.allCheckedIds.length
    },
    ...mapMutations({
      setLocalStorageInfo: 'SET_LOCAL_STORAGE_INFO',
      setSoundMixerData: 'SET_SOUND_MIXER'
    }),
    ...mapActions(['getFileStorage'])
  },
  created() {
    this.getPlayList()
    this.getPackageInfo()
  },
  mounted() {
    this.$bus.$on('locateMenu', (playId) => {
      const index = this.playList.findIndex(item => item.id === playId)
      this.switchPlayItem(index)
      this.$emit('switchTab', 'second')
    })
  }
}
</script>

<style scoped lang="scss">
.audio-manage {
  height: 100%;
  font-size: 16px;
  display: flex;
  .audio-left {
    flex: 0 0 324px;
    height: calc(100% - 8px);
    background-color: #292e37;
    border-radius: 12px;
    padding: 0 12px;
    box-sizing: border-box;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    .list-top-title {
      position: relative;
      flex: 0 0 32px;
      line-height: 32px;
      font-size: #bec9e5;
      font-weight: bold;
      color: #bec9e5;
      margin-top: 22px;
      .btn-like {
        position: absolute;
        right: 0;
        top: 0;
        width: 104px;
        height: 32px;
        background-color: #555d70;
        border-radius: 4px;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 14px;
        color: #bec9e5;
        .add-icon {
          margin-right: 5px;
        }
      }
    }
    .list-wrapper {
      margin-top: 19px;
      flex: 1;
      overflow: auto;
      scrollbar-width: none; /* firefox */
      -ms-overflow-style: none; /* IE 10+ */
      &::-webkit-scrollbar {
        display: none; /* Chrome Safari */
      }
    }
    .list-item {
      position: relative;
      width: 100%;
      height: 60px;
      margin-bottom: 2px;
      border-radius: 6px;
      display: flex;
      flex-direction: column;
      justify-content: center;
      padding: 0 12px;
      box-sizing: border-box;
      cursor: pointer;
      .item-name {
        font-size: 16px;
        color: #bec9e5;
        max-width: calc(100% - 87px);
        @include elliptical();
      }
      .iten-operation {
        position: absolute;
        right: 10px;
        top: 50%;
        transform: translateY(-50%);
        font-size: 12px;
        color: #bec9e5;
        cursor: pointer;
        opacity: 0;
        .mr-icon {
          margin-right: 12px;
        }
      }
      &:hover {
        background-color: #363943;
        .iten-operation {
          opacity: 1;
        }
      }
      &.active {
        background-color: #1f232b;
        border: 1px solid #e95c2b;
        .item-name {
          color: #e95c2b;
        }
      }
    }
  }
  .audio-right {
    position: relative;
    flex: 1;
    min-width: 0;
    margin-left: 16px;
  }
  .top-wrapper {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 50px;
    padding: 7px 15px;
    .top-search-input {
      width: 340px;
      height: 40px;
      border-radius: 4px;
      background: #1b1e24;
      border: 1px solid #535d72;
      box-sizing: border-box;
    }
    .search-button {
      background-color: #535d72;
      color: #bcc9e8;
      &:hover {
        background-color: #5e6678;
      }
    }
  }
  .audio-wrapper {
    height: calc(100% - 120px);
    box-sizing: border-box;
  }
  .audio-header {
    display: flex;
    justify-content: flex-start;
    font-size: 16px;
    color: #646d80;
    .audio-name,
    .audio-attribute,
    .audio-type,
    .upload-time,
    .audio-operation {
      padding: 12.5px 0 12.5px 40px;
    }
  }
  .audio-ul {
    height: calc(100% - 64px);
    overflow-y: auto;
    .audio-li {
      position: relative;
      display: flex;
      justify-content: flex-start;
      margin: 2px 0;
      padding: 6px 0;
      background: #292e37;
      box-sizing: border-box;
      height: 90px;
      &:hover {
        background-color: #3e4252;
      }
    }
  }
  .audio-name,
  .audio-attribute,
  .audio-type,
  .upload-time,
  .audio-operation {
    display: flex;
    align-items: center;
    padding-left: 40px;
    box-sizing: border-box;
  }
  .audio-type {
    flex: 0 0 12.5%;
    width: 10.5%;
    padding-right: 50px;
  }
  .audio-attribute {
    flex: 0 0 20.5%;
    width: 20.5%;
    padding-right: 50px;
  }
  .upload-time {
    flex: 0 0 20.5%;
    width: 20.5%;
    padding-right: 50px;
    white-space: nowrap;
  }
  .audio-operation {
    flex: 0 0 12.5%;
    width: 12.5%;
  }
  .audio-name {
    flex: 0 0 25.5%;
    width: 25.5%;
    padding-right: 50px;
    .name {
      display: inline-block;
      @include ellipticals(2);
    }
  }
  .audio-checked {
    display: flex;
    align-items: center;
    /*text-indent: 10px;*/
    margin-left: 20px;
    flex: 0 0 30px;
    width: 30px;
  }
  .audio-attribute {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: flex-start;
    .audio-path {
      width: 100%;
      display: inline-block;
      line-height: 18px;
      word-break: break-all;
      @include ellipticals(3);
    }
  }
  .audio-bottom {
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: absolute;
    padding: 0 20px;
    box-sizing: border-box;
    bottom: 0;
    left: 0;
    height: 70px;
    width: 100%;
    background-color: #21252d;
  }
}
.text-btn {
  color: #627fc2;
}
::v-deep .el-checkbox__label {
  padding-left: 0 !important;
}
::v-deep .el-input-group__append {
  background-color: #535d72;
  padding: 0 18px;
}
::v-deep .el-checkbox__input {
  transform: scale(1.3);
}
::v-deep .el-button--text:focus {
  color: #627fc2;
}
::v-deep .el-button--text:hover {
  color: #ff5b0c;
}
::v-deep .el-button {
  font-size: 16px;
}
.el-icon-warning {
  color: #b2bfde;
}
</style>
