<template>
  <div class="live-instance-list-page wh100">
    <list-header-area class="header-area"></list-header-area>
    <main class="main-area">
      <instance-list></instance-list>
    </main>
  </div>
</template>

<script>
import listHeaderArea from './listHeaderArea'
import instanceList from './instanceList'

export default {
  data () {
    return {
    }
  },

  components: {listHeaderArea, instanceList},

  methods: {}
}
</script>

<style scoped lang="scss">
  .live-instance-list-page {
    background-color: $color-content-bg;
    overflow: hidden;
    .header-area {
      height: 60px;
      border-bottom: 1px solid $color-border2;
      box-sizing: border-box;
    }
    .main-area {
      width: 100%;
      box-sizing: border-box;
      height: calc(100% - 60px);
      display: flex;
      justify-content: space-between;
    }
  }
</style>
