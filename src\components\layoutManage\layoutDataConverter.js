/**
 * 布局数据转换工具
 * 用于前端布局对象和后端Layout对象之间的转换
 */

/**
 * 前端组件类型到后端类型的映射
 */
const COMPONENT_TYPE_MAP = {
  'main': 'main', // 主画面
  'stream': 'live', // 直播流
  'video': 'record', // 点播流（在线）- 需要根据source字段动态判断
  'bg': 'pic', // 背景图片
  'logo': 'pic', // 图片
  'subtitle': 'subtitle', // 字幕
  'web': 'html', // 网页
  'program': 'schedule' // 节目单
}

/**
 * 后端类型到前端组件类型的映射
 */
const BACKEND_TYPE_MAP = {
  'main': 'main',
  'live': 'stream',
  'record': 'video', // 点播流（在线）
  'local': 'video', // 本地文件（也映射为video，通过source区分）
  'pic': 'logo', // 默认映射为图片，具体类型需要根据上下文判断
  'html': 'web',
  'schedule': 'program',
  'subtitle': 'subtitle',
  'audio': 'audio'
}

/**
 * 将前端布局数据转换为后端Layout对象
 * @param {Object} frontendLayoutData - 前端布局数据
 * @returns {Object} 后端Layout对象
 */
export function convertToBackendLayout(frontendLayoutData) {
  const { name, ratio, components, canvasSize, previewImage, usedType, selected } = frontendLayoutData

  // 先对组件进行排序：背景组件排第一，其他按zIndex升序排列
  const sortedComponents = [...components].sort((a, b) => {
    // 背景组件永远排第一
    if (a.type === 'bg') return -1
    if (b.type === 'bg') return 1

    // 其他组件按zIndex升序排列，zIndex越小排越前
    const aZIndex = a.zIndex || 0
    const bZIndex = b.zIndex || 0
    return aZIndex - bZIndex
  })

  // 转换排序后的组件列表为后端Objects
  const objects = sortedComponents.map((component, index) => {
    return convertToBackendObject(component, index, canvasSize)
  })

  // 构建后端Layout对象
  const backendLayout = {
    id: frontendLayoutData.id || '', // 新建时为空，更新时有值
    name: name,
    objects: objects,
    usedType: usedType || 0, // 默认为全局布局
    selected: selected || false,
    aspectRatio: ratio,
    url: previewImage || '',
    isDefault: false, // 默认不是默认布局
    userData: JSON.stringify(frontendLayoutData) // 保存前端完整数据（字段名改为userData）
  }

  return backendLayout
}

/**
 * 将前端组件转换为后端Object
 * @param {Object} component - 前端组件对象
 * @param {number} index - 组件索引
 * @param {Object} canvasSize - 画布尺寸
 * @returns {Object} 后端Object对象
 */
function convertToBackendObject(component, index, canvasSize) {
  // 根据组件类型和source字段动态判断后端类型
  let backendType = COMPONENT_TYPE_MAP[component.type] || 'pic'

  // 特殊处理：点播源根据source字段判断是record还是local
  if (component.type === 'video') {
    backendType = component.source === 'local' ? 'local' : 'record'
  }

  // 根据组件类型构建不同的Value对象
  let value = {}

  if (backendType === 'main') {
    // 主画面特殊处理，可能不需要value或需要特殊结构
    value = {
      name: component.name || '主画面'
    }
  } else if (backendType === 'schedule') {
    // 节目单类型
    value = {
      name: component.name || `组件${index + 1}`,
      scheduleId: String(component.programId || ''),
      volume: component.volume || 100,
      loop: component.playType === 'loop' ? 1 : 0
    }
  } else if (backendType === 'subtitle') {
    // 字幕类型使用Layer结构
    value = {
      type: 'subtitle',
      url: component.url || '',
      width: component.width || 100,
      height: component.height || 100,
      dx: component.dx || 0,
      dy: component.dy || 0,
      refract: component.opacity || 100,
      text: component.text || '',
      size: component.size || 16,
      color: component.color || '#ffffff',
      boxColor: component.backgroundColor || '#00000000',
      boldItalic: component.fontStyle || '',
      font: component.font || 'Microsoft YaHei',
      position: component.position || 5
    }
  } else if (backendType === 'pic') {
    // 图片类型
    value = {
      name: component.name || `组件${index + 1}`,
      url: component.url || '',
      isBackground: component.type === 'bg'
    }
  } else if (backendType === 'live' || backendType === 'html') {
    // 直播流和网页类型
    value = {
      name: component.name || `组件${index + 1}`,
      url: component.url || '',
      volume: component.volume || 100
    }
  } else if (backendType === 'record' || backendType === 'local') {
    // 点播流类型（在线或本地）
    value = {
      name: component.name || `组件${index + 1}`,
      url: component.url || '',
      volume: component.volume || 100,
      loop: component.playType === 'loop' ? 1 : 0
    }
  } else {
    // 默认Stream结构
    value = {
      name: component.name || `组件${index + 1}`,
      url: component.url || ''
    }
  }

  // 构建Crop对象（裁剪信息）
  const crop = {
    dx: (component.crop && component.crop.x) || 0,
    dy: (component.crop && component.crop.y) || 0,
    width: (component.crop && component.crop.width) || 1,
    height: (component.crop && component.crop.height) || 1
  }

  // 构建Display对象（显示位置和尺寸）
  const display = {
    dx: component.dx || 0,
    dy: component.dy || 0,
    width: component.width || 100,
    height: component.height || 100
  }

  // 构建Effect对象（效果）
  const effect = {
    mosaic: convertMosaicEffect(component),
    colorTone: convertColorTone(component),
    lut: convertLut(component)
  }

  return {
    id: component.id || (index + 1),
    name: component.name || `组件${index + 1}`,
    isMaster: component.type === 'main', // 是否为主画面
    type: backendType,
    value: value,
    crop: crop,
    display: display,
    effect: effect,
    loop: component.loop || 0
  }
}

/**
 * 转换马赛克效果
 */
function convertMosaicEffect(component) {
  const mosaic = {
    type: 0,
    size: 1.0,
    steps: 1.0,
    width: component.width || 100,
    height: component.height || 100,
    color: 'CACACAFF',
    dx: component.dx || 0,
    dy: component.dy || 0
  }

  // 如果有高斯模糊效果
  if (component.gaussianBlur && component.gaussianBlurRadius > 0) {
    mosaic.type = 1 // 高斯模糊
    mosaic.steps = Math.min(Math.max(component.gaussianBlurRadius / 2.5, 1), 20) // 将0-50范围映射到1-20
  }

  return mosaic
}

/**
 * 转换调色效果
 */
function convertColorTone(component) {
  return {
    contrast: 0,
    saturation: 0,
    exposure: 0,
    hue: 0,
    temperature: 0,
    alpha: component.opacity ? (100 - component.opacity) / 100 : 0 // 透明度转换
  }
}

/**
 * 转换LUT效果
 */
function convertLut(component) {
  return {
    path: '',
    factor: 0
  }
}

/**
 * 将后端Layout对象转换为前端布局数据
 * @param {Object} backendLayout - 后端Layout对象
 * @returns {Object} 前端布局数据
 */
export function convertFromBackendLayout(backendLayout) {
  // 如果有userData，优先使用前端保存的完整数据
  if (backendLayout.userData) {
    try {
      const frontendData = JSON.parse(backendLayout.userData)
      // 更新一些可能变化的字段
      return {
        ...frontendData,
        id: backendLayout.id,
        name: backendLayout.name,
        ratio: backendLayout.aspectRatio,
        previewImage: backendLayout.url,
        usedType: backendLayout.usedType,
        selected: backendLayout.selected
      }
    } catch (error) {
      console.warn('解析userData失败，使用对象转换:', error)
    }
  }

  // 如果没有userData或解析失败，从objects转换
  const components = backendLayout.objects.map(obj => convertFromBackendObject(obj))

  // 使用默认画布尺寸
  const canvasSize = getDefaultCanvasSize(backendLayout.aspectRatio)

  return {
    id: backendLayout.id,
    name: backendLayout.name,
    ratio: backendLayout.aspectRatio,
    components: components,
    canvasSize: canvasSize,
    previewImage: backendLayout.url
  }
}

/**
 * 将后端Object转换为前端组件
 * @param {Object} backendObject - 后端Object对象
 * @returns {Object} 前端组件对象
 */
function convertFromBackendObject(backendObject) {
  // 根据类型直接映射
  let frontendType = BACKEND_TYPE_MAP[backendObject.type] || 'logo'

  // 特殊类型判断逻辑
  if (backendObject.type === 'pic') {
    // 根据value中的isBackground字段判断是否为背景
    if (backendObject.value && backendObject.value.isBackground === 'true') {
      frontendType = 'bg'
    } else {
      frontendType = 'logo'
    }
  }

  const component = {
    id: backendObject.id,
    type: frontendType,
    name: backendObject.name,
    dx: backendObject.display.dx,
    dy: backendObject.display.dy,
    width: backendObject.display.width,
    height: backendObject.display.height,
    zIndex: backendObject.idx || 0,
    loop: backendObject.loop
  }

  // 根据类型解析value字段
  if (backendObject.value) {
    const value = backendObject.value

    if (backendObject.type === 'schedule') {
      // 节目单类型
      component.programId = parseInt(value.scheduleId) || 0
      component.volume = value.volume || 100
      component.playType = value.loop === 1 ? 'loop' : 'once'
    } else if (backendObject.type === 'subtitle') {
      // 字幕类型
      component.url = value.url || ''
      component.text = value.text || ''
      component.size = value.size || 16
      component.color = value.color || '#ffffff'
      component.backgroundColor = value.boxColor || '#00000000'
      component.fontStyle = value.boldItalic || ''
      component.font = value.font || 'Microsoft YaHei'
      component.position = value.position || 5
      component.opacity = value.refract || 100
    } else if (backendObject.type === 'record' || backendObject.type === 'local') {
      // 点播流和本地文件
      component.url = value.url || ''
      component.volume = value.volume || 100
      component.playType = value.loop === 1 ? 'loop' : 'once'
      component.source = backendObject.type === 'local' ? 'local' : 'online' // 根据后端类型设置source
    } else if (backendObject.type === 'live' || backendObject.type === 'html') {
      // 直播流和网页
      component.url = value.url || ''
      component.volume = value.volume || 100
    } else {
      // 默认处理（图片等）
      component.url = value.url || ''
    }
  }

  // 转换裁剪信息
  if (backendObject.crop) {
    component.crop = {
      x: backendObject.crop.dx,
      y: backendObject.crop.dy,
      width: backendObject.crop.width,
      height: backendObject.crop.height
    }
  }

  // 转换效果信息
  if (backendObject.effect) {
    // 高斯模糊
    if (backendObject.effect.mosaic && backendObject.effect.mosaic.type === 1) {
      component.gaussianBlur = 1
      component.gaussianBlurRadius = Math.min(Math.max(backendObject.effect.mosaic.steps * 2.5, 1), 50)
    }

    // 透明度
    if (backendObject.effect.colorTone && backendObject.effect.colorTone.alpha > 0) {
      component.opacity = Math.round((1 - backendObject.effect.colorTone.alpha) * 100)
    }
  }

  return component
}

/**
 * 根据比例获取默认画布尺寸
 */
function getDefaultCanvasSize(aspectRatio) {
  const longSide = 1920

  switch (aspectRatio) {
    case '16:9':
      return { width: longSide, height: Math.floor(longSide * 9 / 16) }
    case '9:16':
      return { width: Math.floor(longSide * 9 / 16), height: longSide }
    case '4:3':
      return { width: longSide, height: Math.floor(longSide * 3 / 4) }
    case '4:9':
      return { width: Math.floor(longSide * 4 / 9), height: longSide }
    default:
      return { width: longSide, height: Math.floor(longSide * 9 / 16) }
  }
}
