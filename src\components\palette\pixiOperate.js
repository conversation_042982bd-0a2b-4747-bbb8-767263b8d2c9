import * as PIXI from 'pixi.js'
import PixiClass from './pixiClass'
import jsUtil from '@/common/js/jsUtil'

let AllMap = {}
// console.log(angular)
let pixiOperate = {
  pixiApp: null,
  pixiStage: null,
  trackContainers: {},
  mosaicProportion: 1,
  init ({canvasBox}) {
    if (!this.pixiApp) {
      this.pixiApp = new PIXI.Application({
        resizeTo: canvasBox,
        transparent: 'notMultiplied'
      })
      this.pixiStage = this.pixiApp.stage
      this.pixiApp.stage.sortableChildren = true

      let spriteContainer = new PIXI.Container()
      spriteContainer.zIndex = 1
      spriteContainer.sortableChildren = true
      spriteContainer.filterArea = this.pixiApp.screen
      spriteContainer.filters = []
      this.trackContainers[0] = {
        spriteContainer // 精灵容器
      }
      this.pixiApp.stage.addChild(spriteContainer)
    }
  },
  resize () {
    this.pixiApp.resize()
    for (let k in AllMap) {
      let sprite = AllMap[k]
      sprite.resize()
    }
  },
  createPixiItem ({display, media, tone}) {
    let option = {
      display: jsUtil.deepCopy(display),
      tone: jsUtil.deepCopy(tone),
      zIndex: 1,
      source: media,
      spriteContainer: this.trackContainers[0].spriteContainer,
      pixiApp: this.pixiApp
    }
    let pixiItem = new PixiClass(option)
    AllMap[0] = pixiItem
    return pixiItem
  },
  updateColor ({pixiItem, tone}) {
    let param = jsUtil.deepCopy(tone)
    pixiItem.updateColor(param)
  },
  clearAllSprite () {
    this.pixiStage = null
    for (let k in this.trackContainers) {
      this.trackContainers[k].spriteContainer.destroy()
    }
    this.trackContainers = {}
    this.pixiApp && this.pixiApp.destroy()
    for (let k in AllMap) {
      AllMap[k].remove()
    }
    AllMap = {}
    this.pixiApp = null
  }
}
export default pixiOperate
