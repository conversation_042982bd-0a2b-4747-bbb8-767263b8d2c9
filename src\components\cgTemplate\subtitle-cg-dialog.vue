<template>
  <el-dialog class="subtitle-cg-dialog" title="字幕编辑" :visible.sync="showDialog" top="50px" :width="dialogWidth" :close-on-click-modal="false"
             @click="cancelSelectText" @close="close" append-to-body>
    <div class="st-cg-wrapper">
      <div class="st-cg-left">
        <div class="st-option-area" :style="getSubtitleWH()">
          <img src="" ref="bgImg" class="wh100" alt="">
          <div class="st-cg-box" :style="getCgBoxStyle()">
            <div class="st-cg-container" :style="getCgContainerStyle" @mousedown="cgContainerMouseDown($event)" v-if="currentCgInfo.type === 'subtitle'">
              <i class="container-anchor cursorNormal" :class="item.name" :style="getContainerAnchorStyle" :key="item.name" v-for="item in containerAnchorList"></i>
              <div class="st-cg-list" v-if="currentCgInfo.obj && currentCgInfo.obj.layers && currentCgInfo.obj.layers.length">
                <div class="st-cg-text" :style="getStCgTextStyle(item, index)" :key="item.id" v-for="(item, index) in currentCgInfo.obj.layers"
                     @mousedown.stop="cgTextMouseDown($event, index)">
                  <i class="container-anchor" :class="anchor.name" :style="getContainerAnchorStyle" :key="anchor.name" v-for="anchor in containerAnchorList"
                     @mousedown.stop="textAnchorMouseDown(index, anchor.name, $event)"></i>
                  <div class="st-text-wrapper wh100" :style="getStTextWrapperBg(item)" v-if="item.type === 'subtitle'">
                    <div class="st-text-val wh100" :style="getStTextValStyle(item)" @dblclick="textEdit($event, index)" @blur="cancelSelectText" v-html="item.text"></div>
                  </div>
                  <div class="st-img-wrapper wh100" :style="{backgroundImage: 'url(' + $_getPicture(item.url) + ')'}" v-if="item.type === 'logo'"></div>
                </div>
                <st-cg-ctl :style="getStCgCtlStyle" @changeCgItemIndex="changeCgItemIndex" @close="closeCgItem" v-if="currentCgItemIndex > -1"></st-cg-ctl>
              </div>
            </div>
            <div class="st-cg-container clock" ref="cgClockContainier" :style="getCgClockStyle" @mousedown="cgContainerMouseDown($event)"
                 v-if="currentCgInfo.type === 'clock'">
              <span v-show="currentCgInfo.obj.text === '15:04:05'">00:00:00</span>
              <span v-show="currentCgInfo.obj.text === '2006-01-02'">2001-01-01</span>
              <span v-show="currentCgInfo.obj.text === '2006-01-02 15:04:05'">2001-01-01 00:00:00</span>
            </div>
            <div class="st-cg-container timekeeping" ref="cgTimekeepingContainier" :style="getCgTimekeepingStyle" @mousedown="cgContainerMouseDown($event)"
                 v-if="currentCgInfo.type === 'timekeeping'">
              <template v-if="currentCgInfo.obj.box">
                <span class="hasBorder" :style="{backgroundColor: currentCgInfo.obj.boxColor}" ref="borderRef1">{{getFirstBoxData()}}</span>
                <span class="hasBorder" ref="borderRef_0" style="position: absolute; opacity: 0">00</span>
                <span class="hasBorder" ref="borderRef_1" style="position: absolute; opacity: 0">000</span>
                <span class="hasBorder" ref="borderRef_2" style="position: absolute; opacity: 0">0000</span>
                <span class="hasBorder" ref="borderRef_3" style="position: absolute; opacity: 0">00000</span>
                <span v-show="[2, 3].includes(timekeepingFormatValue)" :style="{fontSize: currentCgInfo.obj.fontSize / 5 * 3 + 'px', padding: '0 5px'}">天</span>
                <span v-show="timekeepingFormatValue === 1" style="display: inline-block;padding: 0 10px">:</span>
                <span class="hasBorder" :style="{backgroundColor: currentCgInfo.obj.boxColor}" v-show="timekeepingFormatValue !== 3" ref="borderRef2">00</span>
                <span v-show="timekeepingFormatValue === 2" :style="{fontSize: currentCgInfo.obj.fontSize / 5 * 3 + 'px', padding: '0 5px'}">时</span>
                <span v-show="timekeepingFormatValue === 1" style="display: inline-block;padding: 0 10px">:</span>
                <span class="hasBorder" :style="{backgroundColor: currentCgInfo.obj.boxColor}" v-show="timekeepingFormatValue !== 3" ref="borderRef3">00</span>
                <span v-show="timekeepingFormatValue === 2" :style="{fontSize: currentCgInfo.obj.fontSize / 5 * 3 + 'px', padding: '0 5px'}">分</span>
              </template>
              <template v-else>
                <span ref="noBorderRef1" style="display: inline-block">{{getFirstBoxData()}}</span>
                <span ref="noBorderRef_0" style="display: inline-block;position: absolute; opacity: 0">00</span>
                <span ref="noBorderRef_1" style="display: inline-block;position: absolute; opacity: 0">000</span>
                <span ref="noBorderRef_2" style="display: inline-block;position: absolute; opacity: 0">0000</span>
                <span ref="noBorderRef_3" style="display: inline-block;position: absolute; opacity: 0">00000</span>
                <span v-show="[2, 3].includes(timekeepingFormatValue)" :style="{fontSize: currentCgInfo.obj.fontSize / 5 * 3 + 'px', padding: '0 5px'}">天</span>
                <span v-show="timekeepingFormatValue === 1" style="display: inline-block;padding: 0 10px">:</span>
                <span v-show="timekeepingFormatValue !== 3" ref="noBorderRef2">00</span>
                <span v-show="timekeepingFormatValue === 2" :style="{fontSize: currentCgInfo.obj.fontSize / 5 * 3 + 'px', padding: '0 5px'}">时</span>
                <span v-show="timekeepingFormatValue === 1" style="display: inline-block;padding: 0 10px">:</span>
                <span v-show="timekeepingFormatValue !== 3" ref="noBorderRef3">00</span>
                <span v-show="timekeepingFormatValue === 2" :style="{fontSize: currentCgInfo.obj.fontSize / 5 * 3 + 'px', padding: '0 5px'}">分</span>
              </template>
            </div>
            <div class="st-cg-container weather" ref="cgWeatherContainier" :style="getCgWeatherStyle" @mousedown="cgContainerMouseDown($event)"
                 v-if="currentCgInfo.type === 'weather'">
              <weather :currentCgInfo="currentCgInfo"></weather>
            </div>
            <div class="st-cg-container move" ref="cgMoveContainier" :style="getCgMoveStyle" @mousedown="cgContainerMouseDown($event)"
                 v-if="currentCgInfo.type === 'movingSubtitle'">
              {{currentCgInfo.obj.text}}
            </div>
            <div class="st-cg-container" ref="cgGifContainier" :style="getCgGifStyle" @mousedown="cgContainerMouseDown($event)"
                 v-if="currentCgInfo.type === 'gif'">
              <i class="container-anchor" :class="anchor.name" :style="getContainerAnchorStyle" :key="anchor.name" v-for="anchor in containerAnchorList"
                 @mousedown.stop="textAnchorMouseDown(0, anchor.name, $event)"></i>
              <div class="st-img-wrapper wh100" :style="{backgroundImage: 'url(' + $_getPicture(currentCgInfo.obj.url) + ')', backgroundSize: '100% 100%', backgroundPosition: 'center',backgroundRepeat: 'no-repeat'}"
                   v-if="currentCgInfo.obj.url"></div>
            </div>
          </div>
        </div>
      </div>
      <div class="st-cg-right">
        <el-form label-position="left" label-width="96px">
          <el-form-item label="字幕名称：">
            <el-input size="small" :maxlength="24" v-model.trim="currentCgInfo.name" placeholder="请输入字幕名称"></el-input>
          </el-form-item>
          <el-form-item label="字幕类型：">
            <el-select v-model="currentCgInfo.type" placeholder="请选择" size="small" @change="selectedSubtitleType">
              <el-option v-for="item in subtitleTypeOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="" v-if="currentCgInfo.type === 'subtitle' || currentCgInfo.type === 'gif'">
            <el-button type="info" size="small" @click="addCgText" v-if="currentCgInfo.type === 'subtitle'">
              <i class="icon icon-fount-text"></i>
              <span>添加文字</span>
            </el-button>
            <v-upload style="display: inline-block;margin-left: 10px;" :fileSize="5" :uploadFileType="'picture'" :isGetResolution="true" @uploadUrl="uploadImgUrl"
                      v-if="currentCgInfo.type === 'subtitle'">
              <el-button type="info" size="small">
                <i class="icon icon-fount-img"></i>
                <span>添加图片</span>
              </el-button>
            </v-upload>
            <v-upload style="display: inline-block;margin-left: 10px;" :fileSize="5" :uploadFileType="'gif'" :isGetResolution="true" @uploadUrl="uploadImgUrl"
                      v-if="currentCgInfo.type === 'gif'">
              <el-button type="info" size="small">
                <i class="icon icon-fount-img"></i>
                <span>上传GIF</span>
              </el-button>
            </v-upload>
          </el-form-item>
          <template v-if="showSubtitleOption">
            <el-form-item label="字体类型：">
              <el-select v-model="currentCgItem.font" placeholder="请选择" size="small">
                <el-option v-for="item in fontFamilyOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="字号大小：">
              <el-input-number v-model.trim="currentCgItem.size" :precision="0" :step="1" :min="12" size="small"></el-input-number>
            </el-form-item>
            <el-form-item label="对齐方式：">
              <div class="flex-v-c" style="height: 40px;">
                <div class="align-type flex-c-c" :class="{active: currentCgItem.textAlign === item.value}" :key="item.value" v-for="item in textAlignStyle"
                     @click.stop="currentCgItem.textAlign = item.value">
                  <img :src="item.bg" alt="">
                </div>
                <div class="align-type flex-c-c" :class="{active: currentCgItem.verticalAlign === item.value}" :key="item.value"
                     v-for="item in verticalAlignStyle" @click.stop="currentCgItem.verticalAlign = item.value">
                  <img :src="item.bg" alt="">
                </div>
              </div>
            </el-form-item>
            <el-form-item label="字体颜色：">
              <el-color-picker v-model="currentCgItem.color" size="small"></el-color-picker>
            </el-form-item>
            <el-form-item label="背景颜色：">
              <el-color-picker v-model="currentCgItem.backgroundColor" size="small"></el-color-picker>
            </el-form-item>
            <el-form-item label="背景透明：">
              <el-slider v-model="currentCgItem.bgOpacity" size="small"></el-slider>
            </el-form-item>
          </template>
          <template v-if="showClockOption">
            <el-form-item label="时间格式：">
              <el-select v-model="currentCgInfo.obj.text" placeholder="请选择" size="small">
                <el-option v-for="item in clockTypeOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="字号大小：">
              <el-input-number v-model.trim="currentCgInfo.obj.size" :precision="0" :step="1" :min="12" size="small"></el-input-number>
            </el-form-item>
            <el-form-item label="字体颜色：">
              <el-color-picker v-model="currentCgInfo.obj.color" size="small"></el-color-picker>
            </el-form-item>
          </template>
          <template v-if="showMoveOption">
            <el-form-item label="字体类型：">
              <el-select v-model="currentCgInfo.obj.font" placeholder="请选择" size="small">
                <el-option v-for="item in fontFamilyOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="字号大小：">
              <el-input-number v-model.trim="currentCgInfo.obj.size" :precision="0" :step="1" :min="12" size="small"></el-input-number>
            </el-form-item>
            <el-form-item label="字体颜色：">
              <el-color-picker v-model="currentCgInfo.obj.color" size="small"></el-color-picker>
            </el-form-item>
            <el-form-item label="字幕内容：">
              <el-input type="textarea" :rows="2" placeholder="请输入字幕内容" v-model.trim="currentCgInfo.obj.text" @keydown.enter.native="carriageReturn($event)"></el-input>
            </el-form-item>
            <el-form-item label="游走类型：">
              <el-radio v-model="currentCgInfo.loop" :label="1">单次</el-radio>
              <el-radio v-model="currentCgInfo.loop" :label="0">循环</el-radio>
            </el-form-item>
            <el-form-item label="游走速度：">
              <div style="display: flex;justify-content: space-between">
                <el-slider :step="1" :max="10" :min="1" v-model="moveSpeed" size="small" class="speed-slider"></el-slider>
                <el-input-number v-model.trim="moveSpeed" :step="1" :min="1" :max="10" size="small" :controls="false" style="width: 50px"></el-input-number>
              </div>
            </el-form-item>
          </template>
          <template v-if="showTimekeepingOption">
            <el-form-item label="计时方式：">
              <el-select v-model="timekeepingTypeValue" placeholder="请选择" size="small">
                <el-option v-for="item in timekeepingTypeOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="显示格式：">
              <el-select v-model="timekeepingFormatValue" placeholder="请选择" size="small">
                <el-option v-for="item in timekeepingFormatOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="终止计时：" v-if="timekeepingTypeValue === 1">
              <el-date-picker v-model="timekeepingEndTime" type="datetime" placeholder="选择日期时间" value-format="timestamp" size="small" :picker-options="pickerOptions"></el-date-picker>
            </el-form-item>
            <el-form-item label="字号大小：">
              <el-input-number v-model.trim="currentCgInfo.obj.fontSize" :precision="0" :step="1" :min="12" size="small" style="width: 100%"></el-input-number>
            </el-form-item>
            <el-form-item label="字体颜色：">
              <el-color-picker v-model="currentCgInfo.obj.fontColor" size="small"></el-color-picker>
            </el-form-item>
            <el-form-item label="样式选择：">
              <div class="style-box">
                <div class="flex-c-c">
                  <el-radio v-model="currentCgInfo.obj.box" :label="false">无框</el-radio>
                  <div class="content-item">
                    <span class="no-border">00</span>
                    <span v-show="[2, 3].includes(timekeepingFormatValue)" style="display: inline-block;padding: 0 2px">天</span>
                    <span v-show="timekeepingFormatValue === 1" style="display: inline-block;padding: 0 5px">:</span>
                    <span class="no-border" v-show="timekeepingFormatValue !== 3">00</span>
                    <span v-show="timekeepingFormatValue === 2" style="display: inline-block;padding: 0 2px">时</span>
                    <span v-show="timekeepingFormatValue === 1" style="display: inline-block;padding: 0 5px">:</span>
                    <span class="no-border" v-show="timekeepingFormatValue !== 3">00</span>
                    <span v-show="timekeepingFormatValue === 2" style="display: inline-block;padding: 0 2px">分</span>
                  </div>
                </div>
                <div class="flex-c-c">
                  <el-radio v-model="currentCgInfo.obj.box" :label="true">带框</el-radio>
                  <div class="content-item">
                    <span class="border-style">00</span>
                    <span v-show="[2, 3].includes(timekeepingFormatValue)" style="display: inline-block;padding: 0 2px">天</span>
                    <span v-show="timekeepingFormatValue === 1" style="display: inline-block;padding: 0 5px">:</span>
                    <span class="border-style" v-show="timekeepingFormatValue !== 3">00</span>
                    <span v-show="timekeepingFormatValue === 2" style="display: inline-block;padding: 0 2px">时</span>
                    <span v-show="timekeepingFormatValue === 1" style="display: inline-block;padding: 0 5px">:</span>
                    <span class="border-style" v-show="timekeepingFormatValue !== 3">00</span>
                    <span v-show="timekeepingFormatValue === 2" style="display: inline-block;padding: 0 2px">分</span>
                  </div>
                </div>
              </div>
            </el-form-item>
            <el-form-item label="边框颜色：" v-if="currentCgInfo.obj.box">
              <el-color-picker v-model="currentCgInfo.obj.boxColor" size="small"></el-color-picker>
            </el-form-item>
          </template>
          <template v-if="showWeatherOption">
            <el-form-item label="所在城市：">
              <el-autocomplete class="account-search" placeholder="请输入城市" v-model.trim="currentCgInfo.obj.cityName"
                               :fetch-suggestions="querySearchAsync" @select="handleSelect" style="width: 100%"/>
            </el-form-item>
            <el-form-item label="数据类型：">
              <el-select v-model="currentCgInfo.obj.dataType" placeholder="请选择" size="small" @change="currentCgInfo.obj.styleType = 1">
                <el-option v-for="item in weatherDataType" :key="item.value" :label="item.label" :value="item.value"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="显示位置：">
              <el-radio-group v-model="currentCgInfo.obj.coordinates" class="weather-postion" @change="updateWeatherPostion">
                <el-radio :label="0">右上角</el-radio>
                <el-radio :label="1">右下角</el-radio>
                <el-radio :label="2">左上角</el-radio>
                <el-radio :label="3">左下角</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="选择样式：">
              <el-select v-model="currentCgInfo.obj.styleType" placeholder="请选择" size="small" @change="updateWeatherPostion(currentCgInfo.obj.coordinates)">
                <el-option v-for="item in weatherStyleType" :key="item.value" :label="item.label" :value="item.value"></el-option>
              </el-select>
              <div class="tips">注：预览中的天气为示例效果，非真实数据</div>
            </el-form-item>
            <el-form-item label="背景透明度：">
              <el-slider v-model="currentCgInfo.obj.transparence" :step="0.1" :min="0" :max="1"></el-slider>
            </el-form-item>
            <el-form-item label="缩放：">
              <el-slider v-model="currentCgInfo.obj.scale" :step="0.1" :min="0.1" :max="3"></el-slider>
            </el-form-item>
          </template>
        </el-form>
        <div class="dialog-footer">
          <el-button type="info" @click="$emit('close')">取 消</el-button>
          <el-button type="primary" @click="confirm">确 定</el-button>
        </div>
      </div>
    </div>
  </el-dialog>
</template>

<script>
  import {mapGetters, mapMutations} from 'vuex'
  import {DefaultSubtitle, DefaultClock, DefeultMove, DefaultGif, DefaultTimekeeping, DefaultWeather} from './subtitleCg/cg-default-data'
  import stCgCtl from './subtitleCg/st-cg-ctl'
  import liveApi from 'api/live'
  import moment from 'moment'
  import weather from 'components/cgTemplate/weather'
  import remoteFile from '@/components/remoteFile'
  import Server from 'common/js/request'
  import store from 'store'

  export default {
    name: 'subtitle-cg-dialog',
    components: {stCgCtl, weather, remoteFile},
    props: {
      isEditCg: {
        type: Boolean
      },
      cgInfo: {
        type: Object,
        default: function () {
          return DefaultSubtitle()
        }
      },
      usedType: {
        type: Number,
        default: 0
      }
    },
    computed: {
      getUploadImageType() {
        if (this.currentCgInfo.type === 'gif') {
          return 'gif'
        }
        return 'picture'
      },
      getStCgCtlStyle () {
        return {
          transform: 'scale(' + 1 / this.cgBoxScale + ')',
          left: this.currentCgItem.dx - 20 / this.cgBoxScale + 'px',
          top: this.currentCgItem.dy + 10 / this.cgBoxScale + 'px'
        }
      },
      getCgMoveStyle () {
        if (this.currentCgInfo.dy < 0) {
          this.currentCgInfo.dy = 0
        }
        return {
          top: this.currentCgInfo.dy + 'px',
          fontFamily: this.currentCgInfo.obj.font,
          fontSize: this.currentCgInfo.obj.size + 'px',
          color: this.currentCgInfo.obj.color
        }
      },
      getCgClockStyle () {
        if (this.currentCgInfo.dx < 0) {
          this.currentCgInfo.dx = 0
        }
        if (this.currentCgInfo.dy < 0) {
          this.currentCgInfo.dy = 0
        }
        return {
          left: this.currentCgInfo.dx + 'px',
          top: this.currentCgInfo.dy + 'px',
          fontFamily: this.currentCgInfo.obj.font,
          fontSize: this.currentCgInfo.obj.size + 'px',
          color: this.currentCgInfo.obj.color
        }
      },
      getCgTimekeepingStyle () {
        if (this.currentCgInfo.dx < 0) {
          this.currentCgInfo.dx = 0
        }
        if (this.currentCgInfo.dy < 0) {
          this.currentCgInfo.dy = 0
        }
        return {
          left: this.currentCgInfo.dx + 'px',
          top: this.currentCgInfo.dy + 'px',
          fontSize: this.currentCgInfo.obj.fontSize + 'px',
          color: this.currentCgInfo.obj.fontColor
        }
      },
      getCgWeatherStyle () {
        if (this.currentCgInfo.dx < 0) {
          this.currentCgInfo.dx = 0
        }
        if (this.currentCgInfo.dy < 0) {
          this.currentCgInfo.dy = 0
        }
        if (this.currentCgInfo.obj.coordinates === 2) {
          return {
            left: this.currentCgInfo.dx + 'px',
            top: this.currentCgInfo.dy + 'px',
            fontSize: this.currentCgInfo.obj.fontSize + 'px',
            color: this.currentCgInfo.obj.fontColor,
            transform: `scale(${this.currentCgInfo.obj.scale})`,
            'transform-origin': 'top left'
          }
        } else if (this.currentCgInfo.obj.coordinates === 0) {
          return {
            right: (this.liveWidth - this.getWeatherRealWH().width) - this.currentCgInfo.dx + 'px',
            top: this.currentCgInfo.dy + 'px',
            fontSize: this.currentCgInfo.obj.fontSize + 'px',
            color: this.currentCgInfo.obj.fontColor,
            transform: `scale(${this.currentCgInfo.obj.scale})`,
            'transform-origin': 'top right'
          }
        } else if (this.currentCgInfo.obj.coordinates === 1) {
          return {
            right: (this.liveWidth - this.getWeatherRealWH().width) - this.currentCgInfo.dx + 'px',
            bottom: (this.liveHeight - this.getWeatherRealWH().height) - this.currentCgInfo.dy + 'px',
            fontSize: this.currentCgInfo.obj.fontSize + 'px',
            color: this.currentCgInfo.obj.fontColor,
            transform: `scale(${this.currentCgInfo.obj.scale})`,
            'transform-origin': 'bottom right'
          }
        } else if (this.currentCgInfo.obj.coordinates === 3) {
          return {
            left: this.currentCgInfo.dx + 'px',
            bottom: (this.liveHeight - this.getWeatherRealWH().height) - this.currentCgInfo.dy + 'px',
            fontSize: this.currentCgInfo.obj.fontSize + 'px',
            color: this.currentCgInfo.obj.fontColor,
            transform: `scale(${this.currentCgInfo.obj.scale})`,
            'transform-origin': 'bottom left'
          }
        }
      },
      getCgGifStyle () {
        return {
          width: this.currentCgInfo.width + 'px',
          height: this.currentCgInfo.height + 'px',
          left: this.currentCgInfo.dx + 'px',
          top: this.currentCgInfo.dy + 'px'
        }
      },
      showSubtitleOption () {
        return this.currentCgInfo.type === 'subtitle' && this.currentCgItemIndex >= 0 && this.currentCgItem && this.currentCgItem.type === 'subtitle'
      },
      showMoveOption () {
        return this.currentCgInfo.type === 'movingSubtitle'
      },
      showClockOption () {
        return this.currentCgInfo.type === 'clock'
      },
      showTimekeepingOption () {
        return this.currentCgInfo.type === 'timekeeping'
      },
      showWeatherOption () {
        return this.currentCgInfo.type === 'weather'
      },
      currentCgItem () {
        if (this.currentCgInfo.obj && this.currentCgInfo.obj.layers && this.currentCgInfo.obj.layers.length) {
          return this.currentCgInfo.obj.layers[this.currentCgItemIndex]
        } else {
          return null
        }
      },
      getContainerAnchorStyle () {
        return {
          transform: `scale(${1 / this.cgBoxScale})`
        }
      },
      dialogWidth () {
        return this.optionAreaWidth + 385 + 'px'
      },
      getCgContainerStyle () {
        return {
          width: this.currentCgInfo.width + 'px',
          height: this.currentCgInfo.height + 'px',
          left: this.currentCgInfo.dx + 'px',
          top: this.currentCgInfo.dy + 'px'
        }
      },
      weatherStyleType () {
        if (this.currentCgInfo) {
          if (this.currentCgInfo.obj.dataType === 'realTime') {
            return [
              {value: 1, label: '样式1'},
              {value: 2, label: '样式2'},
              {value: 3, label: '样式3'},
              {value: 4, label: '样式4'}
            ]
          } else {
            return [
              {value: 1, label: '样式1'},
              {value: 2, label: '样式2'},
              {value: 3, label: '样式3'}
            ]
          }
        } else {
          return []
        }
      },
      ...mapGetters([
        'cgs',
        'liveWidth',
        'liveHeight',
        'liveRatio',
        'timeDifference'
      ])
    },
    watch: {
      'currentCgInfo.obj.text' (val) {
        this.clockCheckDx()
      },
      'currentCgInfo.obj.size' (val) {
        this.clockCheckDx()
      },
      'currentCgInfo.obj.fontSize' (val) {
        this.timekeepingCheckDx()
      },
      'timekeepingFormatValue' (val) {
        this.timekeepingCheckDx()
      },
      timekeepingEndTime (val) {
        this.timekeepingCheckDx()
      }
    },
    data () {
      return {
        cgComfirmDx: -1,
        cgComfirmDy: -1,
        cgComfirmWidth: -1,
        cgComfirmHeight: -1,
        nextCgItemIndex: -1,
        moveSpeed1: 1,
        moveSpeed: 5,
        pickerOptions: {
          // 限制收货时间不让选择今天以前的
          disabledDate(time) {
            return time.getTime() < Date.now() - 8.64e7
          }
        },
        timekeepingEndTime: '',
        timekeepingStartTime: '',
        timekeepingFormatValue: 1,
        timekeepingFormatOptions: [
          {value: 1, label: '时分秒'},
          {value: 2, label: '天时分'},
          {value: 3, label: '天'}
        ],
        timekeepingTypeValue: 1,
        timekeepingTypeOptions: [
          {value: 1, label: '倒计时'},
          {value: 2, label: '正计时'}
        ],
        weatherDataType: [
          {value: 'realTime', label: '即时天气'},
          {value: 'lastThreeDays', label: '未来3天天气'}
        ],
        containerAnchorType: '',
        cgBoxScale: 0,
        currentCgInfo: null,
        showDialog: true,
        optionAreaWidth: 0,
        optionAreaHeight: 0,
        subtitleTypeOptions: [
          {label: '普通cg', value: 'subtitle'},
          {label: '时钟', value: 'clock'},
          {label: '游走字幕', value: 'movingSubtitle'},
          {label: 'GIF动图', value: 'gif'},
          {label: '计时', value: 'timekeeping'},
          {label: '天气', value: 'weather'}
        ],
        fontFamilyOptions: [
          {label: '微软雅黑', value: 'Microsoft YaHei'},
          {label: '黑体', value: 'SimHei'},
          {label: '楷体', value: 'KaiTi'},
          {label: '隶书', value: 'LiSu'},
          {label: '仿宋', value: 'FangSong'},
          {label: '华文细黑', value: 'STXihei'},
          {label: '华文隶书', value: 'STLiti'},
          {label: '华文行楷', value: 'STXingkai'},
          {label: '华文琥珀', value: 'STHupo'},
          {label: '华文彩云', value: 'STCaiyun'},
          {label: '方正舒体', value: 'FZShuTi'},
          {label: '方正姚体', value: 'FZYaoti'},
          {label: '华文新魏', value: 'STXinwei'},
          {label: '幼圆', value: 'YouYuan'}
        ],
        textAlignStyle: [
          {bg: require('./img/left.png'), value: 'left'},
          {bg: require('./img/center.png'), value: 'center'},
          {bg: require('./img/right.png'), value: 'right'}
        ],
        verticalAlignStyle: [
          {bg: require('./img/top.png'), value: 'top'},
          {bg: require('./img/middle.png'), value: 'middle'},
          {bg: require('./img/bottom.png'), value: 'bottom'}
        ],
        clockTypeOptions: [
          {value: '15:04:05', label: '00:00:00'},
          {value: '2006-01-02', label: '2001-01-01'},
          {value: '2006-01-02 15:04:05', label: '2001-01-01 00:00:00'}
        ],
        containerAnchorList: [
          {name: 'nw'},
          {name: 'ne'},
          {name: 'se'},
          {name: 'sw'}
        ],
        cgContainer: {
          startX: 0,
          startY: 0,
          endX: 0,
          endY: 0,
          currentX: 0,
          currentY: 0
        },
        containerAnchor: {
          startX: 0,
          startY: 0,
          endX: 0,
          endY: 0,
          currentX: 0,
          currentY: 0,
          currentW: 0,
          currentH: 0
        },
        currentCgItemIndex: -1,
        cgListMinWidth: 0,
        cgListMinHeight: 0,
        selectNowData: 0,
        speedList: [
          {value: 0.02, label: 1},
          {value: 0.04, label: 2},
          {value: 0.06, label: 3},
          {value: 0.08, label: 4},
          {value: 0.1, label: 5},
          {value: 0.2, label: 6},
          {value: 0.4, label: 7},
          {value: 0.6, label: 8},
          {value: 0.8, label: 9},
          {value: 1, label: 10}
        ]
      }
    },
    methods: {
      getWeatherRealWH () {
        console.log(this.$refs.cgWeatherContainier)
        if (this.$refs.cgWeatherContainier) {
          return {
            width: this.$refs.cgWeatherContainier.offsetWidth * this.currentCgInfo.obj.scale,
            height: this.$refs.cgWeatherContainier.offsetHeight * this.currentCgInfo.obj.scale
          }
        } else {
          return {
            width: this.currentCgInfo.width * this.currentCgInfo.obj.scale,
            height: this.currentCgInfo.height * this.currentCgInfo.obj.scale
          }
        }
      },
      updateWeatherPostion (coordinates) {
        this.$nextTick(() => {
          if (coordinates === 2) {
            this.currentCgInfo.dx = 30
            this.currentCgInfo.dy = 30
          } else if (coordinates === 0) {
            this.currentCgInfo.dx = this.liveWidth - this.getWeatherRealWH().width - 30
            this.currentCgInfo.dy = 30
          } else if (coordinates === 1) {
            this.currentCgInfo.dx = this.liveWidth - this.getWeatherRealWH().width - 30
            this.currentCgInfo.dy = this.liveHeight - this.getWeatherRealWH().height - 30
          } else if (coordinates === 3) {
            this.currentCgInfo.dx = 30
            this.currentCgInfo.dy = this.liveHeight - this.getWeatherRealWH().height - 30
          }
          console.log(this.getWeatherRealWH().width)
        })
      },
      querySearchAsync (queryString, cb) {
        let results = []
        if (queryString) {
          clearTimeout(this.timer)
          this.timer = setTimeout(() => {
            liveApi.searchCity({cityName: queryString}, res => {
              if (res && res.length) {
                results = res.map(item => {
                  return {
                    code: item.code,
                    value: item.fullName,
                    label: item.name
                  }
                })
              }
              cb(results)
            })
          }, 800)
        } else {
          let res = []
          cb(res)
        }
      },
      handleSelect (item) {
        this.currentCgInfo.obj.cityName = item.label
        this.currentCgInfo.obj.cityCode = item.code
      },
      getSpeedValue (label) {
        if (label) {
          let itemObj = this.speedList.find(item => item.label === label)
          return itemObj ? itemObj.value : 0.1
        }
        return 0.1
      },
      getSpeedLabel (value) {
        if (value) {
          let itemObj = this.speedList.find(item => item.value === value)
          return itemObj ? itemObj.label : 5
        }
        return 5
      },
      timekeepingCheckDx () {
        if (this.currentCgInfo.type === 'timekeeping') {
          this.$nextTick(() => {
            if (this.$refs.cgTimekeepingContainier.offsetWidth + this.currentCgInfo.dx > this.liveWidth) {
              this.currentCgInfo.dx = this.getMaxMoveWidth()
            }
            if (this.$refs.cgTimekeepingContainier.offsetHeight + this.currentCgInfo.dy > this.liveHeight) {
              this.currentCgInfo.dy = this.getMaxMoveHeight()
            }
          })
        }
      },
      clockCheckDx () {
        if (this.currentCgInfo.type === 'clock') {
          this.$nextTick(() => {
            if (this.$refs.cgClockContainier.offsetWidth + this.currentCgInfo.dx > this.liveWidth) {
              this.currentCgInfo.dx = this.getMaxMoveWidth()
            }
            if (this.$refs.cgClockContainier.offsetHeight + this.currentCgInfo.dy > this.liveHeight) {
              this.currentCgInfo.dy = this.getMaxMoveHeight()
            }
          })
        } else if (this.currentCgInfo.type === 'movingSubtitle') {
          this.$nextTick(() => {
            if (this.$refs.cgMoveContainier.offsetHeight + this.currentCgInfo.dy > this.liveHeight) {
              this.currentCgInfo.dy = this.getMaxMoveHeight()
            }
          })
        }
      },
      getAndCorrectTime () {
        return new Date().getTime() + this.timeDifference
      },
      getFirstBoxData () {
        if (this.timekeepingTypeValue === 1 && this.timekeepingEndTime) {
          let time1 = this.timekeepingEndTime - this.getAndCorrectTime()
          if (this.timekeepingFormatValue === 1) {
            if (time1 >= 10000 * 60 * 60 * 1000) {
              return '00000'
            } else if (time1 >= 1000 * 60 * 60 * 1000) {
              return '0000'
            } else if (time1 >= 100 * 60 * 60 * 1000) {
              return '000'
            }
          } else {
            if (time1 >= 10000 * 24 * 60 * 60 * 1000) {
              return '00000'
            } else if (time1 >= 1000 * 24 * 60 * 60 * 1000) {
              return '0000'
            } else if (time1 >= 100 * 24 * 60 * 60 * 1000) {
              return '000'
            }
          }
        }
        return '00'
      },
      carriageReturn (e) {
        if (e.preventDefault) e.preventDefault()
      },
      getCgBoxStyle () {
        this.cgBoxScale = this.optionAreaWidth / this.liveWidth
        return {
          width: this.liveWidth + 'px',
          height: this.liveHeight + 'px',
          transform: `scale(${this.cgBoxScale})`
        }
      },
      getSubtitleWH () {
        this.optionAreaHeight = window.innerHeight * 0.7
        this.optionAreaWidth = this.optionAreaHeight * this.liveRatio
        return {
          width: this.optionAreaWidth + 'px',
          height: this.optionAreaHeight + 'px'
        }
      },
      selectedSubtitleType (type) {
        let currentCgId = this.currentCgInfo.id
        if (type === 'subtitle') {
          this.currentCgInfo = this.$_deepCopy(DefaultSubtitle(this.liveWidth, this.liveHeight))
        } else if (type === 'clock') {
          this.currentCgInfo = this.$_deepCopy(DefaultClock())
        } else if (type === 'gif') {
          this.currentCgInfo = this.$_deepCopy(DefaultGif())
        } else if (type === 'timekeeping') {
          this.currentCgInfo = this.$_deepCopy(DefaultTimekeeping())
        } else if (type === 'weather') {
          this.currentCgInfo = this.$_deepCopy(DefaultWeather())
        } else {
          this.currentCgInfo = this.$_deepCopy(DefeultMove())
          this.moveSpeed = this.getSpeedLabel(this.currentCgInfo.obj.speed)
        }
        this.currentCgInfo.usedType = this.usedType
        this.currentCgInfo.id = currentCgId
        this.currentCgInfo.displayWidth = this.liveWidth
        this.currentCgInfo.displayHeight = this.liveHeight
      },
      getImgWH (uploadInfo) {
        if (uploadInfo.width < this.currentCgInfo.width && uploadInfo.height < this.currentCgInfo.height) {
          return {width: uploadInfo.width, height: uploadInfo.height}
        }
        let whRatio = uploadInfo.width / uploadInfo.height
        if (uploadInfo.width > this.currentCgInfo.width && uploadInfo.height < this.currentCgInfo.height) {
          return {width: this.currentCgInfo.width, height: this.currentCgInfo.width / whRatio}
        }
        if (uploadInfo.width < this.currentCgInfo.width && uploadInfo.height > this.currentCgInfo.height) {
          return {width: this.currentCgInfo.height * whRatio, height: this.currentCgInfo.height}
        }
        if (uploadInfo.width > this.currentCgInfo.width && uploadInfo.height > this.currentCgInfo.height) {
          if (uploadInfo.width > uploadInfo.height) return {width: this.currentCgInfo.width, height: this.currentCgInfo.width / whRatio}
          return {width: this.currentCgInfo.height * whRatio, height: this.currentCgInfo.height}
        }
        return {width: uploadInfo.width, height: uploadInfo.height}
      },
      uploadImgUrl (imgUrl, uploadInfo) {
        let imgWHObj = this.getImgWH(uploadInfo)
        if (this.currentCgInfo.type === 'gif') {
          this.currentCgInfo.width = imgWHObj.width
          this.currentCgInfo.height = imgWHObj.height
          this.currentCgInfo.obj.url = imgUrl
        } else {
          this.currentCgInfo.obj.layers.push({
            dx: 0,
            dy: 0,
            height: imgWHObj.height,
            refract: 100,
            type: 'logo',
            url: imgUrl,
            width: imgWHObj.width
          })
        }
      },
      changeCgItemIndex (type) {
        let currentCgItemCopy = this.$_deepCopy(this.currentCgItem)
        if (type === 'top' && this.currentCgItemIndex !== this.currentCgInfo.obj.layers.length - 1) {
          this.currentCgInfo.obj.layers.splice(this.currentCgItemIndex, 1)
          this.currentCgInfo.obj.layers.push(currentCgItemCopy)
        } else if (type === 'bottom' && this.currentCgItemIndex !== 0) {
          this.currentCgInfo.obj.layers.splice(this.currentCgItemIndex, 1)
          this.currentCgInfo.obj.layers.unshift(currentCgItemCopy)
        } else if (type === 'up' && this.currentCgItemIndex !== this.currentCgInfo.obj.layers.length - 1) {
          this.currentCgInfo.obj.layers.splice(this.currentCgItemIndex, 1)
          this.currentCgInfo.obj.layers.splice(this.currentCgItemIndex + 1, 0, currentCgItemCopy)
        } else if (type === 'down' && this.currentCgItemIndex !== 0) {
          this.currentCgInfo.obj.layers.splice(this.currentCgItemIndex, 1)
          this.currentCgInfo.obj.layers.splice(this.currentCgItemIndex - 1, 0, currentCgItemCopy)
        }
      },
      closeCgItem () {
        this.currentCgInfo.obj.layers.splice(this.currentCgItemIndex, 1)
        this.currentCgItemIndex = -1
      },
      cancelSelectText (e) {
        e.target.style.cursor = 'pointer'
        e.target.contentEditable = false
        let text = e.target.innerHTML.trim()
        if (text === '') {
          text = '双击输入文字'
        }
        this.currentCgInfo.obj.layers[this.nextCgItemIndex].text = text
        e.target.innerHTML = text
        this.currentCgItemIndex = -1
      },
      textEdit (e, index) {
        if (e.target.innerHTML === '双击输入文字') {
          e.target.innerHTML = ''
        }
        e.target.contentEditable = true
        e.target.style.cursor = 'text'
        e.target.focus()
        this.nextCgItemIndex = index
      },
      getStTextWrapperBg (item) {
        return {
          backgroundColor: this.$_toRGBA(item.backgroundColor, item.bgOpacity)
        }
      },
      getStTextValStyle (item) {
        return {
          width: item.width + 'px',
          height: item.height + 'px',
          fontFamily: item.font,
          fontSize: item.size + 'px',
          color: item.color,
          textAlign: item.textAlign,
          verticalAlign: item.verticalAlign
        }
      },
      getStCgTextStyle (item, index) {
        return {
          zIndex: index,
          width: item.width + 'px',
          height: item.height + 'px',
          top: item.dy + 'px',
          left: item.dx + 'px'
        }
      },
      addCgText () {
        this.currentCgInfo.obj.layers.push({
          id: this.currentCgInfo.obj.layers.length,
          boldItalic: '',
          backgroundColor: '#ffffff',
          bgOpacity: 0,
          color: '#ffffff',
          dx: 100,
          dy: 100,
          font: 'Microsoft YaHei',
          height: 60,
          textAlign: 'center',
          verticalAlign: 'middle',
          refract: 100,
          size: 27,
          text: '双击输入文字',
          type: 'subtitle',
          width: 200
        })
      },
      cgTextMouseDown (e, index) {
        this.currentCgItemIndex = index
        this.cgContainer.startX = e.clientX
        this.cgContainer.startY = e.clientY
        this.cgContainer.currentX = this.currentCgItem.dx
        this.cgContainer.currentY = this.currentCgItem.dy
        document.onmousemove = this.moveCgText
        document.onmouseup = this.removeMouseEvent
      },
      moveCgText (e) {
        this.cgContainer.endX = e.clientX
        this.cgContainer.endY = e.clientY
        let moveDx = (this.cgContainer.endX - this.cgContainer.startX) / this.cgBoxScale
        let moveDy = (this.cgContainer.endY - this.cgContainer.startY) / this.cgBoxScale
        this.currentCgInfo.obj.layers[this.currentCgItemIndex].dx = this.getCurrentCgItemDx(moveDx)
        this.currentCgInfo.obj.layers[this.currentCgItemIndex].dy = this.getCurrentCgItemDy(moveDy)
      },
      getCurrentCgItemDx (moveDx) {
        return Math.min((this.currentCgInfo.width - this.currentCgItem.width), Math.max(0, this.cgContainer.currentX + moveDx))
      },
      getCurrentCgItemDy (moveDy) {
        return Math.min((this.currentCgInfo.height - this.currentCgItem.height), Math.max(0, this.cgContainer.currentY + moveDy))
      },
      cgContainerMouseDown (e) {
        this.cgContainer.startX = e.clientX
        this.cgContainer.startY = e.clientY
        this.cgContainer.currentX = this.currentCgInfo.dx
        this.cgContainer.currentY = this.currentCgInfo.dy
        document.onmousemove = this.moveCgContainer
        document.onmouseup = this.removeMouseEvent
      },
      moveCgContainer (e) {
        this.cgContainer.endX = e.clientX
        this.cgContainer.endY = e.clientY
        let moveDx = (this.cgContainer.endX - this.cgContainer.startX) / this.cgBoxScale
        let moveDy = (this.cgContainer.endY - this.cgContainer.startY) / this.cgBoxScale
        if (this.currentCgInfo.type !== 'movingSubtitle') {
          this.currentCgInfo.dx = this.getCurrentCgInfoDx(moveDx)
        }
        this.currentCgInfo.dy = this.getCurrentCgInfoDy(moveDy)
      },
      getMaxMoveWidth () {
        if (this.currentCgInfo.type === 'subtitle') return this.liveWidth - this.currentCgInfo.width
        if (this.currentCgInfo.type === 'clock') return this.liveWidth - this.$refs.cgClockContainier.offsetWidth
        if (this.currentCgInfo.type === 'move') return this.liveWidth - this.$refs.cgMoveContainier.offsetWidth
        if (this.currentCgInfo.type === 'gif') return this.liveWidth - this.$refs.cgGifContainier.offsetWidth
        if (this.currentCgInfo.type === 'timekeeping') return this.liveWidth - this.$refs.cgTimekeepingContainier.offsetWidth
        if (this.currentCgInfo.type === 'weather') return this.liveWidth - this.getWeatherRealWH().width
      },
      getCurrentCgInfoDx (moveDx) {
        let maxMoveWidth = this.getMaxMoveWidth()
        return Math.min(maxMoveWidth, Math.max(0, this.cgContainer.currentX + moveDx))
      },
      getMaxMoveHeight () {
        if (this.currentCgInfo.type === 'subtitle') return this.liveHeight - this.currentCgInfo.height
        if (this.currentCgInfo.type === 'clock') return this.liveHeight - this.$refs.cgClockContainier.offsetHeight
        if (this.currentCgInfo.type === 'movingSubtitle') return this.liveHeight - this.$refs.cgMoveContainier.offsetHeight
        if (this.currentCgInfo.type === 'gif') return this.liveHeight - this.$refs.cgGifContainier.offsetHeight
        if (this.currentCgInfo.type === 'timekeeping') return this.liveHeight - this.$refs.cgTimekeepingContainier.offsetHeight
        if (this.currentCgInfo.type === 'weather') return this.liveHeight - this.getWeatherRealWH().height
      },
      getCurrentCgInfoDy (moveDy) {
        let maxMoveHeight = this.getMaxMoveHeight()
        return Math.min(maxMoveHeight, Math.max(0, this.cgContainer.currentY + moveDy))
      },
      removeMouseEvent () {
        document.onmousemove = null
        document.onmouseup = null
      },
      getCgListMinWH () {
        this.cgListMinWidth = 0
        this.cgListMinHeight = 0
        if (this.currentCgInfo.obj.layers.length) {
          this.currentCgInfo.obj.layers.forEach(item => {
            let cgItemSeDx = item.width + item.dx
            let cgItemSeDy = item.height + item.dy
            if (cgItemSeDx > this.cgListMinWidth) {
              this.cgListMinWidth = cgItemSeDx
            }
            if (cgItemSeDy > this.cgListMinHeight) {
              this.cgListMinHeight = cgItemSeDy
            }
          })
        }
      },
      containerAnchorMouseDown (type, e) {
        this.getCgListMinWH()
        this.containerAnchorType = type
        this.containerAnchor.startX = e.clientX
        this.containerAnchor.startY = e.clientY
        this.containerAnchor.currentX = this.currentCgInfo.dx
        this.containerAnchor.currentY = this.currentCgInfo.dy
        this.containerAnchor.currentW = this.currentCgInfo.width
        this.containerAnchor.currentH = this.currentCgInfo.height
        document.onmousemove = this.moveContainerAnchor
        document.onmouseup = this.removeMouseEvent
      },
      moveContainerAnchor (e) {
        this.containerAnchor.endX = e.clientX
        this.containerAnchor.endY = e.clientY
        let moveDx = (this.containerAnchor.endX - this.containerAnchor.startX) / this.cgBoxScale
        let moveDy = (this.containerAnchor.endY - this.containerAnchor.startY) / this.cgBoxScale
        this.currentCgInfo.dx = this.getCurrentCgInfoDxNew(moveDx, this.containerAnchorType)
        this.currentCgInfo.dy = this.getCurrentCgInfoDyNew(moveDy, this.containerAnchorType)
        this.currentCgInfo.width = this.getCurrentCgInfoWidth(moveDx, this.containerAnchorType)
        this.currentCgInfo.height = this.getCurrentCgInfoHeight(moveDy, this.containerAnchorType)
      },
      getMaxMoveDx () {
        return this.containerAnchor.currentX + this.containerAnchor.currentW - this.cgListMinWidth
      },
      getMaxMoveDy () {
        return this.containerAnchor.currentY + this.containerAnchor.currentH - this.cgListMinHeight
      },
      getCurrentCgInfoDxNew (moveDx, containerAnchorType) {
        if (containerAnchorType === 'nw' || containerAnchorType === 'sw') {
          return Math.max(0, Math.min(this.getMaxMoveDx(), this.containerAnchor.currentX + moveDx))
        } else {
          return this.containerAnchor.currentX
        }
      },
      getCurrentCgInfoDyNew (moveDy, containerAnchorType) {
        if (containerAnchorType === 'nw' || containerAnchorType === 'ne') {
          return Math.max(0, Math.min(this.getMaxMoveDy(), this.containerAnchor.currentY + moveDy))
        } else {
          return this.containerAnchor.currentY
        }
      },
      getCurrentCgInfoWidth (moveDx, containerAnchorType) {
        if (containerAnchorType === 'nw' || containerAnchorType === 'sw') {
          return Math.min(this.containerAnchor.currentX + this.containerAnchor.currentW, Math.max(this.cgListMinWidth, this.containerAnchor.currentW - moveDx))
        } else {
          return Math.min(this.liveWidth - this.containerAnchor.currentX, Math.max(this.cgListMinWidth, this.containerAnchor.currentW + moveDx))
        }
      },
      getCurrentCgInfoHeight (moveDy, containerAnchorType) {
        if (containerAnchorType === 'nw' || containerAnchorType === 'ne') {
          return Math.min(this.containerAnchor.currentY + this.containerAnchor.currentH, Math.max(this.cgListMinHeight, this.containerAnchor.currentH - moveDy))
        } else {
          return Math.min(this.liveHeight - this.containerAnchor.currentY, Math.max(this.cgListMinHeight, this.containerAnchor.currentH + moveDy))
        }
      },
      textAnchorMouseDown (index, type, e) {
        this.currentCgItemIndex = index
        this.containerAnchorType = type
        this.containerAnchor.startX = e.clientX
        this.containerAnchor.startY = e.clientY
        if (this.currentCgInfo.type === 'gif') {
          this.containerAnchor.currentX = this.currentCgInfo.dx
          this.containerAnchor.currentY = this.currentCgInfo.dy
          this.containerAnchor.currentW = this.currentCgInfo.width
          this.containerAnchor.currentH = this.currentCgInfo.height
        } else {
          this.containerAnchor.currentX = this.currentCgItem.dx
          this.containerAnchor.currentY = this.currentCgItem.dy
          this.containerAnchor.currentW = this.currentCgItem.width
          this.containerAnchor.currentH = this.currentCgItem.height
        }
        document.onmousemove = this.moveTextAnchor
        document.onmouseup = this.removeMouseEvent
      },
      moveTextAnchor (e) {
        this.containerAnchor.endX = e.clientX
        this.containerAnchor.endY = e.clientY
        let moveDx = (this.containerAnchor.endX - this.containerAnchor.startX) / this.cgBoxScale
        let moveDy = (this.containerAnchor.endY - this.containerAnchor.startY) / this.cgBoxScale
        if (this.currentCgInfo.type === 'gif') {
          this.currentCgInfo.dx = this.getCurrentCgTextDxNew(moveDx, this.containerAnchorType)
          this.currentCgInfo.dy = this.getCurrentCgTextDyNew(moveDy, this.containerAnchorType)
          this.currentCgInfo.width = this.getCurrentCgTextWidth(moveDx, this.containerAnchorType)
          this.currentCgInfo.height = this.getCurrentCgTextHeight(moveDy, this.containerAnchorType)
        } else {
          this.currentCgInfo.obj.layers[this.currentCgItemIndex].dx = this.getCurrentCgTextDxNew(moveDx, this.containerAnchorType)
          this.currentCgInfo.obj.layers[this.currentCgItemIndex].dy = this.getCurrentCgTextDyNew(moveDy, this.containerAnchorType)
          this.currentCgInfo.obj.layers[this.currentCgItemIndex].width = this.getCurrentCgTextWidth(moveDx, this.containerAnchorType)
          this.currentCgInfo.obj.layers[this.currentCgItemIndex].height = this.getCurrentCgTextHeight(moveDy, this.containerAnchorType)
        }
      },
      getCurrentCgTextDxNew (moveDx, containerAnchorType) {
        if (containerAnchorType === 'nw' || containerAnchorType === 'sw') {
          return Math.max(0, this.containerAnchor.currentX + moveDx)
        } else {
          return this.containerAnchor.currentX
        }
      },
      getCurrentCgTextDyNew (moveDy, containerAnchorType) {
        if (containerAnchorType === 'nw' || containerAnchorType === 'ne') {
          return Math.max(0, this.containerAnchor.currentY + moveDy)
        } else {
          return this.containerAnchor.currentY
        }
      },
      getCurrentCgTextWidth (moveDx, containerAnchorType) {
        if (this.currentCgInfo.type === 'gif') {
          if (containerAnchorType === 'nw' || containerAnchorType === 'sw') {
            return Math.min(this.containerAnchor.currentX + this.containerAnchor.currentW, this.containerAnchor.currentW - moveDx)
          } else {
            return Math.min(this.liveWidth - this.containerAnchor.currentX, this.containerAnchor.currentW + moveDx)
          }
        } else {
          if (containerAnchorType === 'nw' || containerAnchorType === 'sw') {
            return Math.min(this.containerAnchor.currentX + this.containerAnchor.currentW, this.containerAnchor.currentW - moveDx)
          } else {
            return Math.min(this.currentCgInfo.width - this.containerAnchor.currentX, this.containerAnchor.currentW + moveDx)
          }
        }
      },
      getCurrentCgTextHeight (moveDy, containerAnchorType) {
        if (this.currentCgInfo.type === 'gif') {
          if (containerAnchorType === 'nw' || containerAnchorType === 'ne') {
            return Math.min(this.containerAnchor.currentY + this.containerAnchor.currentH, this.containerAnchor.currentH - moveDy)
          } else {
            return Math.min(this.liveHeight - this.containerAnchor.currentY, this.containerAnchor.currentH + moveDy)
          }
        } else {
          if (containerAnchorType === 'nw' || containerAnchorType === 'ne') {
            return Math.min(this.containerAnchor.currentY + this.containerAnchor.currentH, this.containerAnchor.currentH - moveDy)
          } else {
            return Math.min(this.currentCgInfo.height - this.containerAnchor.currentY, this.containerAnchor.currentH + moveDy)
          }
        }
      },
      getBoxColor (backgroundColor, bgOpacity) {
        return backgroundColor.substr(1, 6) + this.$_parseOpacity(bgOpacity)
      },
      getSubtitleObj (obj) {
        let that = this
        if (obj && obj.layers && obj.layers.length) {
          obj.layers.forEach(v => {
            if (that.cgComfirmDx < 0) {
              that.cgComfirmDx = v.dx
              that.cgComfirmDy = v.dy
            }
            if (that.cgComfirmDx >= 0) {
              that.cgComfirmDx = Math.min(that.cgComfirmDx, v.dx)
              that.cgComfirmDy = Math.min(that.cgComfirmDy, v.dy)
            }
          })
          obj.layers.forEach(v => {
            if (that.cgComfirmDx < 0) {
              that.cgComfirmWidth = v.width
              that.cgComfirmHeight = v.height
            }
            if (that.cgComfirmDx >= 0) {
              that.cgComfirmWidth = Math.max(that.cgComfirmWidth, v.dx + v.width - that.cgComfirmDx)
              that.cgComfirmHeight = Math.max(that.cgComfirmHeight, v.dy + v.height - that.cgComfirmDy)
            }
          })
          obj.layers = obj.layers.map(v => {
            if (v.type === 'subtitle') {
              return {
                boldItalic: v.boldItalic,
                boxColor: this.getBoxColor(v.backgroundColor, v.bgOpacity),
                color: v.color ? v.color.substr(1, 6) : 'ffffff',
                dx: v.dx - that.cgComfirmDx,
                dy: v.dy - that.cgComfirmDy,
                font: v.font,
                height: v.height,
                position: this.$_getPosition(v.textAlign, v.verticalAlign),
                refract: v.refract,
                size: v.size,
                text: v.text,
                type: v.type,
                width: v.width
              }
            } else {
              let result = v
              result.dx = v.dx - that.cgComfirmDx
              result.dy = v.dy - that.cgComfirmDy
              return result
            }
          })
        }
        return obj
      },
      getParamCg (Cg) {
        if (this.currentCgInfo.type === 'clock') {
          Cg.width = Cg.obj.width = this.$refs.cgClockContainier.offsetWidth * 2
          Cg.height = Cg.obj.height = this.$refs.cgClockContainier.offsetHeight
        } else if (this.currentCgInfo.type === 'gif') {
          Cg.width = Cg.obj.width = this.$refs.cgGifContainier.offsetWidth
          Cg.height = Cg.obj.height = this.$refs.cgGifContainier.offsetHeight
        } else if (this.currentCgInfo.type === 'movingSubtitle') {
          Cg.height = Cg.obj.height = this.$refs.cgMoveContainier.offsetHeight
          Cg.obj.speed = this.getSpeedValue(this.moveSpeed)
        } else {
          Cg.height = Cg.obj.height = this.$refs.cgMoveContainier.offsetHeight
        }
        Cg.obj.color = Cg.obj.color ? Cg.obj.color.substr(1, 6) : 'ffffff'
        return Cg
      },
      getDrawBox (Cg) {
        let z = this.getFirstBoxData().length
        if (this.currentCgInfo.obj.box) {
          let increment = this.$refs[`borderRef_${z - 2}`].offsetWidth - this.$refs.borderRef_0.offsetWidth
          if (this.timekeepingFormatValue !== 3) {
            for (let i = 1; i < 4; i++) {
              Cg.obj.drawBox[i - 1] = {
                dx: this.$refs[`borderRef${i}`].offsetLeft,
                dy: 0,
                width: [this.$refs[`borderRef${i}`].offsetWidth],
                height: Cg.height
              }
              if (this.timekeepingTypeValue === 1 && i !== 1) {
                Cg.obj.drawBox[i - 1].dx = this.$refs[`borderRef${i}`].offsetLeft - increment
              }
            }
          } else {
            Cg.obj.drawBox[0] = {
              dx: this.$refs.borderRef1.offsetLeft,
              dy: 0,
              width: [this.$refs.borderRef1.offsetWidth],
              height: Cg.height
            }
          }
          Cg.obj.drawBox[0].width = [
            this.$refs.borderRef_0.offsetWidth,
            this.$refs.borderRef_1.offsetWidth,
            this.$refs.borderRef_2.offsetWidth,
            this.$refs.borderRef_3.offsetWidth
          ]
        } else {
          let increment = this.$refs[`noBorderRef_${z - 2}`].offsetWidth - this.$refs.noBorderRef_0.offsetWidth
          if (this.timekeepingFormatValue !== 3) {
            for (let i = 1; i < 4; i++) {
              Cg.obj.drawBox[i - 1] = {
                dx: this.$refs[`noBorderRef${i}`].offsetLeft,
                dy: 0,
                width: [this.$refs[`noBorderRef${i}`].offsetWidth],
                height: Cg.height
              }
              if (this.timekeepingTypeValue === 1 && i !== 1) {
                Cg.obj.drawBox[i - 1].dx = this.$refs[`noBorderRef${i}`].offsetLeft - increment
              }
            }
          } else {
            Cg.obj.drawBox[0] = {
              dx: this.$refs.noBorderRef1.offsetLeft,
              dy: 0,
              width: [this.$refs.noBorderRef1.offsetWidth],
              height: Cg.height
            }
          }
          Cg.obj.drawBox[0].width = [
            this.$refs.noBorderRef_0.offsetWidth,
            this.$refs.noBorderRef_1.offsetWidth,
            this.$refs.noBorderRef_2.offsetWidth,
            this.$refs.noBorderRef_3.offsetWidth
          ]
        }
      },
      getTimekeepingCg (Cg) {
        Cg.width = Cg.obj.width = this.$refs.cgTimekeepingContainier.offsetWidth * 2
        Cg.height = Cg.obj.height = this.$refs.cgTimekeepingContainier.offsetHeight
        if (this.timekeepingTypeValue === 1) {
          Cg.type = 'timerDown'
          Cg.obj.expiration = moment(this.timekeepingEndTime).format('YYYY-MM-DD HH:mm:ss')
        } else {
          Cg.type = 'timerUp'
          Cg.obj.startTime = this.timekeepingStartTime ? moment(this.timekeepingStartTime).format('YYYY-MM-DD HH:mm:ss') : moment(this.getAndCorrectTime()).format('YYYY-MM-DD HH:mm:ss')
        }
        if (this.timekeepingFormatValue === 1) {
          Cg.obj.text = 'hour:minute:second'
        } else if (this.timekeepingFormatValue === 2) {
          Cg.obj.text = 'day hour:minute'
        } else if (this.timekeepingFormatValue === 3) {
          Cg.obj.text = 'day'
        }
        this.getDrawBox(Cg)
        Cg.obj.fontColor = Cg.obj.fontColor ? Cg.obj.fontColor.substr(1, 8) : 'ffffffff'
        Cg.obj.boxColor = Cg.obj.boxColor ? Cg.obj.boxColor.substr(1, 8) : 'ffffffff'
        return Cg
      },
      getWeatherCg (Cg) {
        Cg.width = this.$refs.cgWeatherContainier.offsetWidth
        Cg.height = this.$refs.cgWeatherContainier.offsetHeight
        // Cg.obj.scale = 1 / this.cgBoxScale
        // if (Cg.obj.coordinates === 0) {
        //   Cg.dx = Cg.width * Cg.obj.scale
        // } else if (Cg.obj.coordinates === 1) {
        //   Cg.dx = Cg.width * Cg.obj.scale
        //   Cg.dy = Cg.height * Cg.obj.scale
        // } else if (Cg.obj.coordinates === 3) {
        //   Cg.dy = Cg.height * Cg.obj.scale
        // }
        return Cg
      },
      getCgInfoLayers (cgInfo) {
        let layers = cgInfo.obj.layers
        return layers.map(v => {
          if (v.type === 'subtitle') {
            return {
              boldItalic: v.boldItalic,
              backgroundColor: '#' + v.boxColor.substring(0, 6),
              bgOpacity: Math.round(parseInt(v.boxColor.substring(6, 8), 16) / 2.55),
              color: '#' + v.color,
              dx: v.dx + this.cgInfo.dx,
              dy: v.dy + this.cgInfo.dy,
              font: v.font,
              height: v.height,
              textAlign: this.$_getTextAlign(v.position),
              verticalAlign: this.$_getVerticalAlign(v.position),
              refract: v.refract,
              size: v.size,
              text: v.text,
              type: v.type,
              width: v.width
            }
          } else {
            let result = v
            result.dx = v.dx + this.cgInfo.dx
            result.dy = v.dy + this.cgInfo.dy
            return result
          }
        })
      },
      getClockMoveCgInfo (obj) {
        obj.color = '#' + obj.color
        this.moveSpeed = this.getSpeedLabel(obj.speed)
      },
      getTimeUpCgInfo (cgInfo) {
        this.timekeepingStartTime = cgInfo.obj.startTime ? moment(cgInfo.obj.startTime).valueOf() : ''
        this.timekeepingTypeValue = 2
        cgInfo.obj.boxColor = '#' + cgInfo.obj.boxColor
        cgInfo.obj.fontColor = '#' + cgInfo.obj.fontColor
        cgInfo.type = 'timekeeping'
        if (cgInfo.obj.text === 'hour:minute:second') {
          this.timekeepingFormatValue = 1
        } else if (cgInfo.obj.text === 'day hour:minute') {
          this.timekeepingFormatValue = 2
        } else if (cgInfo.obj.text === 'day') {
          this.timekeepingFormatValue = 3
        }
      },
      getTimeDownCgInfo (cgInfo) {
        this.timekeepingEndTime = moment(cgInfo.obj.expiration).valueOf()
        this.timekeepingTypeValue = 1
        cgInfo.obj.boxColor = '#' + cgInfo.obj.boxColor
        cgInfo.obj.fontColor = '#' + cgInfo.obj.fontColor
        cgInfo.type = 'timekeeping'
        if (cgInfo.obj.text === 'hour:minute:second') {
          this.timekeepingFormatValue = 1
        } else if (cgInfo.obj.text === 'day hour:minute') {
          this.timekeepingFormatValue = 2
        } else if (cgInfo.obj.text === 'day') {
          this.timekeepingFormatValue = 3
        }
      },
      getCgInfo (cgInfo) {
        if (cgInfo.type === 'subtitle') {
          cgInfo.obj.layers = this.getCgInfoLayers(cgInfo)
          this.initOutBox(cgInfo)
        } else if (cgInfo.type === 'timerUp') {
          this.getTimeUpCgInfo(cgInfo)
        } else if (cgInfo.type === 'timerDown') {
          this.getTimeDownCgInfo(cgInfo)
        } else {
          this.getClockMoveCgInfo(cgInfo.obj)
        }
        return cgInfo
      },
      initOutBox (cg) {
        cg.dx = 0
        cg.dy = 0
        cg.width = this.liveWidth
        cg.height = this.liveHeight
      },
      confirm () {
        if (!this.currentCgInfo.name) return this.$message.warning('请输入CG名称')
        let Cg = this.$_deepCopy(this.currentCgInfo)
        if (Cg.type === 'subtitle') {
          Cg.obj = this.getSubtitleObj(Cg.obj)
          Cg.dx += this.cgComfirmDx
          Cg.dy += this.cgComfirmDy
          Cg.width = this.cgComfirmWidth
          Cg.height = this.cgComfirmHeight
        } else if (Cg.type === 'timekeeping') {
          if (this.timekeepingTypeValue === 1 && !this.timekeepingEndTime) {
            return this.$message.warning('请输入终止时间')
          } else if (this.timekeepingTypeValue === 1 && this.timekeepingEndTime - new Date().getTime() <= 0) {
            return this.$message.warning('输入时间需大于当前时间')
          }
          Cg = this.getTimekeepingCg(Cg)
        } else if (Cg.type === 'weather') {
          Cg = this.getWeatherCg(Cg)
        } else {
          Cg = this.getParamCg(Cg)
        }
        Cg.usedType = this.usedType
        Cg.displayWidth = this.liveWidth
        Cg.displayHeight = this.liveHeight
        let param = {Cg: Cg}
        if (this.cgs && this.cgs.length) {
          param.level = this.cgs.length + 1
        }
        // this.testNode()
        if (!this.isEditCg) {
          liveApi.addCg(param, () => {
            this.setCgs({index: 0, deleteNum: 0, cgItem: Cg})
            this.$emit('updateCg')
            this.$emit('close')
          }, (code, msg) => {
            this.$message.error(msg)
          })
        } else {
          liveApi.setCg(param, () => {
            this.setCgs({index: 0, deleteNum: 1, cgItem: Cg, id: Cg.id})
            this.$emit('updateCg')
            this.$emit('close')
          }, (code, msg) => {
            this.$message.error(msg)
          })
        }
      },
      testNode () {
        let param = {'id': 121575, 'name': '', 'type': 'weather', 'obj': {'cityName': '四川省', 'cityCode': '510000', 'dataType': 'lastThreeDays', 'coordinates': 2, 'styleType': 1, 'transparence': 1, 'scale': 0.5249999999999999}, 'width': 164, 'height': 43, 'dx': 0, 'dy': 0, 'loop': 0, 'pgmFlag': true, 'pvwFlag': false, 'weather': {'styleType': 1, 'dataType': 'lastThreeDays', 'cityName': '四川省', 'date': '2022-12-17', 'weekday': '周六', 'weather': '阴', 'temperature': '', 'highTemperature': '11', 'lowTemperature': '3', 'windDirection': '北', 'windPower': '≤3级'}, 'userData': '', 'path': '/opt/fullTimeLiveGo/usr/1/weather-1670999151006347334.png'}
        Server.requestJson('/testDjy', param, (res) => {
          console.log(res)
        }, (err) => {
          console.error(err)
        })
      },
      close () {
        this.$emit('close')
      },
      getBg () {
        let canvas = document.createElement('canvas')
        let video = document.getElementById('pgm')
        canvas.width = this.optionAreaWidth
        canvas.height = this.optionAreaHeight
        canvas.getContext('2d')
          .drawImage(video, 0, 0, canvas.width, canvas.height)
        let img = this.$refs.bgImg
        img.src = canvas.toDataURL('image/png')
      },
      ...mapMutations({
        setCgs: 'SET_CGS'
      })
    },
    created () {
      if (this.isEditCg || this.cgInfo) {
        this.currentCgInfo = this.getCgInfo(this.$_deepCopy(this.cgInfo))
      } else {
        this.currentCgInfo = this.$_deepCopy(DefaultSubtitle(this.liveWidth, this.liveHeight))
        this.currentCgInfo.usedType = this.usedType
        this.currentCgInfo.displayWidth = this.liveWidth
        this.currentCgInfo.displayHeight = this.liveHeight
      }
      this.$bus.$emit('startCapture', 1)
    },
    mounted () {
      this.$nextTick(() => {
        this.getBg()
      })
      this.$bus.$on('finishCapture', (data) => {
      })
    }
  }
</script>

<style scoped lang="scss">
  .subtitle-cg-dialog {
    user-select: none;
    .st-cg-wrapper {
      display: flex;
      .st-cg-left {
        margin-right: 20px;
        .st-option-area {
          position: relative;
          background: url("./img/cg-box-bg.png");
        }
      }
      .st-cg-right {
        position: relative;
        flex: 0 0 314px;
        width: 314px;
        padding-bottom: 34px;
        .icon {
          margin-right: 6px;
        }
        .align-type {
          flex: 0 0 30px;
          width: 30px;
          height: 30px;
          margin-right: 8px;
          border: 1px solid #22262E;
          border-radius: 2px;
          background-color: #424957;
          cursor: pointer;
          &.active {
            border-color: $color-theme;
          }
          &:hover {
            opacity: .8;
          }
        }
        .dialog-footer {
          @include flex-v-c();
          justify-content: flex-end;
          position: absolute;
          bottom: 0;
          left: 0;
          width: 100%;
          height: 34px;
        }
      }
    }
    .st-cg-box {
      position: absolute;
      transform-origin: top left;
      z-index: 10;
      left: 0;
      top: 0;
      overflow: hidden;
      .st-cg-container {
        position: absolute;
        &.clock {
          display: inline-block;
          cursor: pointer;
          white-space: nowrap;
        }
        &.timekeeping {
          display: inline-block;
          cursor: pointer;
          white-space: nowrap;
          .hasBorder {
            display: inline-block;
            padding: 0px 8px;
            border-radius: 2px;
          }
        }
        &.weather {
          display: inline-block;
          cursor: pointer;
          white-space: nowrap;
        }
        &.move {
          left: 0;
          width: 100%;
          text-align: center;
          cursor: pointer;
          white-space: nowrap;
          @include elliptical;
        }
        .container-anchor {
          position: absolute;
          width: 6px;
          height: 6px;
          background-color: #fff;
          &.nw {
            left: -3px;
            top: -3px;
            cursor: nw-resize;
          }
          &.ne {
            right: -3px;
            top: -3px;
            cursor: ne-resize;
          }
          &.se {
            bottom: -3px;
            right: -3px;
            cursor: se-resize;
          }
          &.sw {
            bottom: -3px;
            left: -3px;
            cursor: sw-resize;
          }
        }
        .st-cg-list {
          position: absolute;
          left: 0;
          top: 0;
          width: 100%;
          height: 100%;
          cursor: pointer;
          .st-cg-text {
            position: absolute;
            border: 2px dashed #fff;
            .st-text-wrapper {
              overflow: hidden;
            }
            .st-text-val {
              display: table-cell;
            }
            .st-img-wrapper {
              background-size: 100% 100%;
              background-position: center;
              background-repeat: no-repeat;
            }
          }
        }
      }
    }
    .style-box {
      width: 100%;
      padding: 15px 6px;
      background-color: #4F5869;
      .content-item {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 150px;
        height: 32px;
        background: #1B1E24;
        font-size: 12px;
        font-family: Microsoft YaHei;
        color: #AEBBD9;
      }
      .no-border {
        font-size: 20px;
        font-weight: bold;
      }
      .border-style {
        display: inline-block;
        width: 27px;
        height: 24px;
        background: #DCE3F2;
        border-radius: 2px;
        text-align: center;
        line-height: 24px;
        font-size: 16px;
        font-weight: bold;
        color: #394050;
      }
      /deep/.el-radio {
        margin-right: 8px;
      }
    }
    .speed-slider {
      width: calc(100% - 60px);
    }
    /deep/ .el-form-item {
      margin-bottom: 10px;
    }
    /deep/ .el-slider__runway,
    /deep/ .el-slider__bar {
      height: 4px;
    }
    /deep/ .el-slider__button {
      width: 12px;
      height: 12px;
    }
  }
  /deep/ .el-color-picker__trigger {
    border: none;
    padding: 0;
  }
  /deep/ .el-textarea__inner {
    background-color: #14181F;
    color: #B5BBCA;
  }
  .cursorNormal {
    cursor: default!important;
  }
  .weather-postion /deep/ {
    .el-radio {
      margin-bottom: 8px;
      margin-right: 36px;
    }
  }
  .tips {
    font-size: 12px;
    color: #677288;
    line-height: 20px;
  }
</style>
