<template>
  <div class="tab-title">
    <div class="content">
      <span class="line"></span>
      <div class="text">
        <span>{{titleText}}</span>
        <slot />
      </div>
    </div>
    <div class="right-option">
      <slot name="right" />
    </div>
  </div>
</template>

<script>
  export default {
    name: 'tab-title',
    props: {
      titleText: {
        type: String
      }
    }
  }
</script>

<style scoped lang="scss">
  .tab-title {
    position: relative;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    height: 40px;
    font-size: 16px;
    font-weight: bold;
    padding-left: 30px;
    line-height: 40px;
    margin: 10px 0;
    .content {
      .line {
        display: inline-block;
        width: 4px;
        height: 20px;
        background-color: #ff5b1f;
        position: absolute;
        left: 15px;
        top: 10px;
      }
      .text {
        display: flex;
        align-items: center;
      }
    }
    .right-option {
      margin-left: 20px;
    }
  }
</style>
