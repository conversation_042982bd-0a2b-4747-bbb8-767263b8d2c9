
function deepCopy (source) {
  if (source === undefined || source === null) {
    return null
  }
  if (typeof source === 'number' || typeof source === 'string' || typeof source === 'boolean') {
    return source
  }
  if (source instanceof Array) {
    let result = []
    source.forEach(item => {
      result.push(deepCopy(item))
    })
    return result
  }
  let result = {}
  for (var key in source) {
    result[key] = typeof source[key] === 'object' ? deepCopy(source[key]) : source[key]
  }
  return result
}
export default {
  deepCopy
}
