import Server from 'common/js/request'
import store from 'store/index'
const CryptoJS = require('crypto-js')

let instance = null
let VERSION = '1.1'

let localConfig = {
  isLocal: true,
  secretKey: '',
  liveId: '8008208820',
  instanceIp: '************'
}
const getAuthToken = function (timestamp) {
  if (localConfig.isLocal) {
    let time = store.getters.localTimeDifference + new Date().getTime()
    let content = time + '_' + localConfig.liveId
    return time + '@' + CryptoJS.HmacSHA1(content, '').toString(CryptoJS.enc.Base64)
  } else {
    return instance.$_getAuthToken(new Date().getTime())
  }
}
export default {
  bind: function (vue) {
    instance = vue
  },
  resetInstance (param, successCall, failCall) {
    param.token = getAuthToken()
    param.version = VERSION
    Server.requestJson('/ctrl/reset?instanceId=' + param.instanceId, param, successCall, failCall)
  },
  restartInstance (param, successCall, failCall) {
    param.token = getAuthToken()
    param.version = VERSION
    Server.requestJson('/ctrl/restart?instanceId=' + param.instanceId, param, successCall, failCall)
  },
  rebootSever (param, successCall, failCall) {
    param.token = getAuthToken()
    param.version = VERSION
    Server.requestJson('/ctrl/reboot', param, successCall, failCall)
  }
}
