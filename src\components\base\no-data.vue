<template>
  <div class="no-data wh100 flex-c-c">
    <div style="margin-top: -10%;">
      <div class="no-data-box">
        <img class="no-data-img w100" src="./blank-icon.png" alt="">
      </div>
      <div class="no-data-tips">{{tips}}</div>
    </div>
  </div>
</template>

<script>
  export default {
    name: 'no-data',
    props: {
      tips: {
        type: String,
        default: '暂无数据'
      }
    },
    data () {
      return {}
    }
  }
</script>

<style scoped lang="scss">
  .no-data {
    line-height: 16px;
    text-align: center;
    .no-data-box {
      width: 100%;
      margin: 6px auto;
    }
    .no-data-tips {
      color: $color-tips;
    }
  }
</style>
