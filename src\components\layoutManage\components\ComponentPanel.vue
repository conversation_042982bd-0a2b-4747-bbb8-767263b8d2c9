<template>
  <div class="component-panel">
    <div class="component-list">
      <div
        v-for="component in componentTypes"
        :key="component.type"
        class="component-item"
        :class="{ disabled: component.disabled }"
        @click="addComponent(component.type)"
        :title="component.title"
      >
        <div class="component-icon">
          <svg-icon :icon-class="component.icon"></svg-icon>
        </div>
        <div class="component-label">{{ component.label }}</div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ComponentPanel',
  props: {
    hasBackground: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      // 组件类型配置数据
      componentTypesConfig: [
        {
          type: 'stream',
          label: '直播源',
          title: '直播源',
          icon: 'layout-live'
        },
        {
          type: 'video',
          label: '点播源',
          title: '点播源',
          icon: 'layout-video'
        },
        {
          type: 'bg',
          label: '背景',
          title: '背景',
          icon: 'layout-bg'
        },
        {
          type: 'logo',
          label: '图片',
          title: '图片',
          icon: 'layout-img'
        },
        {
          type: 'subtitle',
          label: '文字',
          title: '文字',
          icon: 'layout-text'
        },
        {
          type: 'web',
          label: '网页',
          title: '网页',
          icon: 'layout-web'
        },
        {
          type: 'program',
          label: '节目单',
          title: '节目单',
          icon: 'layout-program'
        }
      ]
    }
  },
  computed: {
    componentTypes() {
      return this.componentTypesConfig.map(config => ({
        ...config,
        disabled: config.type === 'bg' && this.hasBackground
      }))
    }
  },
  methods: {
    addComponent(type) {
      if (type === 'bg' && this.hasBackground) {
        this.$message.warning('背景组件最多只能添加一个')
        return
      }

      this.$emit('add-component', type)
    }
  }
}
</script>

<style lang="scss" scoped>
.component-panel {
  height: 100%;
  padding: 10px 0;
  box-sizing: border-box;
}

.component-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
  align-items: center;
}

.component-item {
  width: 50px;
  height: 50px;
  border-radius: 6px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid transparent;
  color: #BEC9E5;

  &:hover:not(.disabled) {
    background: #555D70;
    transform: translateY(-1px);
  }

  &.disabled {
    opacity: 0.5;
    cursor: not-allowed;

    .component-icon {
      color: #666666;
    }
  }

  .component-icon {
    font-size: 20px;
    margin-bottom: 5px;
  }

  .component-label {
    font-size: 10px;
    text-align: center;
    line-height: 1;
  }
}
</style>
