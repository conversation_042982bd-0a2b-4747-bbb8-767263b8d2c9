<template>
  <el-dialog class="program-select" title="选择节目" :visible.sync="showDialog" width="1160px" top="10px" :close-on-click-modal="false" @close="$emit('close')" append-to-body>
        <el-input placeholder="请输入名称" class="top-search-input" style="width: 340px" v-model.trim="search.name">
      <el-button class="search-button" slot="append" icon="search" @click="getPageInfo(1)">
        <i class="el-icon-search"></i>
        搜索</el-button>
    </el-input>
    <div class="program-wrapper">
      <div class="list-header" ref="header">
        <span class="program-name">节目名称</span>
        <span class="program-duration">时长</span>
        <span class="cg-name">CG名称</span>
        <span class="audio">音频文件名</span>
        <span class="program-number">排列序号
          <el-tooltip effect="light" content="节目按序号从小到大排列，仅在节目管理中有效" placement="top">
            <span class="el-icon-warning" style="margin-left: 5px"></span>
          </el-tooltip>
        </span>
      </div>
      <div class="program-ul" id="program-ul">
        <div class="program-li program-drag"
             :key="item.id" v-for="(item, index) in pageList.data">
          <div class="program-name">
            <div class="name" :title="item.program.name">{{item.program.name}}</div>
            <div class="local-tag" v-show="hasLocalTag(item.program)">本地</div>
          </div>
          <div class="program-duration">
            <div class="time" :title="item.program.duration">{{item.program.duration / 1000 | formatDuration}}</div>
          </div>
          <div class="cg-name">
            <ul class="g-hide-scroll cg-name-ul" v-if="item.program.cgIds && item.program.cgIds.length">
              <li class="cg-name-li" v-for="(v, i) in item.program.cgIds" :title="getCgName(v)">{{i + 1}}、{{getCgName(v)}}</li>
            </ul>
            <span v-else>-</span>
          </div>
          <div class="audio">
            <ul class="g-hide-scroll audio-ul" v-if="item.program.audios && item.program.audios.length">
              <li class="audio-li" v-for="(v, i) in item.program.audios" :class="{lineThrough: v.isDeleted}" :title="v.audioName">{{i + 1}}、{{v.audioName}}</li>
            </ul>
            <span v-else>-</span>
          </div>
          <div class="program-number">{{item.orderNum === 2147483647 ? '' : item.orderNum}}</div>
          <div class="option-btn">
            <el-button type="primary" round size="small" @click="selectItem(item)">选择</el-button>
          </div>
        </div>
        <div class="program-empty wh100" v-if="!pageList.data.length">
          <no-data tips="暂无节目"></no-data>
        </div>
      </div>
    </div>
    <footer-pagination small :page="pageList.page" @getPageInfo="getPageInfo"></footer-pagination>
  </el-dialog>
</template>

<script>
  import {mapGetters} from 'vuex'
  import cloudApi from 'api/cloud'
  import footerPagination from '@/components/pagination/footer-pagination'

  export default {
    name: 'program-select',
    computed: {
      ...mapGetters([
        'cgs',
        'audio'
      ])
    },
    components: {footerPagination},
    data () {
      return {
        showDialog: true,
        search: {
          name: ''
        },
        pageList: {
          data: [],
          page: this.Page()
        }
      }
    },
    methods: {
      hasLocalTag (program) {
        let masterChannel = program.video.channels.find(v => v.idx === 0)
        return masterChannel.localFileName && masterChannel.type === 'local'
      },
      selectItem (item) {
        let newItem = this.$_deepCopy(item)
        newItem.program.duration = newItem.program.duration / 1000
        this.$emit('close')
        this.$emit('selectConfirm', newItem)
      },
      getPageInfo (page) {
        let param = {}
        param.page = page || this.pageList.page.pageIndex
        if (this.search.name) {
          param.name = this.search.name
        }
        this.pageList.loading = true
        cloudApi.getProgramManageList(param, res => {
          if (res && res.data) {
            this.pageList.data = res.data
            this.pageList.data.forEach(v => {
              v.program = JSON.parse(v.program)
            })
            this.GetPage(this.pageList, res)
          }
        }, (code, msg) => {
          this.$message.error(msg)
        })
      },
      getCgName (id) {
        let index = this.cgs.findIndex(item => {
          return item.id === id
        })
        if (index >= 0) {
          return this.cgs[index].name
        } else {
          return '该CG已删除'
        }
      }
    },
    created () {
      this.getPageInfo(1)
    }
  }
</script>

<style scoped lang="scss">
  .program-select {
    height: 100%;
    .search-button {
      background-color: #535D72;
      color: #BCC9E8;
      &:hover {
        background-color: #5e6678;
      }
    }
    .program-wrapper {
      margin-top: 13px;
      height: 700px;
      box-sizing: border-box;
    }
    .program-ul {
      height: calc(100% - 40px);
      overflow-y: auto;
      overflow-x: hidden;
      color: #B2BFDE;
      .program-li {
        position: relative;
        display: flex;
        justify-content: flex-start;
        padding: 6px 0;
        background: #292E37;
        box-sizing: border-box;
        height: 90px;
        &:hover {
          background: #3E4252;
        }
      }
    }
    .list-header {
      display: flex;
      justify-content: flex-start;
      background-color: #21252D;
      color: #646D80;
      .program-number,
      .program-name,
      .program-duration,
      .audio,
      .cg-name {
        padding: 10px 0;
      }
    }
    .option-btn {
      margin-left: 50px;
      display: flex;
      justify-content: center;
      align-items: center;
    }
    .audio {
      display: flex;
      align-items: center;
      flex: 0 0 17%;
      width: 17%;
      padding-right: 40px;
      box-sizing: border-box;
      .audio-ul {
        width: 100%;
        max-height: 78px;
        display: flex;
        flex-direction: column;
        overflow-y: auto;
      }
      .audio-li {
        flex-shrink: 0;
        display: inline-block;
        width: 100%;
        @include elliptical();
      }
    }
    .cg-name {
      display: flex;
      align-items: center;
      flex: 0 0 17%;
      width: 17%;
      padding-right: 40px;
      box-sizing: border-box;
      .cg-name-ul {
        width: 100%;
        max-height: 78px;
        display: flex;
        flex-direction: column;
        overflow-y: auto;
      }
      .cg-name-li {
        display: inline-block;
        width: 100%;
        flex-shrink: 0;
        @include elliptical();
      }
    }
    .program-number {
      display: flex;
      align-items: center;
      justify-content: center;
      flex: 0 0 3%;
      width: 3%;
      white-space: nowrap;
    }
    .program-name {
      display: flex;
      align-items: center;
      text-indent: 40px;
      flex: 0 0 20.5%;
      width: 20.5%;
      padding-right: 40px;
      box-sizing: border-box;
      .name {
        display: inline-block;
        max-width: 100%;
        @include elliptical();
      }
      .local-tag {
        margin-left: 5px;
        display: inline-block;
        background: #5771AB;
        padding: 2px 6px;
        border-radius: 2px;
        color: #D1DFFF;
        font-size: 12px;
        white-space: nowrap;
        text-indent: 0;
      }
    }
    .program-duration {
      display: flex;
      align-items: center;
      flex: 0 0 10%;
      width: 10%;
    }
  }
  .program-dialog {
    .live-type-tips {
      color: $color-tips;
    }
  }
  ::v-deep .el-checkbox__label {
    padding-left: 0!important;
  }
  ::v-deep .el-input-group__append {
    background-color: #535D72;
  }
  ::v-deep .el-checkbox__input {
    transform: scale(1.3);
  }
  ::v-deep .el-table td.el-table__cell {
    border-bottom: 1px solid #434A58;
  }
  .el-icon-warning {
    color: #535D72;
  }
  .tips {
    position: absolute;
    bottom: 25px;
    color: #8892A7;
  }
  ::v-deep .el-button--text:focus {
    color: #627FC2;
  }
  ::v-deep .el-button--text:hover {
    color: #FF5B0C;
  }
  ::v-deep .orderInput.el-input-number .el-input__inner {
    text-align: left;
  }
  ::v-deep .el-dialog__body {
    padding: 16px 20px 0 20px;
  }
</style>
