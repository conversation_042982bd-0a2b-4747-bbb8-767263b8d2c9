<template>
  <div class="program-properties">
    <div class="property-section">
      <h4 class="section-title">选择节目单</h4>
      <div class="program-selector">
        <div class="selected-program">
          <span class="program-name" :title="selectedProgramName">
            {{ selectedProgramName || '请选择节目单' }}
          </span>
          <el-button type="primary" size="mini" @click="showProgramDialog = true">
            {{ selectedProgramName ? '更换' : '选择' }}
          </el-button>
        </div>
      </div>
    </div>

    <!-- 位置大小 -->
    <PositionSizeProperties
      :component="component"
      :canvas-size="canvasSize"
      @update="handleUpdate"
    />

    <!-- 画面显示 -->
    <DisplayModeProperties
      :component="component"
      @update="handleUpdate"
    />

   <!-- 播放模式 -->
   <div class="property-section">
      <div class="section-title">播放模式</div>
      <div class="property-group">
        <el-radio-group v-model="localPlayType" @change="handlePlayTypeChange">
          <el-radio label="loop">循环播放</el-radio>
          <el-radio label="once">只播一次</el-radio>
        </el-radio-group>
      </div>
    </div>

    <!-- 音频调节 -->
    <VolumeProperties
      :component="component"
      @update="handleUpdate"
    />

    <!-- 不透明度和特效 -->
    <EffectProperties
      :component="component"
      @update="handleUpdate"
    />

    <!-- 选择节目单对话框 -->
    <el-dialog
      title="选择节目单"
      :visible.sync="showProgramDialog"
      width="600px"
      :modal="false"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <div class="program-dialog-content">
        <el-table
          :data="programList"
          v-loading="programLoading"
          element-loading-text="加载节目单列表..."
          height="400"
          @row-click="handleRowClick"
          highlight-current-row
        >
          <el-table-column prop="name" label="名称" min-width="200">
            <template slot-scope="scope">
              <span :title="scope.row.name">{{ scope.row.name }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="programCount" label="节目数" width="80" align="center">
            <template slot-scope="scope">
              <span>{{ scope.row.programs ? scope.row.programs.length : 0 }}</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="80" align="center">
            <template slot-scope="scope">
              <el-button
                type="text"
                size="small"
                @click="handleSelectProgram(scope.row)"
                :disabled="scope.row.id === component.programId"
              >
                {{ scope.row.id === component.programId ? '已选择' : '选择' }}
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="showProgramDialog = false">取消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import PositionSizeProperties from './common/PositionSizeProperties.vue'
import DisplayModeProperties from './common/DisplayModeProperties.vue'
import VolumeProperties from './common/VolumeProperties.vue'
import EffectProperties from './common/EffectProperties.vue'
import Live from '@/api/live'

export default {
  name: 'ProgramProperties',
  components: {
    PositionSizeProperties,
    DisplayModeProperties,
    VolumeProperties,
    EffectProperties
  },
  props: {
    component: {
      type: Object,
      required: true
    },
    canvasSize: {
      type: Object,
      default: () => ({ width: 1920, height: 1080 })
    }
  },
  data() {
    return {
      showProgramDialog: false,
      programList: [],
      programLoading: false,
      localZIndex: this.component.zIndex || 1,
      localPlayType: this.component.playType || 'loop'
    }
  },
  computed: {
    selectedProgramName() {
      if (!this.component.programId) return ''
      const program = this.programList.find(p => p.id === this.component.programId)
      return program ? program.name : `节目单 #${this.component.programId}`
    }
  },
  watch: {
    'component.zIndex'(newVal) {
      this.localZIndex = newVal || 1
    },
    'component.playType'(newVal) {
      this.localPlayType = newVal || 'loop'
    },
    showProgramDialog(val) {
      if (val) {
        this.loadProgramList()
      }
    }
  },
  mounted() {
    // 如果已经有选中的节目单，也要加载列表以显示名称
    if (this.component.programId) {
      this.loadProgramList()
    }
  },
  methods: {
    handleUpdate(data) {
      this.$emit('update', data)
    },
    async loadProgramList() {
      this.programLoading = true
      try {
        await new Promise((resolve, reject) => {
          Live.getPgmList({}, (response) => {
            console.log('获取节目单列表成功:', response)
            if (response.schedules && response.schedules.length) {
              this.programList = response.schedules.map(program => ({
                id: program.id,
                name: program.name || `节目单 #${program.id}`,
                programs: program.programs || [],
                createTime: program.createTime,
                status: program.status
              }))
            } else {
              this.programList = []
            }
            resolve(response)
          }, (error) => {
            console.error('获取节目单列表失败:', error)
            this.$message.error('获取节目单列表失败')
            this.programLoading = false
            reject(error)
          })
        })
      } catch (error) {
        console.error('加载节目单列表异常:', error)
      } finally {
        this.programLoading = false
      }
    },

    handleSelectProgram(program) {
      console.log('选择节目单:', program)
      this.$emit('update-component', {
        ...this.component,
        programId: program.id,
        name: program.name
      })
      this.showProgramDialog = false
      this.$message.success(`已选择节目单: ${program.name}`)
    },

    handleRowClick(row) {
      // 点击行也可以选择
      this.handleSelectProgram(row)
    },

    handleZIndexChange(value) {
      this.$emit('update-component', {
        ...this.component,
        zIndex: value
      })
    },

    handlePlayTypeChange(value) {
      this.$emit('update-component', {
        ...this.component,
        playType: value
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.program-properties {
  .property-section {
    margin-bottom: 20px;

    .section-title {
      font-size: 14px;
      font-weight: 500;
      margin-bottom: 12px;
      padding: 0 16px;
    }
  }

  .property-group {
    margin-bottom: 16px;
    padding: 0 16px;
  }
  .program-selector {
    .selected-program {
      display: flex;
      align-items: center;
      gap: 8px;
      padding: 8px 12px;
      background: #383838;
      border: 1px solid #4a4a4a;
      border-radius: 4px;

      .program-name {
        flex: 1;
        color: #cccccc;
        font-size: 13px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        min-height: 20px;
        line-height: 20px;
      }

      .el-button {
        flex-shrink: 0;
        background: #ff6b35;
        border-color: #ff6b35;
        color: #ffffff;

        &:hover {
          background: #ff5722;
          border-color: #ff5722;
        }
      }
    }
  }

  :deep(.el-input-number) {
    width: 100%;

    .el-input__inner {
      background: #383838;
      border-color: #4a4a4a;
      color: #ffffff;

      &:focus {
        border-color: #ff6b35;
      }
    }
  }

  :deep(.el-radio-group) {
    .el-radio {
      color: #ffffff;
      margin-right: 20px;

      .el-radio__input.is-checked .el-radio__inner {
        background: #ff6b35;
        border-color: #ff6b35;
      }

      .el-radio__label {
        color: #B0BBD6;
      }
    }
  }
}

.program-dialog-content {
  .empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 200px;
    color: #999999;

    i {
      font-size: 48px;
      margin-bottom: 12px;
      color: #666666;
    }

    p {
      margin: 0;
      font-size: 14px;
    }
  }

  :deep(.el-table) {
    background: transparent;

    &::before {
      display: none;
    }

    .el-table__header {
      th {
        background: #2c2c2c;
        color: #ffffff;
        border-bottom: 1px solid #4a4a4a;
      }
    }

    .el-table__body {
      tr {
        background: #383838;

        &:hover {
          background: #444444 !important;
        }

        &.current-row {
          background: #4a4a4a !important;
        }

        td {
          border-bottom: 1px solid #4a4a4a;
          color: #cccccc;
        }
      }
    }

    .el-table__empty-block {
      background: transparent;
      color: #999999;
    }
  }
}

:deep(.el-dialog) {
  background: #2c2c2c;

  .el-dialog__header {
    background: #2c2c2c;
    border-bottom: 1px solid #4a4a4a;

    .el-dialog__title {
      color: #ffffff;
    }

    .el-dialog__close {
      color: #cccccc;

      &:hover {
        color: #ffffff;
      }
    }
  }

  .el-dialog__body {
    background: #2c2c2c;
    color: #cccccc;
  }

  .el-dialog__footer {
    background: #2c2c2c;
    border-top: 1px solid #4a4a4a;
  }
}
</style>
