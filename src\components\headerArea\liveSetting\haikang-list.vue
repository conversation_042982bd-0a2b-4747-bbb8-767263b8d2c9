<template>
  <div class="haikang-list-box" ref="listBox">
    <div style="margin: 0 auto">
      <el-button type="text" style="float: right" @click="getPageInfo(1)" icon="el-icon-refresh">刷新</el-button>
      <div class="table-header" style="margin-top: 40px">
        <span style="width: 300px">唯一标识</span>
        <span style="width: 300px">名称</span>
      </div>
      <div class="table-main">
        <div class="table-li" v-for="(item, index) in pageList.data" :class="{otherBg: index % 2 !== 0, select: currentIndex === index}">
          <div class="li-header">
            <span class="li-span" style="width: 300px">{{item.cameraIndexCode}}</span>
            <span class="li-span" style="width: 300px">{{item.cameraName}}</span>
            <span class="check-btn li-span" @click="checkItem(item, index)">查看链接></span>
          </div>
          <div class="check-item">
            <div class="left-item">
              <span class="item">RTMP流：</span>
              <span class="item" :title="currentItem.rtmp">{{currentItem.rtmp}}</span>
            </div>
            <el-button type="text copy-btn1" :data-clipboard-text="currentItem.rtmp">复制</el-button>
          </div>
          <div class="check-item">
            <div class="left-item">
              <span class="item">RTSP流：</span>
              <span class="item" :title="currentItem.rtsp">{{currentItem.rtsp}}</span>
            </div>
            <el-button type="text copy-btn1" :data-clipboard-text="currentItem.rtsp">复制</el-button>
          </div>
          <div class="check-item">
            <div class="left-item">
              <span class="item">HLS流：</span>
              <span class="item" :title="currentItem.hls">{{currentItem.hls}}</span>
            </div>
            <el-button type="text copy-btn1" :data-clipboard-text="currentItem.hls">复制</el-button>
          </div>
        </div>
        <div class="empty-box wh100" v-if="pageList.data && !pageList.data.length">
          <no-data tips="暂无数据"></no-data>
        </div>
      </div>
      <footer-pagination style="margin: 10px 0" small :page="pageList.page" @getPageInfo="getPageInfo"></footer-pagination>
    </div>
  </div>
</template>

<script>
  import footerPagination from '@/components/pagination/footer-pagination'
  import liveApi from 'api/live'
  import noData from 'components/base/no-data'
  import Clipboard from 'clipboard'

  export default {
    name: 'haikang-list',
    components: {footerPagination, noData},
    data () {
      return {
        pageList: {
          data: [],
          page: this.Page(), // 对page初始化 page={pageIndex: 1,amount: 0,pageSize: 20}
          loading: false
        },
        currentItem: {
          index: -1,
          rtmp: '',
          rtsp: '',
          hls: ''
        },
        currentIndex: -1
      }
    },
    methods: {
      clipboard () {
        let clipboard = new Clipboard('.copy-btn1')
        clipboard.on('success', (e) => {
          this.$message.success('已复制')
        })
      },
      getPageInfo (page) {
        this.currentIndex = -1
        this.currentItem.index = -1
        let param = {
          page: page || this.pageList.page.pageIndex
        }
        liveApi.getHaikangList(param, res => {
          if (res) {
            this.pageList.data = res.data
          }
          this.GetPage(this.pageList, res)
        }, (code, msg) => {
          this.$message.warning(msg)
        })
      },
      checkItem (item, index) {
        if (this.currentIndex === index) {
          this.currentIndex = -1
          this.currentItem.index = index
        } else if (index === this.currentItem.index) {
          this.currentIndex = index
          this.currentItem.index = index
        } else {
          liveApi.getHaikangUrl({cameraIndexCode: item.cameraIndexCode}, res => {
            if (res) {
              this.currentItem.rtmp = res.rtmp
              this.currentItem.rtsp = res.rtsp
              this.currentItem.hls = res.hls
              this.currentIndex = index
            }
          }, (code, msg) => {
            this.$message.error(msg)
            this.currentIndex = -1
            this.currentItem.index = -1
          })
        }
      }
    },
    mounted () {
    },
    created () {
      this.clipboard()
      this.getPageInfo(1)
    }
  }
</script>

<style scoped lang="scss">
  .table-header {
    width: 750px;
    background-color: #4A515E;
    padding: 10px 0;
    font-size: 14px;
    color: #B5BBCA;
    span {
      display: inline-block;
      padding-left: 30px;
    }
  }
  .table-main {
    width: 750px;
    height: 360px;
    background-color: #505763;
    overflow-y: auto;
    .table-li {
      width: 100%;
      font-size: 14px;
      color: #B5BBCA;
      height: 40px;
      overflow: hidden;
      transition: height .3s ease-in;
      user-select: contain;
      &.otherBg {
        background-color: #4A515E;
      }
      &.select {
        height: 200px;
        .li-header {
          background-color: #785D5C;
        }
        .check-btn {
          color: #FF5B0C;
        }
      }
      .li-span {
        display: inline-block;
        line-height: 40px;
        padding-left: 20px;
        @include elliptical();
      }
      .check-btn {
        cursor: pointer;
        &:hover {
          color: #FF5B0C;
        }
      }
    }
    .check-item {
      display: flex;
      justify-content: space-between;
      padding: 0 20px;
      height: 55px;

      .left-item {
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        padding: 10px 0;
        width: 80%;
        box-sizing: border-box;

        .item {
          display: inline-block;
          max-width: 100%;
          @include elliptical();
        }
      }
    }
  }
  .haikang-list-box {
    display: flex;
    justify-content: center;
  }
</style>
