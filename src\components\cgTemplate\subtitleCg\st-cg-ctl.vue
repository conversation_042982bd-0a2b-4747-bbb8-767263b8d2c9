<template>
  <div class="st-cg-ctl">
    <div class="st-cg-ctl-item layer" @click.stop="showLayer=!showLayer">
      <i class="icon icon-fount-cg-template"></i>
      <div class="st-cg-layer-box" v-if="showLayer">
        <div class="st-cg-layer-item" :key="item.name" v-for="item in layerList" @click="$emit('changeCgItemIndex', item.name)">
          <img :src="item.img" alt="">
          <span style="margin-left: 4px;">{{item.text}}</span>
        </div>
      </div>
    </div>
    <div class="st-cg-ctl-item delete" @click.stop="$emit('close')">
      <i style="color: #ff0000;" class="icon el-icon-close"></i>
    </div>
  </div>
</template>

<script>
  export default {
    name: 'st-cg-ctl',
    data () {
      return {
        showLayer: false,
        layerList: [
          {name: 'top', text: '置于顶层', img: require('../img/cg-top.png')},
          {name: 'bottom', text: '置于底层', img: require('../img/cg-bottom.png')},
          {name: 'up', text: '上移一层', img: require('../img/cg-up.png')},
          {name: 'down', text: '下移一层', img: require('../img/cg-down.png')}
        ]
      }
    }
  }
</script>

<style scoped lang="scss">
  .st-cg-ctl {
    position: absolute;
    z-index: 100;
    width: 20px;
    .st-cg-ctl-item {
      @include flex-c-c();
      width: 100%;
      height: 20px;
      background-color: #fff;
      &.layer {
        position: relative;
        margin-bottom: 4px;
        .st-cg-layer-box {
          position: absolute;
          left: -104px;
          top: 0;
          .st-cg-layer-item {
            @include flex-c-c();
            width: 100px;
            height: 30px;
            border-bottom: 1px solid $color-border;
            background-color: #fff;
          }
        }
      }
      .icon {
        font-weight: bold;
      }
    }
  }
</style>
