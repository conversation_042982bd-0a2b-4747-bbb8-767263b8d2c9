<template>
<div class="program-list">
  <div class="top-header">
    <span class="title">{{ currentPgmName }}
      <el-tooltip effect="light" content="指定时间段内没有节目时，节目单循环播放" placement="top">
        <span class="el-icon-warning" style="margin-left: 5px"></span>
      </el-tooltip>
    </span>
    <div>
    <span class="g-option-btn-2" style="width: 74px;height: 28px;line-height: 26px" @click="addProgramDialog()" v-show="roleType !== 2">
      <i class="el-icon-plus" style="margin-right: 5px;font-size: 14px"></i>
      <span>新增</span>
    </span>
  </div>
  </div>
  <div class="program-wrapper">
    <div class="list-header" ref="header">
      <span class="program-number">序号</span>
      <span class="program-name">节目名称</span>
      <span class="program-duration">节目时长</span>
      <span class="program-options">操作</span>
    </div>
    <div class="program-ul g-hide-scroll" id="program-ul">
      <div class="program-li" :id="item.id" :class="{'program-drag' : !isLiving(item), 'active-living': isLiving(item), 'active-next': isNextLiving(item)}"
            :key="item.id" v-for="(item, index) in programList" @mouseenter="programOptionShow(item.id)" @mouseleave="programLeave(item.id)">
        <div class="status-flag" v-if="isLiving(item)">
          <svg-icon icon-class="double-arrow" style="margin-left: 3px"></svg-icon>
          <span>onAir</span>
        </div>
        <div class="status-flag" v-if="isNextLiving(item)">
          <span style="margin-left: 5px">staby</span>
        </div>
        <div class="program-number">{{index + 1}}</div>
        <div class="program-name">
          <i class="icon el-icon-video-camera-solid" v-if="isLiving(item)"></i>
          <div class="name" :title="item.name">{{item.name}}</div>
        </div>
        <div class="program-duration">
          <div class="time">{{item.duration | formatDuration}}</div>
        </div>
        <div class="program-option" :class="{'show-option': showOptionId === item.id}">
          <template v-if="roleType !== 2">
            <el-button type="danger" class="my-operate-btn" @click="cutPlay(item)" v-if="!isLiving(item)">切播</el-button>
            <el-dropdown @visible-change="programOptionShow1($event)">
              <span class="my-operate-btn my-operate-btn-span">更多</span>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item @click.native="showPreview(item)" v-if="!isLiving(item)">预览</el-dropdown-item>
                <el-dropdown-item @click.native="slotProgramDialog(item, index)">插播</el-dropdown-item>
                <el-dropdown-item @click.native="relateSubtitleShow(item)">关联字幕</el-dropdown-item>
                <el-dropdown-item @click.native="showRelateAudioDialog(item)">关联音频</el-dropdown-item>
                <el-dropdown-item @click.native="showTransitionPicker(item)">转场特效</el-dropdown-item>
                <el-dropdown-item @click.native="showPinpDialog(item)">画中画</el-dropdown-item>
                <el-dropdown-item @click.native="showFramesClipDialog(item)">画面裁剪</el-dropdown-item>
               <el-dropdown-item @click.native="showPaletteDialog(item)">调色</el-dropdown-item>
                <el-dropdown-item @click.native="showLayoutSelectDialogForProgram(item)">关联布局</el-dropdown-item>
                <span class="dropdown-line"></span>
                <el-dropdown-item @click.native="editProgramsDialog(item)">编辑</el-dropdown-item>
                <el-dropdown-item @click.native="copyProgram(item)">复制</el-dropdown-item>
                <el-dropdown-item @click.native="deleteProgram(item)" v-show="!isLiving(item)">删除</el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </template>
          <template v-else>
            <el-button type="danger" class="my-operate-btn" @click="showPreview(item)" v-if="!isLiving(item)">预览</el-button>
          </template>
        </div>
      </div>
      <div class="program-empty wh100" v-if="!programList.length">
        <no-data tips="暂无节目"></no-data>
      </div>
    </div>
  </div>
  <el-dialog class="relate-audio-first" title="节目关联音乐" width="700px" top="70px" :visible.sync="programAudioDialog" :close-on-click-modal="false" v-if="programAudioDialog">
    <el-table :data="relateItem.audios" style="width: 100%" stripe height="500" element-loading-spinner="el-icon-loading"
              element-loading-background="rgba(0, 0, 0, 0.8)" border row-key="id" size="small">
      <el-table-column label="音频文件名" prop="audioName"></el-table-column>
      <el-table-column label="类型">
        <template slot-scope="scope">
          <span>{{scope.row.audioType ? '音频流' : '点播'}}</span>
        </template>
      </el-table-column>
      <el-table-column label="音频时长">
        <template slot-scope="scope">
          <span>{{scope.row.duration | formatDurationMs}}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作">
        <template slot-scope="scope">
          <el-button type="info" @click="cancelRelate(scope.row)" size="mini">取消关联</el-button>
        </template>
      </el-table-column>
      <div class="empty-box wh100" v-if="relateItem.audios && !relateItem.audios.length" slot="empty">
        <no-data tips="暂无音频"></no-data>
      </div>
    </el-table>
    <div  slot="footer" class="dialog-footer">
      <el-button size="small" type="primary" @click="relateAudio">关联新文件</el-button>
    </div>
    <div class="tips">
      <p>注：</p>
      <p>1、关联多个点播文件时，单个文件播完会自动播放下一个</p>
      <p>2、关联音频直播流时，会一直播放该直播流；需要手动取消关联</p>
    </div>
  </el-dialog>
  <relate-subtitle :programType="programType" v-if="relateSubtitleDialog" @close="relateSubtitleDialog=false" :currentProgram="currentProgram"></relate-subtitle>
  <relate-audio :programType="programType" @close="showRelateAudio=false" v-if="showRelateAudio" :relateItem="relateItem" @callBack="relateCallBack" :selects="relateItem.audios"></relate-audio>
  <frames-clip :programType="programType" @close="showFramesClip = false" v-if="showFramesClip" :currentProgramItem="currentProgramItem"></frames-clip>
  <palette :programType="programType" @close="showPalette = false" v-if="showPalette" :currentProgramItem="currentProgramItem"></palette>
  <pinp-setting :programType="programType" @close="showPinpSetting = false" v-if="showPinpSetting" :currentProgramItem="currentProgramItem"></pinp-setting>
  <preview-dialog v-if="previewDialog" @close="previewDialog = false" :currentProgramItem="currentProgramItem"></preview-dialog>
  <add-program v-model="currentProgramId" v-if="showProgramDialog" @close="showProgramDialog = false" :currentItem="currentItem" :addType="1" :slotId="slotId"></add-program>

  <!-- 布局选择弹窗 -->
  <LayoutSelectDialog
    :visible.sync="showLayoutSelectDialog"
    :target-ratio="currentLiveRatioString"
    @layout-selected="handleLayoutSelected"
    @close="handleLayoutSelectDialogClose"
  />
  <TransitionPicker v-if="transitionPickerShow" @close="transitionPickerShow = false" @confirm="confirmTransition" />
</div>
</template>

<script>
  import {mapGetters, mapActions, mapMutations} from 'vuex'
  import XLSX from 'xlsx'
  import relateSubtitle from '@/components/cgTemplate/relate-subtitle'
  import relateAudio from '@/components/base/relateAudio/relate-audio'
  import framesClip from '@/components/framesClip/frames-clip'
  import palette from '@/components/palette'
  import pinpSetting from '@/components/pinpSetting/pinp-setting'
  import layoutSelectMixin from '@/common/js/layoutSelectMixin'
  import previewDialog from './previewDialog'
  import cloudApi from 'api/cloud'
  import addProgram from '../addProgram'
  import TransitionPicker from '@/components/transitionEffects/transitionDialog.vue'

  export default {
    name: 'program-list',
    mixins: [layoutSelectMixin],
    components: {relateSubtitle, relateAudio, framesClip, palette, pinpSetting, previewDialog, addProgram, TransitionPicker},
    props: {
      nextProgramId: {
        type: String
      },
      currentProgramId: {
        type: String
      }
    },
    computed: {
      getDownUrl () {
        if (process.env.NODE_ENV === 'production') return process.env.VUE_APP_BASE_URL + 'template.xlsx'
        return 'template.xlsx'
      },
      ...mapGetters([
        'roleType',
        'loopPrograms',
        'cgs',
        'localTimeDifference',
        'programsSn',
        'audio',
        'schedules',
        'currentScheduleId'
      ])
    },
    data () {
      return {
        transitionPickerShow: false,
        currentPgmName: '',
        showSelectDialog: true,
        currentItem: null,
        programAudioDialog: false,
        scale: 1,
        showRelateAudio: false,
        programList: [],
        slotId: null,
        slotIndex: -1,
        showProgramDialog: false,
        relateSubtitleDialog: false,
        adding: false,
        programForm: {
          id: 0,
          name: '',
          startTime: '', // 播出时间，本地时间，格式为"2006-01-02 15:04:05"，仅节目表第一个节目有效
          hour: 0, // 节目小时
          minute: 0, // 节目分钟
          second: 0, // 节目秒
          type: 'stream', // "stream"：直播；"record"：录播
          url: '',
          cgIds: [],
          audios: []
        },
        programRules: {
          name: [
            {required: true, message: '请输入节目名称', trigger: 'blur'}
          ],
          url: [
            {required: true, message: '请输入地址', trigger: 'blur'}
          ],
          startTime: [
            {required: true, message: '请选择播出时间', trigger: 'blur'}
          ]
        },
        importData: [],
        currentProgram: '',
        relateItem: {},
        currentProgramItem: {},
        showFramesClip: false,
        showPinpSetting: false,
        previewDialog: false,
        showPalette: false,
        showOptionId: '',
        programType: 'loop'
      }
    },
    methods: {
      programLeave () {
        this.timer = setTimeout(() => {
          if (this.showMore) {
            return
          }
          this.showOptionId = ''
        }, 150)
      },
      programOptionShow (id) {
        clearTimeout(this.timer)
        this.showOptionId = id
      },
      programOptionShow1 (flag) {
        this.showMore = flag
      },
      showPreview (item) {
        if (item.video.channels.length > 0 && item.video.channels[0].type === 'html') {
          this.$message.warning('网页类型节目暂不支持预览')
          return
        }
        this.currentProgramItem = item
        this.previewDialog = true
      },
      showPinpDialog (item) {
        this.currentProgramItem = item
        this.showPinpSetting = true
      },
      showFramesClipDialog (item) {
        this.currentProgramItem = item
        this.showFramesClip = true
      },
      showPaletteDialog (item) {
        this.currentProgramItem = item
        this.showPalette = true
      },
      relateCallBack (editProgram) {
        this.relateItem = editProgram
      },
      cancelRelate (item) {
        let editProgram = this.relateItem
        editProgram.audios.forEach((v, i) => {
          if (v.audioId === item.audioId) {
            editProgram.audios.splice(i, 1)
          }
        })
        let param = {
          programType: this.programType,
          program: editProgram
        }
        this.modProgram(param).then(() => {
          this.relateItem = editProgram
          this.$message.success('取消关联成功')
        })
      },
      async showRelateAudioDialog (item) {
        this.relateItem = this.$_deepCopy(item)
        this.relateItem.audios = await this.getFilterRelateItem(item)
        this.programAudioDialog = true
      },
      showTransitionPicker(item) {
        this.transitionPickerShow = true
      },
      confirmTransition() {},
      getFilterRelateItem (item) {
        return new Promise(resolve => {
          let audioIds = item.audios.map(v => {
            return v.audioId
          })
          cloudApi.checkAudio({ids: audioIds}, res => {
            let result = item.audios.filter(v => {
              return !res.includes(v.audioId)
            })
            resolve(result)
          }, () => {
            resolve(item.audios)
          })
        })
      },
      relateAudio () {
        this.showRelateAudio = true
      },
      relateSubtitleShow (item) {
        this.currentProgram = item.id
        this.relateSubtitleDialog = true
      },
      importExcel (file) {
        // 获取上传的文件对象
        const { files } = file.target
        // 通过FileReader对象读取文件
        const fileReader = new FileReader()
        if (files[0].name.indexOf('.xlsx') > 0 || files[0].name.indexOf('.xls') > 0) {
          fileReader.onload = event => {
            const { result } = event.target
            // 以二进制流方式读取得到整份excel表格对象
            const workbook = XLSX.read(result, { type: 'binary' })
            let data = [] // 存储获取到的数据
            // 遍历每张工作表进行读取（这里默认只读取第一张表）
            for (const sheet in workbook.Sheets) {
              if (workbook.Sheets.hasOwnProperty(sheet)) {
                // 利用 sheet_to_json 方法将 excel 转成 json 数据
                data = data.concat(XLSX.utils.sheet_to_json(workbook.Sheets[sheet]))
                break // 如果只取第一张表，就取消注释这行
              }
            }
            this.importData = this.$_deepCopy(data)
            this.addMorePrograms(this.importData)
          }
        } else {
          // 抛出文件类型错误不正确的相关提示
          this.$message.error('导入文件类型不正确')
          return
        }

        // 以二进制方式打开文件
        fileReader.readAsBinaryString(files[0])
        event.target.value = ''
      },
      splicePrograms (data) {
        let content = this
        let splicePrograms = []
        let topName = ['节目名称', '媒体机构', '节目时长/秒', '直播方式', '流地址', '备注']
        for (let i = 0; i < data.length; i++) {
          for (const dataKey in data[i]) {
            if (topName.indexOf(dataKey) < 0) {
              return content.$message.error('请使用模板文件规范填写资料')
            }
          }
          splicePrograms.push({
            id: '',
            name: '',
            startTime: '',
            duration: 0,
            type: 'stream',
            url: ''
          })
          splicePrograms[i].id = content.$_createRandomId()
          splicePrograms[i].name = data[i]['节目名称'] ? data[i]['节目名称'] + '' : ''
          splicePrograms[i].duration = data[i]['节目时长/秒']
          splicePrograms[i].type = data[i]['直播方式'] === '拉流' ? 'stream' : 'record'
          splicePrograms[i].url = data[i]['流地址']
        }
        return splicePrograms
      },
      addMorePrograms (data) {
        let programsParam = []
        let newProgram = this.splicePrograms(data)
        if (!this.checkForm(newProgram)) {
          return
        }
        programsParam = []
        newProgram.forEach(item => {
          programsParam.push({
            id: item.id,
            name: item.name,
            startTime: null,
            duration: item.duration,
            cgIds: [],
            audios: [],
            video: {
              globalBgmMute: false,
              useBackground: false,
              channels: [
                {
                  type: item.type,
                  url: item.url,
                  idx: 0,
                  crop: null,
                  display: null
                }
              ]
            }
          })
        })

        if (this.adding) {
          return this.$message.warning('正在添加中')
        }
        this.adding = true
        let param = {
          programType: this.programType,
          programs: programsParam
        }
        this.addProgram(param).then(() => {
          this.adding = false
        }, (code, msg) => {
          this.adding = false
        })
      },
      checkForm (data) {
        let content = this
        let flag = true
        for (let i = 0; i < data.length; i++) {
          if (!data[i].name) {
            content.$message.warning(`第${i + 2}行中的节目名称不能为空`)
            flag = false
            break
          } else if (!data[i].duration) {
            flag = false
            content.$message.warning(`第${i + 2}行中的节目时长不能为空`)
            break
          } else if (!data[i].type) {
            flag = false
            content.$message.warning(`第${i + 2}行中的直播方式不能为空`)
            break
          } else if (!data[i].url) {
            flag = false
            content.$message.warning(`第${i + 2}行中的流地址不能为空`)
            break
          }
        }
        return flag
      },
      editProgramsDialog (item) {
        this.currentItem = item
        this.slotId = null
        this.showProgramDialog = true
      },
      isNextLiving (item) {
        return this.nextProgramId === item.id && this.nextProgramId !== this.currentProgramId
      },
      isLiving (item) {
        return this.currentProgramId === item.id
      },
      deleteProgram (item) {
        this.$confirm('确定删除该节目单吗?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          let param = {
            id: item.id,
            programType: this.programType
          }
          this.delProgram(param)
        }).catch(() => {
        })
      },
      checkAudio (newProgram) {
        let audioIds = newProgram.audios.map(item => {
          return item.audioId
        })
        cloudApi.checkAudio({ids: audioIds}, res => {
          newProgram.audios.forEach(item => {
            if (res.includes(item.audioId)) {
              item.isDeleted = true
            }
          })
          this.copyConfirm(newProgram)
        })
      },
      copyConfirm (newProgram) {
        let param = {
          programType: this.programType,
          programs: [newProgram]
        }
        this.addProgram(param).then(() => {
          this.$emit('close')
        })
      },
      copyProgram (item) {
        this.$confirm('确定复制节目吗?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          let newProgram = this.$_deepCopy(item)
          newProgram.id = this.$_createRandomId()
          if (newProgram.cgIds && newProgram.cgIds.length) {
            newProgram.cgIds = newProgram.cgIds.filter(item => {
              return this.cgs && !!this.cgs.find(v => v.id === item)
            })
          }
          if (newProgram.audios && newProgram.audios.length) {
            this.checkAudio(newProgram)
          } else {
            this.copyConfirm(newProgram)
          }
        }).catch(() => {
        })
      },
      addProgramDialog () {
        this.slotId = null
        this.currentItem = null
        this.showProgramDialog = true
      },
      cutPlay (item) {
        this.$confirm('是否切换播出该节目?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          if (this.isLiving(item)) {
            return this.$message.warning('该节目正在直播中')
          }
          this.cutProgram(item.id)
        }).catch(() => {
        })
      },
      slotProgramDialog (item) {
        this.slotId = item.id
        this.currentItem = null
        this.showProgramDialog = true
      },
      getInit () {
        this.getPrograms()
      },
      ...mapMutations({
        setProgramsSn: 'SET_PROGRAMS_SN'
      }),
      ...mapActions(['getPrograms', 'addProgram', `delProgram`, 'modProgram', 'cutProgram'])
    },
    created () {
      this.getInit()
    },
    watch: {
      loopPrograms: {
        handler () {
          this.programList = this.$_deepCopy(this.loopPrograms)
        },
        deep: true
      },
      currentProgramId () {
        this.getPrograms()
        if (this.programList.length) {
          setTimeout(() => {
            let el = document.getElementsByClassName('active-living')[0]
            if (el) {
              el.scrollIntoView({block: 'start'})
            }
          }, 100)
        }
      },
      currentScheduleId: {
        handler (newVal) {
          if (Array.isArray(this.schedules)) {
            const schedule = this.schedules.find(item => item.id === newVal)
            if (schedule) {
              this.currentPgmName = schedule.name
            }
          }
        },
        immediate: true
      }
    }
  }
</script>

<style scoped lang="scss">
  .program-list {
    height: 100%;
    position: relative;
    box-sizing: border-box;
    border: 2px solid #373D4A;
    font-size: 16px;
    .top-header {
      padding-left: 19px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      height: 49px;
      background-color: #373D4A;
      box-sizing: border-box;
      .title {
        font-size: 18px;
        color: #909AB0;
      }
      .download-model {
        margin-right: 10px;
        color: #70798E;
        .el-icon-document:hover {
          color: #fc4f08!important;
        }
      }
    }
    .more-dialog {
      position: absolute;
      width: 100px;
      background: #FFFFFF;
      box-shadow: 0px 3px 8px 0px rgba(0, 0, 0, 0.28);
      border-radius: 4px;
      overflow: hidden;

      .more-li {
        width: 100%;
        height: 32px;
        line-height: 32px;
        padding-left: 19px;
        box-sizing: border-box;
        color: #222222;
        cursor: pointer;
        &:hover {
          color: $color-theme;
          background: #FFEBE3;
        }
      }
    }
    .program-wrapper {
      height: calc(100% - 50px);
      box-sizing: border-box;
    }

    .program-ul {
      height: calc(100% - 46px);
      overflow-y: auto;
      overflow-x: hidden;
      box-sizing: border-box;
      padding-left: 2px;
      .program-li {
        position: relative;
        display: flex;
        justify-content: flex-start;
        margin: 2px 0;
        padding: 6px 0;
        background: #292E37;
        box-sizing: border-box;
        height: 90px;
        &.active-living {
          background-color: #D01616;
          color: #ffffff;
          .my-operate-btn {
            background: rgba(255, 255, 255, .5);
            border: none;
            color: #ffffff;
            &:hover {
              background: $color-theme;
            }
            &:active {
              opacity: .8;
            }
          }
          &:hover {
            background-color: #D01616;
          }
        }
        &.active-next {
          background-color: #17A30C;
          color: #ffffff;
          .my-operate-btn {
            background: rgba(255, 255, 255, .5);
            border: none;
            color: #ffffff;
            &:hover {
              background: $color-theme;
            }
            &:active {
              opacity: .8;
            }
          }
          &:hover {
            background-color: #17A30C;
          }
        }
        &:hover {
          background: #3E4252;
          .drag-icon {
            display: inline-block;
          }
        }
        .status-flag {
          position: absolute;
          left: 0;
          top: 0;
          width: 88px;
          height: 28px;
          line-height: 28px;
          color: #ffffff;
          background: linear-gradient(-45deg,transparent 28px,rgba(255, 255, 255, .3) 0);
        }
        .drag-icon {
          position: absolute;
          display: none;
          padding: 0 10px;
          cursor: pointer;
          left: 0;
          top: calc(50% - 3px);
          .icon {
            flex: 0 0 16px;
            width: 16px;
            height: 6px;
            border-top: 2px solid #A6B2CE;
            border-bottom: 2px solid #A6B2CE;
          }
        }
        .el-icon-video-camera-solid {
          position: absolute;
          display: none;
          padding: 0 10px;
          cursor: pointer;
          left: 0;
          top: calc(50% - 3px);
          font-size: 22px;
          margin: 0 8px;
          color: $color-theme;
        }
      }
    }
    .list-header {
      display: flex;
      justify-content: flex-start;
      align-items: center;
      color: #646D80;
      font-size: 16px;
      box-sizing: border-box;
      height: 46px;
    }
    .program-option {
      display: none;
      justify-content: center;
      align-items: center;
      flex-wrap: wrap;
      flex: 0 0 22%;
      width: 22%;
      white-space: nowrap;
      &.show-option {
        display: flex;
      }
      .my-operate-btn {
        display: inline-block;
        margin-right: 6px;
        width: 56px;
      }
      .my-operate-btn-span {
        background: #4f5867;
        border-color: #4f5867;
        color: #C2D1EE;
        cursor: pointer;
        text-align: center;
        line-height: 28px;
        &:hover {
          background: $color-theme;
          border-color: $color-theme;
          color: #ffffff;
        }
      }
    }
    .program-options {
      display: flex;
      align-items: center;
      justify-content: center;
      flex: 0 0 22%;
      width: 22%;
      white-space: nowrap;
    }
    .program-number {
      display: flex;
      align-items: center;
      text-indent: 40px;
      flex: 0 0 12%;
      width: 12%;
      white-space: nowrap;
    }
    .program-name {
      display: flex;
      align-items: center;
      text-indent: 40px;
      flex: 0 0 46%;
      width: 46%;
      padding-right: 40px;
      box-sizing: border-box;
      .name {
        display: inline-block;
        width: 200px;
        @include elliptical();
      }
    }
    .program-duration {
      display: flex;
      align-items: center;
      flex: 0 0 16%;
      width: 16%;
    }
  }
  .box-content {
    max-height: 600px;
    overflow-y: auto;

    .bgm-info-box {
      display: flex;
      justify-content: flex-start;
      align-items: center;
      color: #B5BBCA;
      margin-bottom: 10px;
    }
    .bgm-info-item {
      font-size: 22px;
      display: inline-block;
      margin-right: 50px;
      width: 300px;
      @include elliptical();
    }
  }
  .program-dialog {
    .live-type-tips {
      color: $color-tips;
    }
  }
  ::v-deep .el-table td.el-table__cell {
    border-bottom: 1px solid #434A58;
  }
  .menu-enter-active{
    transition: opacity .5s;
  }
  .menu-enter{
    opacity: 0;
  }
  .tips {
    position: absolute;
    bottom: 25px;
    color: #8892A7;
  }
  .dropdown-line {
    display: inline-block;
    height: 9px;
    width: 100%;
    border-top: 1px solid #E6E6E6;
  }
</style>
