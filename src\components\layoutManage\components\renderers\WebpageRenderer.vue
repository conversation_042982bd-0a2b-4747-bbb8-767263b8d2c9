<template>
  <div class="webpage-renderer">
    <div v-if="component.url" class="webpage-preview">
      <!-- 网页预览 - 实际项目中可能需要使用 iframe 或其他方案 -->
      <div class="webpage-info">
        <i class="el-icon-link"></i>
        <span>网页内容</span>
        <div class="webpage-url">{{ component.url }}</div>
        <div class="crop-info" v-if="hasCropInfo">
          裁剪区域: {{ cropInfo }}
        </div>
      </div>
    </div>
    <div v-else class="webpage-placeholder">
      <i class="el-icon-link"></i>
      <span>网页</span>
    </div>
  </div>
</template>

<script>
export default {
  name: 'WebpageRenderer',
  props: {
    component: {
      type: Object,
      required: true
    },
    isSelected: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    hasCropInfo() {
      return this.component.crop &&
             (this.component.crop.width !== 1 || this.component.crop.height !== 1)
    },
    cropInfo() {
      if (!this.component.crop) return ''
      const crop = this.component.crop
      return `${Math.round(crop.width * 100)}% × ${Math.round(crop.height * 100)}%`
    }
  },
  methods: {
    openWebpage() {
      if (this.component.url) {
        window.open(this.component.url, '_blank')
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.webpage-renderer {
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.webpage-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: rgba(245, 108, 108, 0.1);
  border: 1px dashed rgba(245, 108, 108, 0.3);
  color: #f56c6c;
  font-size: 12px;

  i {
    font-size: 24px;
    margin-bottom: 4px;
  }
}

.webpage-preview {
  width: 100%;
  height: 100%;
  background: #ffffff;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;

  &:hover {
    background: #f5f5f5;
  }

  .webpage-info {
    text-align: center;
    color: #f56c6c;

    i {
      font-size: 32px;
      margin-bottom: 8px;
      display: block;
    }

    span {
      display: block;
      font-size: 14px;
      margin-bottom: 4px;
      color: #333333;
    }

    .webpage-url {
      font-size: 10px;
      opacity: 0.8;
      max-width: 200px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      margin-bottom: 4px;
      color: #666666;
    }

    .crop-info {
      font-size: 10px;
      opacity: 0.6;
      color: #999999;
    }
  }
}
</style>
