<template>
  <div class="effect-properties">
    <div class="property-section">
      <div class="section-title">效果</div>
      <div class="property-group">
        <!-- 不透明度 -->
        <div v-if="showOpacity" class="opacity-control">
          <label>不透明度</label>
          <div class="opacity-slider">
            <el-slider
              v-model="localOpacity"
              :min="0"
              :max="100"
              :show-tooltip="false"
              @change="handleOpacityChange"
            />
            <div class="opacity-value">{{ localOpacity }}%</div>
            <div class="opacity-input">
              <el-input-number
                v-model="localOpacity"
                :min="0"
                :max="100"
                size="small"
                controls-position="right"
                @change="handleOpacityChange"
              />
            </div>
          </div>
        </div>

        <!-- 高斯模糊开关 -->
        <div v-if="showBlur" class="blur-switch">
          <label>高斯模糊</label>
          <el-switch
            v-model="localGaussianBlur"
            @change="handleBlurToggle"
          />
        </div>

        <!-- 模糊度 -->
        <div v-if="showBlur && localGaussianBlur" class="blur-control">
          <label>模糊度 ({{ localBlurRadius }}px)</label>

          <!-- 快速预设 -->
          <div class="blur-presets">
            <el-button
              v-for="preset in blurPresets"
              :key="preset.value"
              :type="localBlurRadius === preset.value ? 'primary' : 'default'"
              size="mini"
              @click="setBlurPreset(preset.value)"
            >
              {{ preset.label }}
            </el-button>
          </div>

          <div class="blur-slider">
            <div class="blur-input">
              <el-input-number
                v-model="localBlurRadius"
                :min="1"
                :max="50"
                size="small"
                controls-position="right"
                @change="handleBlurRadiusChange"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'EffectProperties',
  props: {
    component: {
      type: Object,
      required: true
    },
    showOpacity: {
      type: Boolean,
      default: true
    },
    showBlur: {
      type: Boolean,
      default: true
    },
    defaultOpacity: {
      type: Number,
      default: 100
    },
    defaultBlurRadius: {
      type: Number,
      default: 10
    }
  },
  data() {
    return {
      localOpacity: 100,
      localGaussianBlur: false,
      localBlurRadius: 10,
      blurPresets: [
        { label: '轻微', value: 3 },
        { label: '中度', value: 10 },
        { label: '重度', value: 20 },
        { label: '极度', value: 35 }
      ]
    }
  },
  watch: {
    component: {
      handler() {
        this.syncLocalValues()
      },
      immediate: true
    }
  },
  methods: {
    syncLocalValues() {
      this.localOpacity = this.component.opacity || this.defaultOpacity
      this.localGaussianBlur = !!this.component.gaussianBlur
      this.localBlurRadius = this.component.gaussianBlurRadius || this.defaultBlurRadius
    },
    handleOpacityChange() {
      this.$emit('update', { opacity: this.localOpacity })
    },
        handleBlurToggle() {
      const blurValue = this.localGaussianBlur ? 1 : 0
      const radiusValue = this.localGaussianBlur ? this.localBlurRadius : 0

      this.$emit('update', {
        gaussianBlur: blurValue,
        gaussianBlurRadius: radiusValue
      })
    },
    handleBlurRadiusChange() {
      this.$emit('update', { gaussianBlurRadius: this.localBlurRadius })
    },
    setBlurPreset(value) {
      this.localBlurRadius = value
      this.handleBlurRadiusChange()
    }
  }
}
</script>

<style lang="scss" scoped>
.effect-properties {
  .property-section {
    margin-bottom: 20px;

    .section-title {
      font-size: 14px;
      font-weight: 500;
      margin-bottom: 12px;
      padding: 0 16px;
    }
  }

  .property-group {
    margin-bottom: 16px;
    padding: 0 16px;
  }

  .opacity-control, .blur-control {
    margin-bottom: 16px;

    label {
      display: block;
      font-size: 12px;
      margin-bottom: 8px;
    }

    .opacity-slider, .blur-slider {
      display: flex;
      align-items: center;
      gap: 12px;

      :deep(.el-slider) {
        flex: 1;

        .el-slider__runway {
          background: #4a4a4a;
        }

        .el-slider__bar {
          background: #ff6b35;
        }

        .el-slider__button {
          border-color: #ff6b35;
        }
      }

      .opacity-value {
        font-size: 12px;
        color: #ffffff;
        min-width: 32px;
      }

      .opacity-input, .blur-input {
        width: 80px;

        :deep(.el-input-number) {
          width: 100%;

          .el-input__inner {
            background: #4a4a4a;
            border-color: #606060;
            color: #ffffff;
            height: 28px;
            line-height: 28px;
          }
        }
      }
    }
  }

  .blur-switch {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 12px;

    label {
      font-size: 12px;
    }

    :deep(.el-switch) {
      .el-switch__core {

        &:after {
        }
      }

      &.is-checked .el-switch__core {
        background: #ff6b35;
        border-color: #ff6b35;
      }
    }
  }

  .blur-presets {
    display: flex;
    gap: 6px;
    margin-bottom: 12px;
    flex-wrap: wrap;

    :deep(.el-button) {
      height: 24px;
      padding: 0 8px;
      font-size: 11px;

      &.el-button--default {
        background: #4a4a4a;
        border-color: #606060;
        color: #cccccc;

        &:hover {
          background: #5a5a5a;
          border-color: #ff6b35;
          color: #ffffff;
        }
      }

      &.el-button--primary {
        background: #ff6b35;
        border-color: #ff6b35;

        &:hover {
          background: #ff7a47;
          border-color: #ff7a47;
        }
      }
    }
  }
}
</style>
