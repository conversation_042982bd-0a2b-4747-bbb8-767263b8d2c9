@font-face {
  font-family: 'iconfount';
  src: url('../font/iconfount.woff') format('woff');
  font-weight: normal;
  font-style: normal;
}

[class^="icon-"]:before, [class*=" icon-"]:before {
  font-family: "iconfount" !important;
  font-style: normal;
  font-weight: normal;
  speak: none;

  display: inline-block;
  text-decoration: inherit;
  width: 1em;
  text-align: center;
  /* margin-right: .2em; */
  /* opacity: .8; */

  /* For safety - reset parent styles, that can break glyph codes*/
  font-variant: normal;
  text-transform: none;

  /* fix buttons height, for twitter bootstrap */
  line-height: 1em;

  /* Animation center compensation - margins should be symmetric */
  /* remove if not needed */
  /* margin-left: .2em; */

  /* you can be more comfortable with increased icons size */
  /* font-size: 120%; */

  /* Font smoothing. That was taken from TWBS */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;

  /* Uncomment for 3D effect */
  /* text-shadow: 1px 1px 1px rgba(127, 127, 127, 0.3); */
}

.icon-fount-subtitle:before { content: '\e800'; } /* '' */
.icon-fount-no-loop:before { content: '\e801'; } /* '' */
.icon-fount-loop:before { content: '\e802'; } /* '' */
.icon-fount-auto-play:before { content: '\e803'; } /* '' */
.icon-fount-cg-template:before { content: '\e804'; } /* '' */
.icon-fount-cd-add:before { content: '\e805'; } /* '' */
.icon-fount-cg-clear:before { content: '\e806'; } /* '' */
.icon-fount-save-config:before { content: '\e807'; } /* '' */
.icon-fount-mixing-console:before { content: '\e808'; } /* '' */
.icon-fount-setting:before { content: '\e809'; } /* '' */
.icon-fount-shortcut-key:before { content: '\e80a'; } /* '' */
.icon-fount-full-screen:before { content: '\e80b'; } /* '' */
.icon-fount-live-video:before { content: '\e80c'; } /* '' */
.icon-fount-cut-channel:before { content: '\e80d'; } /* '' */
.icon-fount-special-effects:before { content: '\e80e'; } /* '' */
.icon-fount-urgent-cut:before { content: '\e80f'; } /* '' */
.icon-fount-cg:before { content: '\e810'; } /* '' */
.icon-fount-cg-add:before { content: '\e811'; } /* '' */
.icon-fount-switch-channel:before { content: '\e812'; } /* '' */
.icon-fount-gasket:before { content: '\e813'; } /* '' */
.icon-fount-output-setting:before { content: '\e814'; } /* '' */
.icon-fount-img:before { content: '\e815'; } /* '' */
.icon-fount-text:before { content: '\e816'; } /* '' */
