<template>
  <div class="nav-content-box">
    <div style="width:325px;box-sizing: border-box; margin-bottom: 20px" class="clearfix">
      <div class="toning-box">
        <div class="toning-name">色相</div>
        <el-slider class="toning-slider hue-slider"
                   v-model="tone.hue" :min="-180" :max="180" :step="1"
                   show-input :show-input-controls="false" input-size="small" @input="toneChange()"></el-slider>
      </div>
      <div class="toning-box">
        <div class="toning-name">对比度</div>
        <el-slider class="toning-slider contrast-slider"
                   v-model="tone.contrast" :min="-100" :max="100" :step="1"
                   show-input :show-input-controls="false" @input="toneChange()"></el-slider>
      </div>
      <div class="toning-box">
        <div class="toning-name">亮度</div>
        <el-slider class="toning-slider brightness-slider"
                   v-model="tone.brightness" :min="-100" :max="100" :step="1"
                   show-input :show-input-controls="false" @input="toneChange()"></el-slider>
      </div>
      <div class="toning-box">
        <div class="toning-name">饱和度</div>
        <el-slider class="toning-slider saturate-slider"
                   v-model="tone.saturate" :min="-100" :max="100" :step="1"
                   show-input :show-input-controls="false" @input="toneChange()"></el-slider>
      </div>
      <el-button class="el-normal-btn" style="float: right" size="small" @click="resetTone">重置</el-button>
    </div>
    <div class="btn-area">
      <el-button type="primary" size="medium" @click="setTone()">应用</el-button>
<!--      <el-button class="el-normal-btn" size="medium" @click="setTone()">应用</el-button>-->
      <el-button type="info" plain size="medium" @click="$emit('close')">取消</el-button>
    </div>
  </div>
</template>

<script>
  export default {
    name: '',
    props: {
      toneData: {
        type: Object,
        default: {}
      }
    },
    components: {},
    computed: {},
    data () {
      return {
        tone: {
          brightness: Number(((this.toneData.brightness - 1) * 100).toFixed(0)),
          saturate: this.toneData.saturate * 100,
          contrast: this.toneData.contrast * 100,
          hue: this.toneData.hue
        }
      }
    },
    methods: {
      setTone () {
        this.$emit('set', {type: 'toning'})
      },
      toneChange (allApply) {
        let tone = {
          brightness: (this.tone.brightness / 100) + 1,
          saturate: this.tone.saturate / 100,
          contrast: this.tone.contrast / 100,
          hue: this.tone.hue
        }
        this.$emit('change', tone)
      },
      resetTone () {
        this.tone.brightness = 0
        this.tone.saturate = 0
        this.tone.temperature = 0
        this.tone.contrast = 0
        this.tone.hue = 0
        let tone = {
          brightness: (this.tone.brightness / 100) + 1,
          saturate: this.tone.saturate / 100,
          contrast: this.tone.contrast / 100,
          hue: this.tone.hue
        }
        this.$emit('change', tone)
      }
    },
    mounted () {
    },
    created () {
    },
    watch: {
      toneData () {
        this.tone.brightness = Number(((this.toneData.brightness - 1) * 100).toFixed(0))
        this.tone.saturate = this.toneData.saturate * 100
        this.tone.temperature = this.toneData.temperature * 100
        this.tone.contrast = this.toneData.contrast * 100
        this.tone.hue = this.toneData.hue
      }
    }
  }
</script>
<style lang="scss" type="text/scss" scoped>
  .nav-content-box {
    width: 100%;
    overflow-y: auto;
    padding-top: 10px;
  }
  .toning-box ::v-deep{
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    .el-slider__input {
      width: 46px;
    }
    .el-slider__bar {
      background-color: transparent;
    }
    .el-slider__button {
      border: 2px solid #DDE4F4;
    }
    .toning-name {
      width: 60px;
      font-size: 14px;
      font-family: "Microsoft YaHei";
      font-weight: 400;
      color: #8C97B1;
      line-height: 20px;
    }
    .toning-slider {
      flex-shrink: 1;
      flex-grow: 1;
    }
    .hue-slider{
      .el-slider__runway {
        background-image: linear-gradient(90deg,red,#ff2b00,#f50,#ff8000,#fa0,#ffd500,#ff0,#d4ff00,#af0,#80ff00,#5f0,#2bff00,#0f0,#00ff2b,#0f5,#00ff80,#0fa,#00ffd5,#0ff,#00d4ff,#0af,#007fff,#05f,#002bff,#00f,#2a00ff,#50f,#7f00ff,#a0f,#d400ff,#f0f,#ff00d4,#f0a,#ff0080,#f05,#ff002b,red);
      }
    }
    .contrast-slider, .brightness-slider{
      .el-slider__runway {
        background-image: linear-gradient(90deg,#000,#fff);
      }
    }
    .saturate-slider{
      .el-slider__runway {
        background-image: linear-gradient(90deg, #616161, #50e399);
      }
    }
    .temperature-slider{
      .el-slider__runway {
        background-image: linear-gradient(90deg, #d7ddef, #cd0808);
      }
    }
    .el-slider__runway.show-input {
      width: 74%;
    }
    .el-input-number.is-without-controls .el-input__inner {
      padding-left: 5px;
      padding-right: 5px;
    }
  }
  .btn-area {
    margin-top: 30px;
    position: absolute;
    right: 0;
    left: 0;
    bottom: 0;
    padding: 8px 0;
    box-sizing: border-box;
    display: flex;
    justify-content: flex-end;
    padding-right: 20px;
    z-index: 1002;
  }
  .el-normal-btn {
    background-color: #535D72;
    color: #CADAFF;
    border: 1px solid #535D72;

    /*&:focus {*/
    /*  background-color: #404654;*/
    /*  color: #C2CADD;*/
    /*  border: 1px solid #404654;*/
    /*}*/
  }
</style>
