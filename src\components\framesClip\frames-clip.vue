<template>
  <el-dialog class="frames-clip" title="画面裁剪" :visible.sync="showChannelClip" width="800px" top="60px" :close-on-click-modal="false"
             :close-on-press-escape="false" @close="$emit('close')">
    <div class="clipping-content-wrapper" :style="{width: contentWidth + 'px'}" v-loading="videoLoading" element-loading-background="rgba(0, 0, 0, 0)" element-loading-text="加载中...." element-loading-spinner="loading-bg">
      <div class="clipping-content" v-if="showClipArea" ref="clippingContent">
        <img class="img-cover" ref="clippingImg" :src="currentChannelBg" alt="">
      </div>
    </div>
    <div class="footer-option">
      <div class="left-btn">
        <el-button type="info" size="small" icon="el-icon-refresh-left" style="margin-right: 20px;" @click="resetClip">重置</el-button>
<!--        <el-button style="margin-right: 20px;" type="info" size="small" icon="el-icon-refresh" @click="refreshCurrentBg">加载画面</el-button>-->
        <el-radio-group v-model="radio" @change="switchRadio">
          <el-radio :label="1">
            <span class="label" style="margin-right: 10px;">常用比例</span>
            <el-select style="width: 90px;" v-model="activeProportion" placeholder="请选择" size="mini" @change="switchAspectRatio">
              <el-option v-for="item in proportion" :key="item.value" :label="item.value" :value="item.scale"></el-option>
            </el-select>
          </el-radio>
          <el-radio :label="2">
            <span class="label">自由比例</span>
          </el-radio>
        </el-radio-group>
      </div>
      <el-button type="primary" icon="el-icon-crop" size="small" @click="saveClip">裁减并应用</el-button>
    </div>
  </el-dialog>
</template>

<script>
  import {mapActions, mapGetters} from 'vuex'
  import Cropper from 'cropperjs'
  import 'cropperjs/src/css/cropper.scss'
  import ChannelVideo from 'common/js/channel-preview'
  import webrtcMgr from 'common/js/webrtc-manager'
  import cloudApi from 'api/cloud'

  const CROPBOXWIDTH = 640
  const CROPBOXHEIGHT = 360
  export default {
    name: 'frames-clip',
    props: {
      programType: {
        type: String,
        default: ''
      },
      scheduleId: {
        type: Number,
        default: 0
      },
      manageId: {
        type: Number,
        default: 0
      },
      currentProgramItem: {
        type: Object,
        default: () => {}
      }
    },
    computed: {
      ...mapGetters([
        'liveRatio',
        'liveWidth',
        'liveHeight',
        'audio',
        'defaultPic',
        'cgs'
      ])
    },
    data () {
      return {
        video: null,
        videoLoading: false,
        masterCrop: {},
        contentWidth: 0,
        initCropperLock: false,
        showClipArea: true,
        paint: null,
        proportion: [
          {value: '16:9', scale: 16 / 9},
          {value: '9:16', scale: 9 / 16},
          {value: '1:1', scale: 1},
          {value: '4:3', scale: 4 / 3},
          {value: '4:9', scale: 4 / 9}
        ],
        cropper: null,
        showChannelClip: true,
        activeProportion: 16 / 9,
        currentProportion: 0,
        radio: 2,
        currentChannelBg: '',
        imgWidth: 0,
        imgHeight: 0,
        cropperFace: {
          width: 0,
          height: 0,
          dx: 0,
          dy: 0
        }
      }
    },
    methods: {
      saveClip () {
        let editProgram = this.currentProgramItem
        editProgram.video.channels.forEach(item => {
          if (item.idx === 0) {
            item.crop = {
              dx: this.cropperFace.dx,
              dy: this.cropperFace.dy,
              width: this.cropperFace.width,
              height: this.cropperFace.height
            }
          }
        })
        if (this.manageId > 0) {
          let param = {
            id: this.manageId,
            program: JSON.stringify(editProgram)
          }
          cloudApi.setProgram(param, () => {
            this.$bus.$emit('refreshProgramManage')
            this.$emit('close')
          })
        } else if (this.scheduleId > 0) {
          let param = {
            id: this.scheduleId,
            program: JSON.stringify(editProgram)
          }
          cloudApi.uploadProgramArrangementById(param, () => {
            this.$bus.$emit('refreshProgramManage')
            this.$emit('close')
          })
        } else {
          let param = {
            programType: this.programType,
            program: editProgram
          }
          this.modProgram(param).then(() => {
            this.$emit('close')
          })
        }
      },
      resetClip () {
        let data
        let scale = Math.min(CROPBOXWIDTH / this.imgWidth, CROPBOXHEIGHT / this.imgHeight)
        data = {
          left: 0,
          top: 0,
          width: this.imgWidth * scale,
          height: this.imgHeight * scale
        }
        this.cropper.setCropBoxData(data)
      },
      switchRadio (value) {
        if (value === 1) {
          this.currentProportion = this.activeProportion
        } else {
          this.currentProportion = NaN
        }
        this.cropper.setAspectRatio(this.currentProportion)
      },
      switchAspectRatio (value) {
        if (this.radio === 2) {
          return
        }
        this.activeProportion = value
        this.cropper.setAspectRatio(value)
      },
      refreshCurrentBg () {
        this.cropper.destroy()
        this.getCurrentBg()
      },
      initCropper () {
        let that = this
        let img = this.$refs.clippingImg
        this.cropper = new Cropper(img, {
          width: CROPBOXWIDTH,
          height: CROPBOXHEIGHT,
          img: this.currentChannelBg,
          viewMode: 1,
          dragMode: 'move',
          movable: false,
          guides: true,
          scalable: false,
          zoomable: false,
          crop (event) {
            that.cropperFace.width = Number((event.detail.width / that.imgWidth).toFixed(2))
            that.cropperFace.height = Number((event.detail.height / that.imgHeight).toFixed(2))
            that.cropperFace.dx = Number((event.detail.x / that.imgWidth).toFixed(2))
            that.cropperFace.dy = Number((event.detail.y / that.imgHeight).toFixed(2))
          }
        })
      },
      getInputCroppedInfo () {
        let scale = Math.min(CROPBOXWIDTH / this.imgWidth, CROPBOXHEIGHT / this.imgHeight)
        let data
        if (this.masterCrop.height > 0) {
          data = {
            left: parseInt(this.masterCrop.dx * this.imgWidth * scale),
            top: parseInt(this.masterCrop.dy * this.imgHeight * scale),
            width: parseInt(this.masterCrop.width * this.imgWidth * scale),
            height: parseInt(this.masterCrop.height * this.imgHeight * scale)
          }
        } else {
          data = {
            left: 0,
            top: 0,
            width: this.imgWidth * scale,
            height: this.imgHeight * scale
          }
        }
        this.cropper.setCropBoxData(data)
      },
      createVideo () {
        let videoDom = document.createElement('video')
        videoDom.setAttribute('id', 'preview')
        videoDom.setAttribute('autoplay', true)
        videoDom.setAttribute('muted', true)
        videoDom.className = 'videoBg'
        this.$refs.clippingContent.appendChild(videoDom)
      },
      getCurrentBg () {
        if (this.initCropperLock) {
          return
        }
        this.initCropperLock = true
        let canvas = document.createElement('canvas')
        let video = document.getElementById('preview')
        canvas.width = this.imgWidth
        canvas.height = this.imgHeight
        canvas.getContext('2d')
            .drawImage(video, 0, 0, canvas.width, canvas.height)
        let img = this.$refs.clippingImg
        img.src = canvas.toDataURL('image/png')
        setTimeout(() => {
          this.initCropper()
          setTimeout(() => {
            this.getInputCroppedInfo()
            this.initCropperLock = false
            document.getElementsByClassName('cropper-crop-box')[0].style.zIndex = 2
            this.videoLoading = false
          }, 50)
        }, 50)
      },
      programReset (program) {
        let programCopy = program
        programCopy.startTime = null
        let master = programCopy.video.channels.find(v => v.idx === 0)
        master.display = {dx: 0, dy: 0, width: 0, height: 0}
        master.crop = {dx: 0, dy: 0, width: 1, height: 1}
        programCopy.video.channels = [master]
        programCopy.audios = []
        programCopy.video.useBackground = false
        programCopy.video.usePip = false
        return programCopy
      },
      ...mapActions(['modProgram'])
    },
    mounted () {
      this.videoLoading = true
      let program = this.programReset(this.$_deepCopy(this.currentProgramItem))
      let audio = this.$_deepCopy(this.audio)
      audio.audios = []
      setTimeout(() => {
        this.createVideo()
        this.video = new ChannelVideo('preview', program, audio, this.defaultPic, null, res => {
          this.imgWidth = res.width
          this.imgHeight = res.height
          let scale = Math.min(CROPBOXWIDTH / this.imgWidth, CROPBOXHEIGHT / this.imgHeight)
          document.getElementById('preview').setAttribute('width', this.imgWidth * scale)
          document.getElementById('preview').setAttribute('height', this.imgHeight * scale)
          this.getCurrentBg()
        }, () => {
          this.imgWidth = this.liveWidth
          this.imgHeight = this.liveHeight
          let scale = Math.min(CROPBOXWIDTH / this.imgWidth, CROPBOXHEIGHT / this.imgHeight)
          document.getElementById('preview').setAttribute('width', this.imgWidth * scale)
          document.getElementById('preview').setAttribute('height', this.imgHeight * scale)
          document.getElementById('preview').style.backgroundColor = 'black'
          this.getCurrentBg()
          this.videoLoading = false
          this.$message.warning('画面加载超时，请检查流地址或刷新后重试')
        })
      }, 100)
      this.$nextTick(() => {
        this.contentWidth = 640
      })
    },
    beforeDestroy () {
      if (this.cropper && this.cropper.destroy) {
        this.cropper.destroy()
      }
      webrtcMgr.destroyAllPreview()
    },
    created () {
      this.masterCrop = this.currentProgramItem.video.channels.find(item => item.idx === 0).crop || {}
    }
  }
</script>

<style scoped lang="scss">
  .frames-clip {
    .clipping-content-wrapper {
      position: relative;
      height: 360px;
      margin: 0 auto;

      .clipping-content {
        width: 100%;
        height: 100%;
        background-color: #22252D;
        overflow: hidden;

        .img-cover {
          overflow: hidden;
        }

        .clipping-bg {
          position: absolute;
          z-index: -2;
          left: 0;
          top: 0;
          width: 100%;
          height: 100%;
        }
        .video {
          position: absolute;
        }
      }
    }
    .footer-option {
      display: flex;
      justify-content: space-between;
      margin-top: 30px;
      .label {
        color: $color-text;
      }
    }
  }
  /deep/ .el-loading-mask {
    display: flex;
    justify-content: center;
  }
  /deep/ .el-loading-spinner {
    padding: 10px 0;
    width: 80px;
    background: rgba(0, 0, 0, 0.8);
    border-radius: 6px;
  }
  /deep/ .loading-bg {
    display: inline-block;
    width: 20px;
    height: 20px;
    background: url("../../common/img/loading-icon.gif") no-repeat center;
    background-size: 20px 20px;
  }
  /deep/ .el-loading-spinner .el-loading-text {
    color: white;
  }
</style>
