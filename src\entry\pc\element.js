import Vue from 'vue'
import {
  message
} from '@/common/js/resetMessage'

import {
  /* eslint-disable no-unused-vars */
  Button,
  ButtonGroup,
  Tabs,
  TabPane,
  Divider,
  Input,
  InputNumber,
  Form,
  FormItem,
  Loading,
  Message,
  MessageBox,
  Dialog,
  Table,
  TableColumn,
  Radio,
  RadioGroup,
  Switch,
  Select,
  Image,
  Option,
  Upload,
  Slider,
  Progress,
  Pagination,
  Tooltip,
  ColorPicker,
  Drawer,
  Popover,
  DatePicker,
  Checkbox,
  CheckboxGroup,
  Dropdown,
  DropdownMenu,
  DropdownItem,
  TimePicker,
  Autocomplete,
  Scrollbar,
  Col,
  Row,
  Cascader
} from 'element-ui'

Vue.use(Button)
Vue.use(ButtonGroup)
Vue.use(Tabs)
Vue.use(TabPane)
Vue.use(Divider)
Vue.use(Input)
Vue.use(InputNumber)
Vue.use(Form)
Vue.use(FormItem)
Vue.use(Loading)
Vue.use(Table)
Vue.use(TableColumn)
Vue.use(Dialog)
Vue.use(Radio)
Vue.use(RadioGroup)
Vue.use(Switch)
Vue.use(Select)
Vue.use(Image)
Vue.use(Option)
Vue.use(Upload)
Vue.use(Slider)
Vue.use(Progress)
Vue.use(Tooltip)
Vue.use(Pagination)
Vue.use(ColorPicker)
Vue.use(Drawer)
Vue.use(Popover)
Vue.use(DatePicker)
Vue.use(Checkbox)
Vue.use(CheckboxGroup)
Vue.use(Dropdown)
Vue.use(DropdownMenu)
Vue.use(DropdownItem)
Vue.use(TimePicker)
Vue.use(Autocomplete)
Vue.use(Scrollbar)
Vue.use(Row)
Vue.use(Col)
Vue.use(Cascader)
// Vue.prototype.$message = Message
Vue.prototype.$message = message
Vue.prototype.$confirm = MessageBox.confirm
Vue.prototype.$prompt = MessageBox.prompt
