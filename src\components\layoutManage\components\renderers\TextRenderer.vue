<template>
  <div class="text-renderer">
    <div
      class="text-content"
      :style="textStyle"
      :contenteditable="isEditing"
      @blur="handleTextBlur"
      @keydown.enter.prevent="handleEnterKey"
    >
      {{ displayText }}
    </div>
  </div>
</template>

<script>
export default {
  name: 'TextRenderer',
  props: {
    component: {
      type: Object,
      required: true
    },
    isSelected: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      isEditing: false
    }
  },
  computed: {
    displayText() {
      return this.component.text || '文字内容'
    },
    textStyle() {
      const style = {
        width: '100%',
        height: '100%',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        fontSize: `${this.component.size || 26}px`,
        fontFamily: this.component.font || 'Microsoft YaHei',
        color: this.component.color || '#ffffff',
        textAlign: 'center',
        wordBreak: 'break-all',
        overflow: 'hidden',
        background: 'transparent',
        border: 'none',
        outline: 'none',
        resize: 'none'
      }

      if (this.component.textShadow) {
        const shadow = this.component.textShadow
        style.textShadow = `${shadow.offsetX || 0}px ${shadow.offsetY || 0}px ${shadow.blur || 0}px ${shadow.color || '#000000'}`
      }

      // 如果有描边效果
      if (this.component.stroke && this.component.strokeWidth) {
        style.webkitTextStroke = `${this.component.strokeWidth}px ${this.component.strokeColor || '#000000'}`
      }

      return style
    }
  },
  methods: {
    startEditing() {
      if (this.isSelected) {
        this.isEditing = true
        this.$nextTick(() => {
          const textElement = this.$el.querySelector('.text-content')
          if (textElement) {
            textElement.focus()
            // 选中所有文本
            const range = document.createRange()
            range.selectNodeContents(textElement)
            const selection = window.getSelection()
            selection.removeAllRanges()
            selection.addRange(range)
          }
        })
      }
    },
    handleTextBlur(event) {
      if (this.isEditing) {
        const newText = event.target.textContent
        this.$emit('update-text', newText)
        this.isEditing = false
      }
    },
    handleEnterKey(event) {
      // 阻止换行，结束编辑
      event.target.blur()
    }
  },
  watch: {
    isSelected(newVal) {
      if (!newVal) {
        this.isEditing = false
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.text-renderer {
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.text-content {
  cursor: text;

  &[contenteditable="true"] {
    cursor: text;
    background: rgba(255, 255, 255, 0.1);
  }
}
</style>
