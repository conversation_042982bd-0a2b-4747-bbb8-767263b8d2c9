<template>
  <div class="transition-effects">
    <el-select v-model="activeValue" placeholder="请选择转场效果" @change="changeEffect">
      <el-option v-for="item in selectList" :key="item.tag"
        :label="item.name" :value="item.tag"></el-option>
    </el-select>
    <div style="text-align: left;margin: 20px 0;position: relative">
      <div class="boundary"></div>
      <span class="effects-type-name">基础转场</span>
    </div>
    <ul class="effects-list-ul clearfix">
      <li class="drag-item" v-for="item in basisTransitionList" :key="item.tag"
        :data-name="item.name" :data-tag="item.tag" data-type="cutTo" :title="item.name"
        :data-img="item.bg">
        <div class="effect-bg" :style="{'background-image': 'url(' + item.bg + ')'}">
          <img :src="supportHttps(item.gif)" alt="">
        </div>
        <div class="effect-name">{{item.name}}</div>
      </li>
    </ul>
    <div style="text-align: left; margin-bottom: 20px;position: relative">
      <div class="boundary"></div>
      <span class="effects-type-name">运镜转场</span>
    </div>
    <ul class="effects-list-ul clearfix">
      <li class="drag-item" v-for="item in mirrorTransitionList" :key="item.tag"
        :data-name="item.name" :data-tag="item.tag" data-type="cutTo" :title="item.name"
        :data-img="item.bg">
        <div class="effect-bg" :style="{'background-image': 'url(' + item.bg + ')'}">
          <img :src="supportHttps(item.gif)" alt="">
        </div>
        <div class="effect-name">{{item.name}}</div>
      </li>

    </ul>
  </div>
</template>

<script>
const imgPrefix = 'https://img.quklive.com/ui/vedit'

export default {
  components: {},
  props: {},
  computed: {
    selectList() {
      return [{ name: '无', tag: '' }].concat(this.basisTransitionList, this.mirrorTransitionList)
    }
  },
  data() {
    return {
      activeValue: '',
      basisTransitionList: [
        {
          tag: '1',
          bg: imgPrefix + '/img/WipeRight.png',
          dataImg: imgPrefix + '/img/WipeRight.png',
          gif: imgPrefix + '/gif/WipeRight.gif',
          name: '向右擦除'
        },
        {
          tag: '2',
          bg: imgPrefix + '/img/WipeLeft.png',
          dataImg: imgPrefix + '/img/WipeLeft.png',
          gif: imgPrefix + '/gif/WipeLeft.gif',
          name: '向左擦除'
        },
        {
          tag: '3',
          bg: imgPrefix + '/img/WipeDown.png',
          dataImg: imgPrefix + '/img/WipeDown.png',
          gif: imgPrefix + '/gif/WipeDown.gif',
          name: '向下擦除'
        },
        {
          tag: '4',
          bg: imgPrefix + '/img/WipeUp.png',
          dataImg: imgPrefix + '/img/WipeUp.png',
          gif: imgPrefix + '/gif/WipeUp.gif',
          name: '向上擦除'
        },
        {
          tag: 'Fade',
          bg: imgPrefix + '/img/Fade.png',
          dataImg: imgPrefix + '/img/Fade.png',
          gif: imgPrefix + '/gif/Fade.gif',
          name: '淡入淡出'
        }
      ],
      mirrorTransitionList: [
        {
          tag: 'CircleOpen',
          bg: imgPrefix + '/img/CircleOpen.png',
          dataImg: imgPrefix + '/img/CircleOpen.png',
          gif: imgPrefix + '/gif/CircleOpen.gif',
          name: '圆形开场'
        },
        {
          tag: 'CircleCrop',
          bg: imgPrefix + '/img/CircleCrop.png',
          dataImg: imgPrefix + '/img/CircleCrop.png',
          gif: imgPrefix + '/gif/CircleCrop.gif',
          name: '圆形划像'
        },
        {
          tag: 'Directional',
          bg: imgPrefix + '/img/Directional.png',
          dataImg: imgPrefix + '/img/Directional.png',
          gif: imgPrefix + '/gif/Directional.gif',
          name: '下滑入场'
        },
        {
          tag: 'FadeColor',
          bg: imgPrefix + '/img/FadeColor.png',
          dataImg: imgPrefix + '/img/FadeColor.png',
          gif: imgPrefix + '/gif/FadeColor.gif',
          name: '叠黑'
        },
        {
          tag: 'Flyeye',
          bg: imgPrefix + '/img/Flyeye.png',
          dataImg: imgPrefix + '/img/Flyeye.png',
          gif: imgPrefix + '/gif/Flyeye.gif',
          name: '蝇眼'
        },
        {
          tag: 'Pixelize',
          bg: imgPrefix + '/img/Pixelize.png',
          dataImg: imgPrefix + '/img/Pixelize.png',
          gif: imgPrefix + '/gif/Pixelize.gif',
          name: '像素化'
        },
        {
          tag: 'PolkaDotsCurtain',
          bg: imgPrefix + '/img/PolkaDotsCurtain.png',
          dataImg: imgPrefix + '/img/PolkaDotsCurtain.png',
          gif: imgPrefix + '/gif/PolkaDotsCurtain.gif',
          name: '弧形扩散'
        },
        {
          tag: 'SimpleZoom',
          bg: imgPrefix + '/img/SimpleZoom.png',
          dataImg: imgPrefix + '/img/SimpleZoom.png',
          gif: imgPrefix + '/gif/SimpleZoom.gif',
          name: '快速拉大'
        },
        {
          tag: 'WaterDrop',
          bg: imgPrefix + '/img/WaterDrop.png',
          dataImg: imgPrefix + '/img/WaterDrop.png',
          gif: imgPrefix + '/gif/WaterDrop.gif',
          name: '水波渐变'
        },
        {
          tag: 'ButterflyWaveScrawler',
          bg: imgPrefix + '/img/ButterflyWaveScrawler.png',
          dataImg: imgPrefix + '/img/ButterflyWaveScrawler.png',
          gif: imgPrefix + '/gif/ButterflyWaveScrawler.gif',
          name: '晃动'
        },
        {
          tag: 'Circle',
          bg: imgPrefix + '/img/Circle.png',
          dataImg: imgPrefix + '/img/Circle.png',
          gif: imgPrefix + '/gif/Circle.gif',
          name: '圆形转场'
        },
        {
          tag: 'CrossWarp',
          bg: imgPrefix + '/img/CrossWarp.png',
          dataImg: imgPrefix + '/img/CrossWarp.png',
          gif: imgPrefix + '/gif/CrossWarp.gif',
          name: '横向翘曲'
        },
        {
          tag: 'DoomScreenTransition',
          bg: imgPrefix + '/img/DoomScreenTransition.png',
          dataImg: imgPrefix + '/img/DoomScreenTransition.png',
          gif: imgPrefix + '/gif/DoomScreenTransition.gif',
          name: '幕布'
        },
        {
          tag: 'Doorway',
          bg: imgPrefix + '/img/Doorway.png',
          dataImg: imgPrefix + '/img/Doorway.png',
          gif: imgPrefix + '/gif/Doorway.gif',
          name: '门廊'
        },
        {
          tag: 'Dreamy',
          bg: imgPrefix + '/img/Dreamy.png',
          dataImg: imgPrefix + '/img/Dreamy.png',
          gif: imgPrefix + '/gif/Dreamy.gif',
          name: '波浪'
        },
        {
          tag: 'DreamyZoom',
          bg: imgPrefix + '/img/DreamyZoom.png',
          dataImg: imgPrefix + '/img/DreamyZoom.png',
          gif: imgPrefix + '/gif/DreamyZoom.gif',
          name: '水平聚拢'
        },
        {
          tag: 'FilmBurn',
          bg: imgPrefix + '/img/FilmBurn.png',
          dataImg: imgPrefix + '/img/FilmBurn.png',
          gif: imgPrefix + '/gif/FilmBurn.gif',
          name: '火烧云'
        },
        {
          tag: 'GlitchMemories',
          bg: imgPrefix + '/img/GlitchMemories.png',
          dataImg: imgPrefix + '/img/GlitchMemories.png',
          gif: imgPrefix + '/gif/GlitchMemories.gif',
          name: '抖动'
        },
        {
          tag: 'InvertedPageCurl',
          bg: imgPrefix + '/img/InvertedPageCurl.png',
          dataImg: imgPrefix + '/img/InvertedPageCurl.png',
          gif: imgPrefix + '/gif/InvertedPageCurl.gif',
          name: '翻页'
        },
        {
          tag: 'Kaleidoscope',
          bg: imgPrefix + '/img/Kaleidoscope.png',
          dataImg: imgPrefix + '/img/Kaleidoscope.png',
          gif: imgPrefix + '/gif/Kaleidoscope.gif',
          name: '万花筒'
        },
        {
          tag: 'LeftRight',
          bg: imgPrefix + '/img/LeftRight.png',
          dataImg: imgPrefix + '/img/LeftRight.png',
          gif: imgPrefix + '/gif/LeftRight.gif',
          name: '左右切换'
        },
        {
          tag: 'RotateScaleFade',
          bg: imgPrefix + '/img/RotateScaleFade.png',
          dataImg: imgPrefix + '/img/RotateScaleFade.png',
          gif: imgPrefix + '/gif/RotateScaleFade.gif',
          name: '上下收放'
        },
        {
          tag: 'Squeeze',
          bg: imgPrefix + '/img/Squeeze.png',
          dataImg: imgPrefix + '/img/Squeeze.png',
          gif: imgPrefix + '/gif/Squeeze.gif',
          name: '上下聚拢'
        },
        {
          tag: 'StereoViewer',
          bg: imgPrefix + '/img/StereoViewer.png',
          dataImg: imgPrefix + '/img/StereoViewer.png',
          gif: imgPrefix + '/gif/StereoViewer.gif',
          name: '立像切换'
        },
        {
          tag: 'Swap',
          bg: imgPrefix + '/img/Swap.png',
          dataImg: imgPrefix + '/img/Swap.png',
          gif: imgPrefix + '/gif/Swap.gif',
          name: '放大切换'
        },
        {
          tag: 'Swirl',
          bg: imgPrefix + '/img/Swirl.png',
          dataImg: imgPrefix + '/img/Swirl.png',
          gif: imgPrefix + '/gif/Swirl.gif',
          name: '螺旋'
        },
        {
          tag: 'TangentMotionBlur',
          bg: imgPrefix + '/img/TangentMotionBlur.png',
          dataImg: imgPrefix + '/img/TangentMotionBlur.png',
          gif: imgPrefix + '/gif/TangentMotionBlur.gif',
          name: '运动模糊'
        },
        {
          tag: 'TopBottom',
          bg: imgPrefix + '/img/TopBottom.png',
          dataImg: imgPrefix + '/img/TopBottom.png',
          gif: imgPrefix + '/gif/TopBottom.gif',
          name: '上下运镜'
        },
        {
          tag: 'UndulatingBurnOut',
          bg: imgPrefix + '/img/UndulatingBurnOut.png',
          dataImg: imgPrefix + '/img/UndulatingBurnOut.png',
          gif: imgPrefix + '/gif/UndulatingBurnOut.gif',
          name: '水流蔓延'
        },
        {
          tag: 'Wind',
          bg: imgPrefix + '/img/Wind.png',
          dataImg: imgPrefix + '/img/Wind.png',
          gif: imgPrefix + '/gif/Wind.gif',
          name: '扫风'
        },
        {
          tag: 'WindowSlice',
          bg: imgPrefix + '/img/WindowSlice.png',
          dataImg: imgPrefix + '/img/WindowSlice.png',
          gif: imgPrefix + '/gif/WindowSlice.gif',
          name: '百叶窗'
        },
        {
          tag: 'ZoomInCircles',
          bg: imgPrefix + '/img/ZoomInCircles.png',
          dataImg: imgPrefix + '/img/ZoomInCircles.png',
          gif: imgPrefix + '/gif/ZoomInCircles.gif',
          name: '水波纹'
        }
      ]
    }
  },
  methods: {
    changeEffect() {
      console.log(this.activeValue)
      const item = this.selectList.find(v => v.tag === this.activeValue)
      this.$emit('changeEffect', item)
    }
  },
  created() {},
  mounted() {}
}
</script>

<style scoped lang="scss">
.transition-effects {
  width: 100%;
  height: 100%;
  padding: 10px;
  box-sizing: border-box;
  overflow: auto;
  scrollbar-width: none; /* firefox */
  -ms-overflow-style: none; /* IE 10+ */
  &::-webkit-scrollbar {
    display: none; /* Chrome Safari */
  }
}
.effects-type {
  position: relative;
}
.effects-type-name {
  font-size: 16px;
  color: #9ba3b6;
  padding-left: 15px;
}
.effects-type .boundary {
  position: absolute;
  left: 0px;
  top: 4px;
  width: 3px;
  height: 15px;
  background: #fc4f08;
}
.effects-item-list {
  display: block;
  height: 100%;
  padding: 20px;
  overflow: auto;
}
.effects-list-ul {
  list-style: none;
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-start;
}
.effects-list-ul > li {
  text-align: center;
  cursor: pointer;
  margin-right: 15px;
  margin-bottom: 15px;
  overflow: hidden;
  .effect-bg {
    position: relative;
    width: 144px;
    height: 81px;
    background-size: 100%;
    background-repeat: no-repeat;
    background-position: center;
    border-radius: 6px;
    border: 1px solid #242730;
    overflow: hidden;
    &:hover {
      img {
        display: block;
      }
    }
    img {
      position: absolute;
      left: 0;
      top: 0;
      width: 100%;
      height: 100%;
      object-fit: contain;
      display: none;
    }
  }
  .effect-name {
    color: #80879c;
    font-size: 14px;
    line-height: 28px;
  }
}
</style>
