<template>
  <div class="layout-item">
    <!-- 图片预览区域 -->
    <div class="image-preview">
      <!-- 比例显示区域 -->
      <div class="ratio-display">
        {{ layoutData.aspectRatio || '16:9' }}
      </div>
      <img :src="layoutData.previewImage || defaultImage" :alt="layoutData.name" />
    </div>

    <!-- 底部信息区域 -->
    <div class="bottom-info">
      <div class="layout-name">{{ layoutData.name }}</div>
      <div class="action-buttons">
        <button class="edit-btn" @click="handleEdit" title="编辑">
          <svg-icon icon-class="edit" />
        </button>
        <button class="delete-btn" @click="handleDelete" title="删除">
          <svg-icon icon-class="delete" />
        </button>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'LayoutItem',
  props: {
    layoutData: {
      type: Object,
      default: () => ({
        id: '',
        name: '布局名称1',
        image: '',
        ratio: '16:9'
      })
    }
  },
  data() {
    return {
      defaultImage: 'data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAAoACgDASIAAhEBAxEB/8QAGwAAAQUBAQAAAAAAAAAAAAAABgADBQcIBAL/xAAsEAACAQMDAwMEAQUAAAAAAAABAgMABBEFEiEGMUETUWEHInGBkRQyobHB/8QAGAEAAwEBAAAAAAAAAAAAAAAAAAECAwT/xAAhEQACAgICAgMBAAAAAAAAAAAAAQIRAyESMUFRImFxgf/aAAwDAQACEQMRAD8A3jRRRQAUUUUAFFFFABRRRQAUUUUAf//Z'
    }
  },
  computed: {},
  methods: {
    handleEdit() {
      this.$emit('edit', this.layoutData)
    },
    handleDelete() {
      this.$confirm(`确定要删除布局 "${this.layoutData.name}" 吗？`, '删除确认', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$emit('delete', this.layoutData)
      }).catch(() => {
        // 用户取消删除
      })
    }
  },
  created() {},
  watch: {}
}
</script>

<style lang="scss" scoped>
.layout-item {
  width: 280px;
  background: #1a1a1a;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  display: flex;
  flex-direction: column;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.4);
  }
}

.image-preview {
  width: 100%;
  height: 157px;
  overflow: hidden;
  position: relative;
  flex-shrink: 0;

  img {
    width: 100%;
    height: 100%;
    object-fit: contain;
    transition: transform 0.3s ease;
  }

  &:hover img {
    transform: scale(1.05);
  }
}

.ratio-display {
  position: absolute;
  top: 12px;
  left: 12px;
  color: white;
  font-size: 16px;
  font-weight: 500;
  z-index: 2;
  background: rgba(0, 0, 0, 0.5);
  padding: 2px 6px;
  border-radius: 4px;
  backdrop-filter: blur(5px);
}

.bottom-info {
  height: 37px;
  background: #2E333D;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 12px;
  flex-shrink: 0;
}

.layout-name {
  color: white;
  font-size: 14px;
  font-weight: 500;
  flex: 1;
  @include elliptical();
}

.action-buttons {
  display: flex;
  gap: 6px;

  button {
    width: 24px;
    height: 24px;
    border: none;
    border-radius: 4px;
    background: transparent;
    backdrop-filter: blur(10px);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;

    &:hover {
      background: rgba(255, 255, 255, 0.3);
      transform: scale(1.1);
    }

    .icon-edit,
    .icon-delete {
      font-size: 10px;
      filter: drop-shadow(0 1px 1px rgba(0, 0, 0, 0.5));
    }
  }
  .edit-btn,
  .delete-btn {
    color: #fff;
  }
  .edit-btn:hover {
    color: $color-theme;
  }

  .delete-btn:hover {
    color: $color-theme;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .layout-item {
    width: 240px;
  }

  .image-preview {
    width: 240px;
    height: 135px; // 保持16:9比例
  }

  .ratio-display {
    font-size: 14px;
    top: 8px;
    left: 8px;
    padding: 3px 6px;
  }

  .bottom-info {
    height: 32px;
    padding: 0 8px;
  }

  .layout-name {
    font-size: 12px;
  }

  .action-buttons button {
    width: 20px;
    height: 20px;

    .icon-edit,
    .icon-delete {
      font-size: 8px;
    }
  }
}

@media (max-width: 480px) {
  .layout-item {
    width: 180px;
  }

  .image-preview {
    width: 180px;
    height: 101px; // 保持16:9比例
  }

  .bottom-info {
    height: 28px;
  }
}
</style>
