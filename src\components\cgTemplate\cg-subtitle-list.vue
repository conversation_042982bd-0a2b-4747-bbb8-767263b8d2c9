<template>
  <div class="cg-subtitle-list" :class="{'cg-list-relate': type==='relate'}">
    <div v-if="type !== 'relate'" class="option-area">
      <span class="g-option-btn-1" style="width: 84px" @click="addCd()" >
        <i class="icon el-icon-plus"></i>
        <span>添加</span>
      </span>
    </div>
    <div class="cg-list" :class="{'cg-list-relate': type==='relate'}">
      <div class="empty-box" style="height: 100%" v-if="cgsFromType && !cgsFromType.length">
        <no-data :tips="type === 'relate' ? '暂无关联字幕' : '暂无字幕'"></no-data>
      </div>
      <ul class="cg-container">
        <li class="showCg-item" :class="{'is-vertical': liveRatio < 1,'is-add': checkList.includes(item.id)}" :style="getItemWidth()" v-for="(item, index) in cgsFromType">
          <div class="cg-item" @click.prevent="selectItem(item)">
            <el-checkbox-group v-model="checkList">
              <el-checkbox :label="item.id" class="check-box" v-if="isAdd"></el-checkbox>
              <div class="showCg-preview" v-if="item.type === 'clock'" :class="{'is-vertical': liveRatio < 1}">
                <div class="cg-ratio">{{item.displayWidth + '*' + item.displayHeight}}</div>
                <div class="pos-abs" :style="previewStyle(item)">
                  <div style="position: relative;height: 100%;width: 100%">
                    <div class="pos-abs" :style="clockStyle(item.obj)" :key="item.obj.id">
                      <div class="pos-abs" :style="clockStyleDiv(item.obj)">
                        <div :style="textStyle(item.obj)">
                          <span v-show="item.obj.text === '15:04:05'" style="white-space: nowrap">00:00:00</span>
                          <span v-show="item.obj.text === '2006-01-02'" style="white-space: nowrap">2001-01-01</span>
                          <span v-show="item.obj.text === '2006-01-02 15:04:05'" style="white-space: nowrap">2001-01-01 00:00:00</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="showCg-preview" :class="{'is-vertical': liveRatio < 1}" v-if="item.type === 'timerUp' || item.type === 'timerDown'">
                <div class="cg-ratio">{{item.displayWidth + '*' + item.displayHeight}}</div>
                <div class="pos-abs" :style="timerStyle(item)">
                  <template v-if="!item.obj.box">
                    <span>00</span>
                    <span v-show="item.obj.text.includes('day')" :style="{fontSize: item.obj.fontSize / 5 * 3 + 'px'}">天</span>
                    <span v-show="item.obj.text === 'hour:minute:second'">:</span>
                    <span v-show="item.obj.text !== 'day'">00</span>
                    <span v-show="item.obj.text === 'day hour:minute'" :style="{fontSize: item.obj.fontSize / 5 * 3 + 'px'}">时</span>
                    <span v-show="item.obj.text === 'hour:minute:second'">:</span>
                    <span v-show="item.obj.text !== 'day'">00</span>
                    <span v-show="item.obj.text === 'day hour:minute'" :style="{fontSize: item.obj.fontSize / 5 * 3 + 'px'}">分</span>
                  </template>
                  <template v-else>
                    <span class="hasBorder" :style="{backgroundColor: '#' + item.obj.boxColor}">00</span>
                    <span v-show="item.obj.text.includes('day')" :style="{fontSize: item.obj.fontSize / 5 * 3 + 'px'}">天</span>
                    <span v-show="item.obj.text === 'hour:minute:second'">:</span>
                    <span class="hasBorder" v-show="item.obj.text !== 'day'" :style="{backgroundColor: '#' + item.obj.boxColor}">00</span>
                    <span v-show="item.obj.text === 'day hour:minute'" :style="{fontSize: item.obj.fontSize / 5 * 3 + 'px'}">时</span>
                    <span v-show="item.obj.text === 'hour:minute:second'">:</span>
                    <span class="hasBorder" v-show="item.obj.text !== 'day'" :style="{backgroundColor: '#' + item.obj.boxColor}">00</span>
                    <span v-show="item.obj.text === 'day hour:minute'" :style="{fontSize: item.obj.fontSize / 5 * 3 + 'px'}">分</span>
                  </template>
                </div>
              </div>
              <div class="showCg-preview" v-if="item.type === 'weather'" :class="{'is-vertical': liveRatio < 1}">
                <div class="cg-ratio">{{item.displayWidth + '*' + item.displayHeight}}</div>
                <div class="pos-abs" :style="weatherStyle(item)">
                  <weather :currentCgInfo="item" class="preview-weather"></weather>
                </div>
              </div>
              <div class="showCg-preview" v-else-if="item.type === 'movingSubtitle'" :class="{'is-vertical': liveRatio < 1}">
                <div class="cg-ratio">{{item.displayWidth + '*' + item.displayHeight}}</div>
                <div class="pos-abs movingSubtitle" :style="previewStyle(item)">
                  <div style="position: relative;height: 100%;width: 100%">
                    <div class="pos-abs" :style="subTitleStyle(item.obj)" :key="item.obj.id">
                      <div class="pos-abs" :style="movingSubtitleStyleDiv(item.obj)">{{item.obj.text}}</div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="showCg-preview" v-else-if="item.type === 'subtitle'" :class="{'is-vertical': liveRatio < 1}">
                <div class="cg-ratio">{{item.displayWidth + '*' + item.displayHeight}}</div>
                <div class="pos-abs" :style="previewStyle(item)">
                  <div style="position: relative;height: 100%;width: 100%;overflow:hidden;" v-if="item.obj">
                    <template v-for="val in item.obj.layers">
                      <div class="pos-abs" :style="clockStyle(val)" v-if="val.type === 'clock'" :key="val.id">
                        <div class="pos-abs" :style="clockStyleDiv(val)">
                          <div :style="textStyle(val)" v-html="val.text"></div>
                        </div>
                      </div>
                      <img class="pos-abs" :src="$_getPicture(val.url)" :style="imgStyle(val)" alt="" :key="val.id" v-if="val.type === 'logo'">
                      <div class="pos-abs" :style="subTitleStyle(val)" :key="val.id" v-if="val.type === 'subtitle'">
                        <div class="pos-abs" :style="subTitleStyleDiv(val)">
                          <div :style="textStyle(val)" v-html="val.text"></div>
                        </div>
                      </div>
                    </template>
                  </div>
                </div>
              </div>
              <div class="showCg-preview" v-else-if="item.type === 'gif'" :class="{'is-vertical': liveRatio < 1}">
                <div class="cg-ratio">{{item.displayWidth + '*' + item.displayHeight}}</div>
                <div class="pos-abs" :style="previewStyle(item)">
                  <div style="position: relative;height: 100%;width: 100%;overflow:hidden;" v-if="item.obj">
                    <img class="pos-abs" :src="$_getPicture(item.obj.url)" :style="gifStyle(item)">
                  </div>
                </div>
              </div>
            </el-checkbox-group>
            <div class="showCg-title" :title="item.name">
              {{item.name}}
            </div>
            <div class="showCg-option">
              <el-button type="danger" class="my-operate-btn" :class="{'is-vertical': liveRatio < 1}" style="margin-right: 10px" @click="editCg(item)" v-if="!isAdd">编辑</el-button>
              <el-button type="danger" class="my-operate-btn" @click.stop="deleteItem(item, index)" v-if="!isAdd && type !== 'relate'">删除</el-button>
              <el-button type="danger" class="my-operate-btn" @click.stop="cancelItem(item, index)" v-if="type === 'relate' && !isAdd">取消</el-button>
            </div>
          </div>
        </li>
        <li class="empty-box" :style="getItemWidth()"></li>
        <li class="empty-box" :style="getItemWidth()"></li>
        <li class="empty-box" :style="getItemWidth()"></li>
        <li class="empty-box" :style="getItemWidth()"></li>
        <li class="empty-box" :style="getItemWidth()"></li>
      </ul>
    </div>
    <subtitle-cg-dialog @updateCg="updateCg" :cgInfo="cgInfo" :isEditCg="isEditCg" v-if="showSubtitleDialog" @close="showSubtitleDialog=false" :usedType="1"></subtitle-cg-dialog>
  </div>
</template>

<script>
  import {mapActions, mapGetters, mapMutations} from 'vuex'
  import subtitleCgDialog from './subtitle-cg-dialog'
  import liveApi from 'api/live'
  import cloudApi from 'api/cloud'
  import weather from 'components/cgTemplate/weather'

  let scale = 0
  let cgItemHeight = 144
  export default {
    name: 'cg-subtitle-list',
    components: {subtitleCgDialog, weather},
    props: {
      currentProgramItem: {
        type: Object,
        default: () => {}
      },
      programType: {
        type: String,
        default: ''
      },
      scheduleId: {
        type: Number,
        default: 0
      },
      manageId: {
        type: Number,
        default: 0
      },
      type: {
        type: String,
        default: ''
      },
      isAdd: {
        type: Boolean,
        default: false
      },
      currentProgram: {
        type: String,
        default: ''
      }
    },
    computed: {
      ...mapGetters([
        'cgs',
        'liveWidth',
        'liveHeight',
        'liveRatio',
        'programs'
      ])
    },
    data () {
      return {
        showSubtitleDialog: false,
        isEditCg: false,
        cgInfo: null,
        checkList: [],
        cgsFromType: []
      }
    },
    methods: {
      selectItem (item) {
        if (!this.isAdd) {
          return
        }
        let index = this.checkList.findIndex(v => v === item.id)
        if (index > -1) {
          this.checkList.splice(index, 1)
        } else {
          this.checkList.push(item.id)
        }
      },
      updateCg () {
        this.getInitAll()
      },
      addRelateSubtitle () {
        if (this.manageId > 0) {
          let editProgram = this.currentProgramItem
          editProgram.cgIds = this.$_deepCopy(this.checkList)
          let param = {
            id: this.manageId,
            program: JSON.stringify(editProgram)
          }
          cloudApi.setProgram(param, () => {
            this.$bus.$emit('refreshProgramManage')
            this.$emit('updateRelate')
            this.$emit('close')
          })
        } else if (this.scheduleId > 0) {
          let editProgram = this.currentProgramItem
          editProgram.cgIds = this.$_deepCopy(this.checkList)
          let param = {
            id: this.scheduleId,
            program: JSON.stringify(editProgram)
          }
          cloudApi.uploadProgramArrangementById(param, () => {
            this.$bus.$emit('refreshProgramManage')
            this.$emit('updateRelate')
            this.$emit('close')
          })
        } else {
          let editProgramIndex = this.programs.findIndex(item => {
            return item.id === this.currentProgram
          })
          let editProgram = this.$_deepCopy(this.programs[editProgramIndex])
          editProgram.cgIds = this.$_deepCopy(this.checkList)
          let param = {
            programType: this.programType,
            program: editProgram
          }
          this.modProgram(param).then(() => {
            this.$emit('updateRelate')
            this.$emit('close')
          })
        }
      },
      imgStyle (item) {
        return {
          left: item.dx * scale + 'px',
          top: item.dy * scale + 'px',
          opacity: item.refract ? item.refract / 100 : 1,
          width: item.width * scale + 'px',
          height: item.height * scale + 'px'
        }
      },
      gifStyle (item) {
        return {
          width: item.width * scale + 'px',
          height: item.height * scale + 'px'
        }
      },
      cancelItem (item) {
        if (this.manageId > 0) {
          let editProgram = this.currentProgramItem
          let editIndex = editProgram.cgIds.findIndex(v => v === item.id)
          editProgram.cgIds.splice(editIndex, 1)
          let param = {
            id: this.manageId,
            program: JSON.stringify(editProgram)
          }
          cloudApi.setProgram(param, () => {
            this.cgsFromType.splice(editIndex, 1)
            this.$bus.$emit('refreshProgramManage')
          })
        } else if (this.scheduleId > 0) {
          let editProgram = this.currentProgramItem
          let editIndex = editProgram.cgIds.findIndex(v => v === item.id)
          editProgram.cgIds.splice(editIndex, 1)
          let param = {
            id: this.scheduleId,
            program: JSON.stringify(editProgram)
          }
          cloudApi.uploadProgramArrangementById(param, () => {
            this.cgsFromType.splice(editIndex, 1)
            this.$bus.$emit('refreshProgramManage')
          })
        } else {
          let editProgramIndex = this.programs.findIndex(v => {
            return v.id === this.currentProgram
          })
          let editProgram = this.$_deepCopy(this.programs[editProgramIndex])
          let editIndex = editProgram.cgIds.findIndex(v => v === item.id)
          editProgram.cgIds.splice(editIndex, 1)
          let param = {
            programType: this.programType,
            program: editProgram
          }
          this.modProgram(param).then(() => {
            this.cgsFromType.splice(editIndex, 1)
          })
        }
      },
      deleteItem (item, index) {
        this.$confirm('删除字幕将导致已关联的字幕文件失效，请确认是否删除?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          let param = {id: item.id}
          liveApi.removeCg(param, () => {
            this.setCgs({index: index, deleteNum: 1, id: item.id})
            this.getInitAll()
            this.getPrograms()
            this.$message.success('删除成功')
          }, (code, msg) => {
            this.$message.error(msg)
          })
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
      },
      getBoxColor (backgroundColor, bgOpacity) {
        return backgroundColor.substr(1, 6) + this.$_parseOpacity(bgOpacity)
      },
      indexSort (prop) {
        return function (a, b) {
          let index1 = a[prop]
          let index2 = b[prop]
          return index1 - index2
        }
      },
      getLayers (tempObj) {
        let scale = 640 / this.liveWidth
        return Object.values(tempObj.list).map(item => {
          let baseObj = {
            dx: item.left / scale,
            dy: item.top / scale,
            height: item.h / scale,
            width: item.w / scale,
            refract: 100 / scale,
            zIndex: item.zIndex
          }
          if (item.fontFamily) {
            return Object.assign(baseObj, {
              boldItalic: '',
              boxColor: this.getBoxColor(item.bgColor, item.bgOpacity),
              color: item.color,
              font: item.fontFamily,
              position: this.$_getPosition(item.horizon, item.vertical),
              size: parseInt(item.size / scale),
              text: item.text,
              type: 'subtitle'
            })
          } else {
            return Object.assign(baseObj, {
              type: 'logo',
              url: item.url
            })
          }
        })
      },
      moveTemplateVal (tempObj) {
        let scale = 640 / this.liveWidth
        let layers = this.getLayers(tempObj)
        return {
          dx: tempObj.container.left / scale,
          dy: tempObj.container.top / scale,
          height: tempObj.container.h / scale,
          id: parseInt(Math.random() * 1000000),
          loop: Number(tempObj.container.animate.loop),
          obj: layers[0],
          pgmFlag: false,
          pvwFlag: false,
          startType: 0,
          type: 'movingSubtitle',
          width: tempObj.container.w / scale
        }
      },
      subtitleTemplateVal (tempObj) {
        let scale = 640 / this.liveWidth
        let layers = this.getLayers(tempObj).sort(this.indexSort('zIndex'))
        return {
          dx: tempObj.container.left / scale,
          dy: tempObj.container.top / scale,
          height: tempObj.container.h / scale,
          id: parseInt(Math.random() * 1000000),
          loop: 0,
          obj: {
            layers: layers
          },
          pgmFlag: false,
          pvwFlag: false,
          startType: 0,
          type: 'subtitle',
          width: tempObj.container.w / scale
        }
      },
      editCgTemplate (cgTemplateItem) {
        let cgInfo
        if (Number(cgTemplateItem.contentObj.cg640.container.animate.start) === 1) {
          cgInfo = this.moveTemplateVal(cgTemplateItem.contentObj.cg640)
        } else {
          cgInfo = this.subtitleTemplateVal(cgTemplateItem.contentObj.cg640)
        }
        this.addCd(cgInfo)
      },
      addCd (cgInfo) {
        this.isEditCg = false
        this.cgInfo = cgInfo
        this.showSubtitleDialog = true
      },
      editCg (item) {
        this.isEditCg = true
        this.cgInfo = item
        this.showSubtitleDialog = true
      },
      getItemWidth () {
        return {width: cgItemHeight * this.liveRatio + 'px', flexBasis: cgItemHeight * this.liveRatio + 'px'}
      },
      previewStyle (item) {
        return {
          width: item.width * scale + 'px',
          height: item.height * scale + 'px',
          left: item.dx * scale + 'px',
          top: item.dy * scale + 'px',
          position: 'absolute'
        }
      },
      timerStyle (item) {
        let styles = {
          zoom: scale,
          position: 'absolute',
          left: item.dx + 'px',
          fontSize: item.obj.fontSize + 'px',
          top: item.dy + 'px',
          width: item.width + 'px',
          height: item.height + 'px',
          color: '#' + item.obj.fontColor
        }
        return styles
      },
      weatherStyle (item) {
        let styles = {
          zoom: scale,
          position: 'absolute',
          fontSize: item.obj.fontSize + 'px',
          color: '#' + item.obj.fontColor,
          transform: `scale(${item.obj.scale})`
        }

        if (item.obj.coordinates === 2) {
          styles.left = item.dx + 'px'
          styles.top = item.dy + 'px'
          styles.transformOrigin = 'top left'
        } else if (item.obj.coordinates === 0) {
          styles.right = (item.displayWidth - item.width * item.obj.scale) - item.dx + 'px'
          styles.top = item.dy + 'px'
          styles.transformOrigin = 'top right'
        } else if (item.obj.coordinates === 1) {
          styles.right = (item.displayWidth - item.width * item.obj.scale) - item.dx + 'px'
          styles.bottom = (item.displayHeight - item.height * item.obj.scale) - item.dy + 'px'
          styles.transformOrigin = 'bottom right'
        } else if (item.obj.coordinates === 3) {
          styles.left = item.dx + 'px',
          styles.bottom = (item.displayHeight - item.height * item.obj.scale) - item.dy + 'px'
          styles.transformOrigin = 'bottom left'
        }
        return styles
      },
      clockStyle (item) {
        let styles = {
          left: item.dx * scale + 'px',
          fontSize: item.size + 'px',
          top: item.dy * scale + 'px',
          opacity: item.refract / 100,
          width: item.width * scale + 'px',
          height: item.height * scale + 'px',
          color: '#' + item.color,
          fontFamily: item.font
        }
        let boldItalic = this.getBoldItalic(item.boldItalic)
        return Object.assign({}, styles, boldItalic)
      },
      clockStyleDiv (item) {
        return {
          transform: 'scale(' + scale + ')',
          width: item.width + 'px',
          height: item.height + 'px',
          marginLeft: '-' + item.width * (1 - scale) / 2 + 'px',
          marginTop: '-' + item.height * (1 - scale) / 2 + 'px'
        }
      },
      getBoldItalic (str) {
        let styles = {}
        if (str.indexOf('B') >= 0) {
          styles = {fontWeight: 'bold'}
        }
        if (str.indexOf('I') >= 0) {
          styles = {fontStyle: 'italic'}
        }
        return styles
      },
      subTitleStyle (item) {
        let styles = {
          left: item.dx * scale + 'px',
          fontSize: item.size + 'px',
          top: item.dy * scale + 'px',
          opacity: item.refract / 100,
          width: item.width * scale + 'px',
          height: item.height * scale + 'px',
          color: '#' + item.color,
          fontFamily: item.font,
          backgroundColor: this.$_colorRgba(item.boxColor)
        }
        let boldItalic = this.getBoldItalic(item.boldItalic)
        return Object.assign({}, styles, boldItalic)
      },
      textStyle (item) {
        let styles = {
          display: 'table-cell',
          wordBreak: 'break-all',
          width: item.width + 'px',
          height: item.height + 'px'
        }
        let flexStyle = this.getAlignStyle(item.position)
        return Object.assign({}, styles, flexStyle)
      },
      getVerticalAlign (position) {
        if ([1, 2, 3].includes(position)) return 'top'
        if ([4, 5, 6].includes(position)) return 'middle'
        return 'bottom'
      },
      getTextAlign (position) {
        if ([1, 4, 7].includes(position)) return 'left'
        if ([2, 5, 8].includes(position)) return 'center'
        return 'right'
      },
      getAlignStyle (position) {
        let style = {}
        style.verticalAlign = this.getVerticalAlign(position)
        style.textAlign = this.getTextAlign(position)
        return style
      },
      movingSubtitleStyleDiv (item) {
        return {
          transform: 'scale(' + scale + ')',
          width: item.width + 'px',
          height: item.height + 'px',
          marginLeft: '-' + item.width * (1 - scale) / 2 + 'px',
          marginTop: '-' + item.height * (1 - scale) / 2 + 'px',
          whiteSpace: 'nowrap'
        }
      },
      subTitleStyleDiv (item) {
        return {
          transform: 'scale(' + scale + ')',
          width: item.width + 'px',
          height: item.height + 'px',
          marginLeft: '-' + item.width * (1 - scale) / 2 + 'px',
          marginTop: '-' + item.height * (1 - scale) / 2 + 'px'
        }
      },
      getInit () {
        this.cgsFromType = []
        let cgs = this.cgs.filter(item => {
          return item.usedType === 1
        })
        let program
        if (this.manageId > 0) {
          program = this.currentProgramItem
        } else if (this.scheduleId > 0) {
          program = this.currentProgramItem
        } else {
          let programIndex = this.programs.findIndex(item => {
            return item.id === this.currentProgram
          })
          program = this.$_deepCopy(this.programs[programIndex])
        }
        if (this.type === 'relate' && !this.isAdd) {
          program.cgIds && program.cgIds.forEach(item => {
            let cg = this.cgs.find(v => {
              return v.id === item
            })
            cg && this.cgsFromType.push(cg)
          })
        } else {
          this.cgsFromType = this.$_deepCopy(cgs)
          if (program.cgIds) {
            this.checkList = this.$_deepCopy(program.cgIds)
          }
        }
      },
      getInitAll () {
        if (this.type === 'relate') {
          this.getInit()
        } else {
          let cgs = this.cgs.filter(item => {
            return item.usedType === 1
          })
          this.cgsFromType = this.$_deepCopy(cgs)
        }
      },
      ...mapMutations({
        setCgs: 'SET_CGS'
      }),
      ...mapActions(['modProgram', 'getPrograms'])
    },
    created () {
      this.getInitAll()
      if (this.liveRatio < 1) {
        cgItemHeight = 200
      } else {
        cgItemHeight = 144
        if (this.type === 'relate') {
          cgItemHeight = 126
        }
      }
      scale = cgItemHeight / this.liveHeight
    }
  }
</script>

<style scoped lang="scss">
  .cg-subtitle-list {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    padding: 20px;
    box-sizing: border-box;
    height: 100%;
    width: 100%;
    &.cg-list-relate {
      padding: 0;
    }
    .tab-title {
      width: 30%;
      flex: 0 0 40px;
      height: 40px;
      margin-left: 20px;
    }
    .option-area {
      position: absolute;
      right: 15px;
      top: 15px;
      z-index: 1;
    }
    .cg-list {
      height: 100%;
      overflow: hidden;
      background-color: #232631;
      &.cg-list-relate {
        background-color: #373D4A!important;
        height: 100%;
      }
      .cg-container {
        display: flex;
        justify-content: space-around;
        flex-wrap: wrap;
        position: relative;
        max-height: 100%;
        overflow-y: auto;
        overflow-x: hidden;
        padding-top: 20px;
      }
      .empty-box {
        height: 0;
        margin: 0 10px;
        padding: 10px 10px;
      }
      .showCg-item {
        margin: 10px 10px;
        padding: 10px 10px;
        height: 174px;
        &.is-vertical {
          height: 230px;
        }
        &:hover {
          background-color: #4b5160;
        }
        &.is-add {
          background-color: #FC4F08;
          .showCg-title {
            color: #ffffff!important;
          }
        }
        .cg-item {
          position: relative;
          cursor: pointer;
          .check-box {
            position: absolute;
            left: 5px;
            top: 0;
            ::v-deep .el-checkbox__input {
              z-index: 5;
            }
          }
          .ai-img {
            position: relative;
            top: 3px;
            width: 100%;
            height: 100%;
          }
          &:hover {
            /*.closeCgicon {*/
            /*  display: block;*/
            /*}*/
            .showCg-option {
              display: block;
            }
          }
          .closeCgicon {
            display: none;
            position: absolute;
            width: 25px;
            height: 25px;
            line-height: 25px;
            right: 0;
            top: 0;
            text-align: center;
            z-index: 1;
            cursor: pointer;
            color: #fff;
            background-color: #FF2A2A;
            font-size: 16px;
          }
          .showCg-preview {
            height: 144px;
            position: relative;
            background-image: url(./img/blank.png);
            overflow: hidden;
            .cg-ratio {
              position: absolute;
              bottom: 5px;
              left: 5px;
              padding: 2px 5px;
              border-radius: 10px;
              z-index: 200;
              background-color: rgba(0,0,0, .4);
              font-size: 14px;
              color: #B5BBCA;
            }
            &.is-vertical {
              height: 200px!important;
            }
            .pos-abs {
              position: absolute;
              &.movingSubtitle {
                left: 0 !important;
                width: 100% !important;
                text-align: center;
                .pos-abs {
                  width: 100% !important;
                  margin-left: 0 !important;
                }
              }
            }
          }
          .showCg-title {
            padding: 13px 0;
            text-align: center;
            color: #93A0BA;
            @include elliptical();
            font-size: 16px;
          }
          .showCg-option {
            display: none;
            position: absolute;
            top: -10px;
            left: -10px;
            width: calc(100% + 20px);
            text-align: center;
            padding-top: 30%;
            box-sizing: border-box;
            .my-operate-btn {
              margin-bottom: 10px;
              font-size: 14px;
              &.is-vertical {
                margin-right: 0!important;
              }
            }
          }
        }
      }
    }
  }
  .hasBorder {
    display: inline-block;
    padding: 0px 8px;
    border-radius: 2px;
  }
  .preview-weather /deep/ {
     .lastThreeDays, .realTime {
      border: 0px!important;
    }
  }
</style>
